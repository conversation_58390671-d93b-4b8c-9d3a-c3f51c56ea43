sudo apt-get update
sudo apt upgrade -y
sudo apt install python3-pip python3-dev
sudo apt install python3-virtualenv
sudo apt-get install ffmpeg
# sudo pip3 install virtualenv

mkdir ~/vido_tide

cd ~/vido_tide
# virtualenv .venv
python3 -m venv .venv
source .venv/bin/activate
pip install -r requirements.txt

source .venv/bin/activate && python engagement_app/tests/test_sample_data_integration.py

source .venv/bin/activate && python engagement_app/scripts/run_pipeline.py
