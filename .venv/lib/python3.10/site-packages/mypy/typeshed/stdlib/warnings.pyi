import re
import sys
from _warnings import warn as warn, warn_explicit as warn_explicit
from collections.abc import Sequence
from types import ModuleType, TracebackType
from typing import Any, Generic, Literal, TextIO, overload
from typing_extensions import LiteralString, TypeAlias, TypeVar

__all__ = [
    "warn",
    "warn_explicit",
    "showwarning",
    "formatwarning",
    "filterwarnings",
    "simplefilter",
    "resetwarnings",
    "catch_warnings",
]

if sys.version_info >= (3, 13):
    __all__ += ["deprecated"]

_T = TypeVar("_T")
_W_co = TypeVar("_W_co", bound=list[WarningMessage] | None, default=list[WarningMessage] | None, covariant=True)

if sys.version_info >= (3, 14):
    _ActionKind: TypeAlias = Literal["default", "error", "ignore", "always", "module", "once"]
else:
    _ActionKind: TypeAlias = Literal["default", "error", "ignore", "always", "all", "module", "once"]
filters: Sequence[tuple[str, re.Pattern[str] | None, type[Warning], re.Pattern[str] | None, int]]  # undocumented, do not mutate

def showwarning(
    message: Warning | str,
    category: type[Warning],
    filename: str,
    lineno: int,
    file: TextIO | None = None,
    line: str | None = None,
) -> None: ...
def formatwarning(
    message: Warning | str, category: type[Warning], filename: str, lineno: int, line: str | None = None
) -> str: ...
def filterwarnings(
    action: _ActionKind, message: str = "", category: type[Warning] = ..., module: str = "", lineno: int = 0, append: bool = False
) -> None: ...
def simplefilter(action: _ActionKind, category: type[Warning] = ..., lineno: int = 0, append: bool = False) -> None: ...
def resetwarnings() -> None: ...

class _OptionError(Exception): ...

class WarningMessage:
    message: Warning | str
    category: type[Warning]
    filename: str
    lineno: int
    file: TextIO | None
    line: str | None
    source: Any | None
    def __init__(
        self,
        message: Warning | str,
        category: type[Warning],
        filename: str,
        lineno: int,
        file: TextIO | None = None,
        line: str | None = None,
        source: Any | None = None,
    ) -> None: ...

class catch_warnings(Generic[_W_co]):
    if sys.version_info >= (3, 11):
        @overload
        def __init__(
            self: catch_warnings[None],
            *,
            record: Literal[False] = False,
            module: ModuleType | None = None,
            action: _ActionKind | None = None,
            category: type[Warning] = ...,
            lineno: int = 0,
            append: bool = False,
        ) -> None: ...
        @overload
        def __init__(
            self: catch_warnings[list[WarningMessage]],
            *,
            record: Literal[True],
            module: ModuleType | None = None,
            action: _ActionKind | None = None,
            category: type[Warning] = ...,
            lineno: int = 0,
            append: bool = False,
        ) -> None: ...
        @overload
        def __init__(
            self,
            *,
            record: bool,
            module: ModuleType | None = None,
            action: _ActionKind | None = None,
            category: type[Warning] = ...,
            lineno: int = 0,
            append: bool = False,
        ) -> None: ...
    else:
        @overload
        def __init__(self: catch_warnings[None], *, record: Literal[False] = False, module: ModuleType | None = None) -> None: ...
        @overload
        def __init__(
            self: catch_warnings[list[WarningMessage]], *, record: Literal[True], module: ModuleType | None = None
        ) -> None: ...
        @overload
        def __init__(self, *, record: bool, module: ModuleType | None = None) -> None: ...

    def __enter__(self) -> _W_co: ...
    def __exit__(
        self, exc_type: type[BaseException] | None, exc_val: BaseException | None, exc_tb: TracebackType | None
    ) -> None: ...

if sys.version_info >= (3, 13):
    class deprecated:
        message: LiteralString
        category: type[Warning] | None
        stacklevel: int
        def __init__(self, message: LiteralString, /, *, category: type[Warning] | None = ..., stacklevel: int = 1) -> None: ...
        def __call__(self, arg: _T, /) -> _T: ...
