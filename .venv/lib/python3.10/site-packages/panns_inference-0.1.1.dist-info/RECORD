panns_inference-0.1.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
panns_inference-0.1.1.dist-info/LICENSE.MIT,sha256=6_aAqIFvFbNnmB9I1GE2mphIopyc5kTCSbvLqkgxoB8,1099
panns_inference-0.1.1.dist-info/METADATA,sha256=uzUWZq8NVITicC1HaM7T0yQAchkqEu3BNzfEAQPlmok,2364
panns_inference-0.1.1.dist-info/RECORD,,
panns_inference-0.1.1.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
panns_inference-0.1.1.dist-info/WHEEL,sha256=pkctZYzUS4AYVn6dJ-7367OJZivF2e8RA9b_ZBjif18,92
panns_inference-0.1.1.dist-info/top_level.txt,sha256=7hhCGDjTr5mtX4tcYrIx3jv5clUN2aLwLhrr7PH3nK8,16
panns_inference/__init__.py,sha256=KCn5kcgDUxhHMDx1-BQOvDqu0OZ641pi92BxkQ-md6k,106
panns_inference/__pycache__/__init__.cpython-310.pyc,,
panns_inference/__pycache__/config.cpython-310.pyc,,
panns_inference/__pycache__/inference.cpython-310.pyc,,
panns_inference/__pycache__/models.cpython-310.pyc,,
panns_inference/__pycache__/pytorch_utils.cpython-310.pyc,,
panns_inference/config.py,sha256=FRCgoEpos-F5YX9-AdnBTfUybhILTASpgQkh9Vb-uig,1000
panns_inference/inference.py,sha256=8MM0OrXRBhb45EzkNqtSUp99hW4RvGlT9oi1SF-G0oo,4657
panns_inference/models.py,sha256=kfmeh5vULZOFD4GZFAht0RZ_EGfyZVCgVh8lxO76FX8,10148
panns_inference/pytorch_utils.py,sha256=ipfc7E2N-h8865PekJyP_niCg1CrAR8YwlGGWeaHpes,2657
