{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/drive": {"description": "See, edit, create, and delete all of your Google Drive files"}, "https://www.googleapis.com/auth/drive.appdata": {"description": "See, create, and delete its own configuration data in your Google Drive"}, "https://www.googleapis.com/auth/drive.apps.readonly": {"description": "View your Google Drive apps"}, "https://www.googleapis.com/auth/drive.file": {"description": "See, edit, create, and delete only the specific Google Drive files you use with this app"}, "https://www.googleapis.com/auth/drive.meet.readonly": {"description": "See and download your Google Drive files that were created or edited by Google Meet."}, "https://www.googleapis.com/auth/drive.metadata": {"description": "View and manage metadata of files in your Google Drive"}, "https://www.googleapis.com/auth/drive.metadata.readonly": {"description": "See information about your Google Drive files"}, "https://www.googleapis.com/auth/drive.photos.readonly": {"description": "View the photos, videos and albums in your Google Photos"}, "https://www.googleapis.com/auth/drive.readonly": {"description": "See and download all your Google Drive files"}, "https://www.googleapis.com/auth/drive.scripts": {"description": "Modify your Google Apps Script scripts' behavior"}}}}, "basePath": "/drive/v2/", "baseUrl": "https://www.googleapis.com/drive/v2/", "batchPath": "batch/drive/v2", "description": "The Google Drive API allows clients to access resources from Google Drive.", "discoveryVersion": "v1", "documentationLink": "https://developers.google.com/workspace/drive/", "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "drive:v2", "kind": "discovery#restDescription", "mtlsRootUrl": "https://www.mtls.googleapis.com/", "name": "drive", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"about": {"methods": {"get": {"description": "Gets the information about the current user along with Drive API settings", "flatPath": "about", "httpMethod": "GET", "id": "drive.about.get", "parameterOrder": [], "parameters": {"includeSubscribed": {"default": "true", "description": "Whether to count changes outside the My Drive hierarchy. When set to false, changes to files such as those in the Application Data folder or shared files which have not been added to My Drive will be omitted from the `maxChangeIdCount`.", "location": "query", "type": "boolean"}, "maxChangeIdCount": {"default": "1", "description": "Maximum number of remaining change IDs to count", "format": "int64", "location": "query", "type": "string"}, "startChangeId": {"description": "Change ID to start counting from when calculating number of remaining change IDs", "format": "int64", "location": "query", "type": "string"}}, "path": "about", "response": {"$ref": "About"}, "scopes": ["https://www.googleapis.com/auth/drive", "https://www.googleapis.com/auth/drive.appdata", "https://www.googleapis.com/auth/drive.file", "https://www.googleapis.com/auth/drive.metadata", "https://www.googleapis.com/auth/drive.metadata.readonly", "https://www.googleapis.com/auth/drive.photos.readonly", "https://www.googleapis.com/auth/drive.readonly"]}}}, "apps": {"methods": {"get": {"description": "Gets a specific app.", "flatPath": "apps/{appId}", "httpMethod": "GET", "id": "drive.apps.get", "parameterOrder": ["appId"], "parameters": {"appId": {"description": "The ID of the app.", "location": "path", "required": true, "type": "string"}}, "path": "apps/{appId}", "response": {"$ref": "App"}, "scopes": ["https://www.googleapis.com/auth/drive", "https://www.googleapis.com/auth/drive.appdata", "https://www.googleapis.com/auth/drive.apps.readonly", "https://www.googleapis.com/auth/drive.file", "https://www.googleapis.com/auth/drive.metadata", "https://www.googleapis.com/auth/drive.metadata.readonly", "https://www.googleapis.com/auth/drive.readonly"]}, "list": {"description": "Lists a user's installed apps.", "flatPath": "apps", "httpMethod": "GET", "id": "drive.apps.list", "parameterOrder": [], "parameters": {"appFilterExtensions": {"default": "", "description": "A comma-separated list of file extensions for open with filtering. All apps within the given app query scope which can open any of the given file extensions will be included in the response. If `appFilterMimeTypes` are provided as well, the result is a union of the two resulting app lists.", "location": "query", "type": "string"}, "appFilterMimeTypes": {"default": "", "description": "A comma-separated list of MIME types for open with filtering. All apps within the given app query scope which can open any of the given MIME types will be included in the response. If `appFilterExtensions` are provided as well, the result is a union of the two resulting app lists.", "location": "query", "type": "string"}, "languageCode": {"description": "A language or locale code, as defined by BCP 47, with some extensions from Unicode's LDML format (http://www.unicode.org/reports/tr35/).", "location": "query", "type": "string"}}, "path": "apps", "response": {"$ref": "AppList"}, "scopes": ["https://www.googleapis.com/auth/drive.apps.readonly"]}}}, "changes": {"methods": {"get": {"description": "Deprecated: Use `changes.getStartPageToken` and `changes.list` to retrieve recent changes.", "flatPath": "changes/{changeId}", "httpMethod": "GET", "id": "drive.changes.get", "parameterOrder": ["changeId"], "parameters": {"changeId": {"description": "The ID of the change.", "location": "path", "required": true, "type": "string"}, "driveId": {"description": "The shared drive from which the change will be returned.", "location": "query", "type": "string"}, "supportsAllDrives": {"default": "false", "description": "Whether the requesting application supports both My Drives and shared drives.", "location": "query", "type": "boolean"}, "supportsTeamDrives": {"default": "false", "deprecated": true, "description": "Deprecated: Use `supportsAllDrives` instead.", "location": "query", "type": "boolean"}, "teamDriveId": {"deprecated": true, "description": "Deprecated: Use `driveId` instead.", "location": "query", "type": "string"}}, "path": "changes/{changeId}", "response": {"$ref": "Change"}, "scopes": ["https://www.googleapis.com/auth/drive", "https://www.googleapis.com/auth/drive.appdata", "https://www.googleapis.com/auth/drive.apps.readonly", "https://www.googleapis.com/auth/drive.file", "https://www.googleapis.com/auth/drive.meet.readonly", "https://www.googleapis.com/auth/drive.metadata", "https://www.googleapis.com/auth/drive.metadata.readonly", "https://www.googleapis.com/auth/drive.photos.readonly", "https://www.googleapis.com/auth/drive.readonly"]}, "getStartPageToken": {"description": "Gets the starting pageToken for listing future changes.", "flatPath": "changes/startPageToken", "httpMethod": "GET", "id": "drive.changes.getStartPageToken", "parameterOrder": [], "parameters": {"driveId": {"description": "The ID of the shared drive for which the starting pageToken for listing future changes from that shared drive will be returned.", "location": "query", "type": "string"}, "supportsAllDrives": {"default": "false", "description": "Whether the requesting application supports both My Drives and shared drives.", "location": "query", "type": "boolean"}, "supportsTeamDrives": {"default": "false", "deprecated": true, "description": "Deprecated: Use `supportsAllDrives` instead.", "location": "query", "type": "boolean"}, "teamDriveId": {"deprecated": true, "description": "Deprecated: Use `driveId` instead.", "location": "query", "type": "string"}}, "path": "changes/startPageToken", "response": {"$ref": "StartPageToken"}, "scopes": ["https://www.googleapis.com/auth/drive", "https://www.googleapis.com/auth/drive.appdata", "https://www.googleapis.com/auth/drive.apps.readonly", "https://www.googleapis.com/auth/drive.file", "https://www.googleapis.com/auth/drive.meet.readonly", "https://www.googleapis.com/auth/drive.metadata", "https://www.googleapis.com/auth/drive.metadata.readonly", "https://www.googleapis.com/auth/drive.photos.readonly", "https://www.googleapis.com/auth/drive.readonly"]}, "list": {"description": "Lists the changes for a user or shared drive.", "flatPath": "changes", "httpMethod": "GET", "id": "drive.changes.list", "parameterOrder": [], "parameters": {"driveId": {"description": "The shared drive from which changes will be returned. If specified the change IDs will be reflective of the shared drive; use the combined drive ID and change ID as an identifier.", "location": "query", "type": "string"}, "includeCorpusRemovals": {"default": "false", "description": "Whether changes should include the file resource if the file is still accessible by the user at the time of the request, even when a file was removed from the list of changes and there will be no further change entries for this file.", "location": "query", "type": "boolean"}, "includeDeleted": {"default": "true", "description": "Whether to include changes indicating that items have been removed from the list of changes, for example by deletion or loss of access.", "location": "query", "type": "boolean"}, "includeItemsFromAllDrives": {"default": "false", "description": "Whether both My Drive and shared drive items should be included in results.", "location": "query", "type": "boolean"}, "includeLabels": {"description": "A comma-separated list of IDs of labels to include in the `labelInfo` part of the response.", "location": "query", "type": "string"}, "includePermissionsForView": {"description": "Specifies which additional view's permissions to include in the response. Only `published` is supported.", "location": "query", "type": "string"}, "includeSubscribed": {"default": "true", "description": "Whether to include changes outside the My Drive hierarchy in the result. When set to false, changes to files such as those in the Application Data folder or shared files which have not been added to My Drive will be omitted from the result.", "location": "query", "type": "boolean"}, "includeTeamDriveItems": {"default": "false", "deprecated": true, "description": "Deprecated: Use `includeItemsFromAllDrives` instead.", "location": "query", "type": "boolean"}, "maxResults": {"default": "100", "description": "Maximum number of changes to return.", "format": "int32", "location": "query", "minimum": "1", "type": "integer"}, "pageToken": {"description": "The token for continuing a previous list request on the next page. This should be set to the value of `nextPageToken` from the previous response or to the response from the getStartPageToken method.", "location": "query", "type": "string"}, "spaces": {"description": "A comma-separated list of spaces to query. Supported values are `drive`, `appDataFolder` and `photos`.", "location": "query", "type": "string"}, "startChangeId": {"deprecated": true, "description": "Deprecated: Use `pageToken` instead.", "format": "int64", "location": "query", "type": "string"}, "supportsAllDrives": {"default": "false", "description": "Whether the requesting application supports both My Drives and shared drives.", "location": "query", "type": "boolean"}, "supportsTeamDrives": {"default": "false", "deprecated": true, "description": "Deprecated: Use `supportsAllDrives` instead.", "location": "query", "type": "boolean"}, "teamDriveId": {"deprecated": true, "description": "Deprecated: Use `driveId` instead.", "location": "query", "type": "string"}}, "path": "changes", "response": {"$ref": "ChangeList"}, "scopes": ["https://www.googleapis.com/auth/drive", "https://www.googleapis.com/auth/drive.appdata", "https://www.googleapis.com/auth/drive.apps.readonly", "https://www.googleapis.com/auth/drive.file", "https://www.googleapis.com/auth/drive.meet.readonly", "https://www.googleapis.com/auth/drive.metadata", "https://www.googleapis.com/auth/drive.metadata.readonly", "https://www.googleapis.com/auth/drive.photos.readonly", "https://www.googleapis.com/auth/drive.readonly"], "supportsSubscription": true}, "watch": {"description": "Subscribe to changes for a user.", "flatPath": "changes/watch", "httpMethod": "POST", "id": "drive.changes.watch", "parameterOrder": [], "parameters": {"driveId": {"description": "The shared drive from which changes will be returned. If specified the change IDs will be reflective of the shared drive; use the combined drive ID and change ID as an identifier.", "location": "query", "type": "string"}, "includeCorpusRemovals": {"default": "false", "description": "Whether changes should include the file resource if the file is still accessible by the user at the time of the request, even when a file was removed from the list of changes and there will be no further change entries for this file.", "location": "query", "type": "boolean"}, "includeDeleted": {"default": "true", "description": "Whether to include changes indicating that items have been removed from the list of changes, for example by deletion or loss of access.", "location": "query", "type": "boolean"}, "includeItemsFromAllDrives": {"default": "false", "description": "Whether both My Drive and shared drive items should be included in results.", "location": "query", "type": "boolean"}, "includeLabels": {"description": "A comma-separated list of IDs of labels to include in the `labelInfo` part of the response.", "location": "query", "type": "string"}, "includePermissionsForView": {"description": "Specifies which additional view's permissions to include in the response. Only `published` is supported.", "location": "query", "type": "string"}, "includeSubscribed": {"default": "true", "description": "Whether to include changes outside the My Drive hierarchy in the result. When set to false, changes to files such as those in the Application Data folder or shared files which have not been added to My Drive will be omitted from the result.", "location": "query", "type": "boolean"}, "includeTeamDriveItems": {"default": "false", "deprecated": true, "description": "Deprecated: Use `includeItemsFromAllDrives` instead.", "location": "query", "type": "boolean"}, "maxResults": {"default": "100", "description": "Maximum number of changes to return.", "format": "int32", "location": "query", "minimum": "1", "type": "integer"}, "pageToken": {"description": "The token for continuing a previous list request on the next page. This should be set to the value of `nextPageToken` from the previous response or to the response from the getStartPageToken method.", "location": "query", "type": "string"}, "spaces": {"description": "A comma-separated list of spaces to query. Supported values are `drive`, `appDataFolder` and `photos`.", "location": "query", "type": "string"}, "startChangeId": {"deprecated": true, "description": "Deprecated: Use `pageToken` instead.", "format": "int64", "location": "query", "type": "string"}, "supportsAllDrives": {"default": "false", "description": "Whether the requesting application supports both My Drives and shared drives.", "location": "query", "type": "boolean"}, "supportsTeamDrives": {"default": "false", "deprecated": true, "description": "Deprecated: Use `supportsAllDrives` instead.", "location": "query", "type": "boolean"}, "teamDriveId": {"deprecated": true, "description": "Deprecated: Use `driveId` instead.", "location": "query", "type": "string"}}, "path": "changes/watch", "request": {"$ref": "Channel", "parameterName": "resource"}, "response": {"$ref": "Channel"}, "scopes": ["https://www.googleapis.com/auth/drive", "https://www.googleapis.com/auth/drive.appdata", "https://www.googleapis.com/auth/drive.apps.readonly", "https://www.googleapis.com/auth/drive.file", "https://www.googleapis.com/auth/drive.meet.readonly", "https://www.googleapis.com/auth/drive.metadata", "https://www.googleapis.com/auth/drive.metadata.readonly", "https://www.googleapis.com/auth/drive.photos.readonly", "https://www.googleapis.com/auth/drive.readonly"], "supportsSubscription": true}}}, "channels": {"methods": {"stop": {"description": "Stops watching resources through this channel.", "flatPath": "channels/stop", "httpMethod": "POST", "id": "drive.channels.stop", "parameterOrder": [], "parameters": {}, "path": "channels/stop", "request": {"$ref": "Channel", "parameterName": "resource"}, "scopes": ["https://www.googleapis.com/auth/drive", "https://www.googleapis.com/auth/drive.appdata", "https://www.googleapis.com/auth/drive.apps.readonly", "https://www.googleapis.com/auth/drive.file", "https://www.googleapis.com/auth/drive.meet.readonly", "https://www.googleapis.com/auth/drive.metadata", "https://www.googleapis.com/auth/drive.metadata.readonly", "https://www.googleapis.com/auth/drive.photos.readonly", "https://www.googleapis.com/auth/drive.readonly"]}}}, "children": {"methods": {"delete": {"description": "Removes a child from a folder.", "flatPath": "files/{folderId}/children/{childId}", "httpMethod": "DELETE", "id": "drive.children.delete", "parameterOrder": ["folderId", "childId"], "parameters": {"childId": {"description": "The ID of the child.", "location": "path", "required": true, "type": "string"}, "enforceSingleParent": {"default": "false", "deprecated": true, "description": "Deprecated: If an item is not in a shared drive and its last parent is removed, the item is placed under its owner's root.", "location": "query", "type": "boolean"}, "folderId": {"description": "The ID of the folder.", "location": "path", "required": true, "type": "string"}}, "path": "files/{folderId}/children/{childId}", "scopes": ["https://www.googleapis.com/auth/drive", "https://www.googleapis.com/auth/drive.file"]}, "get": {"description": "Gets a specific child reference.", "flatPath": "files/{folderId}/children/{childId}", "httpMethod": "GET", "id": "drive.children.get", "parameterOrder": ["folderId", "childId"], "parameters": {"childId": {"description": "The ID of the child.", "location": "path", "required": true, "type": "string"}, "folderId": {"description": "The ID of the folder.", "location": "path", "required": true, "type": "string"}}, "path": "files/{folderId}/children/{childId}", "response": {"$ref": "ChildReference"}, "scopes": ["https://www.googleapis.com/auth/drive", "https://www.googleapis.com/auth/drive.appdata", "https://www.googleapis.com/auth/drive.file", "https://www.googleapis.com/auth/drive.meet.readonly", "https://www.googleapis.com/auth/drive.metadata", "https://www.googleapis.com/auth/drive.metadata.readonly", "https://www.googleapis.com/auth/drive.photos.readonly", "https://www.googleapis.com/auth/drive.readonly"]}, "insert": {"description": "Inserts a file into a folder.", "flatPath": "files/{folderId}/children", "httpMethod": "POST", "id": "drive.children.insert", "parameterOrder": ["folderId"], "parameters": {"enforceSingleParent": {"default": "false", "deprecated": true, "description": "Deprecated: Adding files to multiple folders is no longer supported. Use `shortcuts` instead.", "location": "query", "type": "boolean"}, "folderId": {"description": "The ID of the folder.", "location": "path", "required": true, "type": "string"}, "supportsAllDrives": {"default": "false", "description": "Whether the requesting application supports both My Drives and shared drives.", "location": "query", "type": "boolean"}, "supportsTeamDrives": {"default": "false", "deprecated": true, "description": "Deprecated: Use `supportsAllDrives` instead.", "location": "query", "type": "boolean"}}, "path": "files/{folderId}/children", "request": {"$ref": "ChildReference"}, "response": {"$ref": "ChildReference"}, "scopes": ["https://www.googleapis.com/auth/drive", "https://www.googleapis.com/auth/drive.appdata", "https://www.googleapis.com/auth/drive.file"]}, "list": {"description": "Lists a folder's children.", "flatPath": "files/{folderId}/children", "httpMethod": "GET", "id": "drive.children.list", "parameterOrder": ["folderId"], "parameters": {"folderId": {"description": "The ID of the folder.", "location": "path", "required": true, "type": "string"}, "maxResults": {"default": "100", "description": "Maximum number of children to return.", "format": "int32", "location": "query", "minimum": "0", "type": "integer"}, "orderBy": {"description": "A comma-separated list of sort keys. Valid keys are `createdDate`, `folder`, `lastViewedByMeDate`, `modifiedByMeDate`, `modifiedDate`, `quotaBytesUsed`, `recency`, `sharedWithMeDate`, `starred`, and `title`. Each key sorts ascending by default, but may be reversed with the `desc` modifier. Example usage: ?orderBy=folder,modifiedDate desc,title. Please note that there is a current limitation for users with approximately one million files in which the requested sort order is ignored.", "location": "query", "type": "string"}, "pageToken": {"description": "Page token for children.", "location": "query", "type": "string"}, "q": {"description": "Query string for searching children.", "location": "query", "type": "string"}}, "path": "files/{folderId}/children", "response": {"$ref": "ChildList"}, "scopes": ["https://www.googleapis.com/auth/drive", "https://www.googleapis.com/auth/drive.appdata", "https://www.googleapis.com/auth/drive.file", "https://www.googleapis.com/auth/drive.meet.readonly", "https://www.googleapis.com/auth/drive.metadata", "https://www.googleapis.com/auth/drive.metadata.readonly", "https://www.googleapis.com/auth/drive.photos.readonly", "https://www.googleapis.com/auth/drive.readonly"]}}}, "comments": {"methods": {"delete": {"description": "Deletes a comment.", "flatPath": "files/{fileId}/comments/{commentId}", "httpMethod": "DELETE", "id": "drive.comments.delete", "parameterOrder": ["fileId", "commentId"], "parameters": {"commentId": {"description": "The ID of the comment.", "location": "path", "required": true, "type": "string"}, "fileId": {"description": "The ID of the file.", "location": "path", "required": true, "type": "string"}}, "path": "files/{fileId}/comments/{commentId}", "scopes": ["https://www.googleapis.com/auth/drive", "https://www.googleapis.com/auth/drive.file"]}, "get": {"description": "Gets a comment by <PERSON>.", "flatPath": "files/{fileId}/comments/{commentId}", "httpMethod": "GET", "id": "drive.comments.get", "parameterOrder": ["fileId", "commentId"], "parameters": {"commentId": {"description": "The ID of the comment.", "location": "path", "required": true, "type": "string"}, "fileId": {"description": "The ID of the file.", "location": "path", "required": true, "type": "string"}, "includeDeleted": {"default": "false", "description": "If set, this will succeed when retrieving a deleted comment, and will include any deleted replies.", "location": "query", "type": "boolean"}}, "path": "files/{fileId}/comments/{commentId}", "response": {"$ref": "Comment"}, "scopes": ["https://www.googleapis.com/auth/drive", "https://www.googleapis.com/auth/drive.file", "https://www.googleapis.com/auth/drive.meet.readonly", "https://www.googleapis.com/auth/drive.readonly"]}, "insert": {"description": "Creates a new comment on the given file.", "flatPath": "files/{fileId}/comments", "httpMethod": "POST", "id": "drive.comments.insert", "parameterOrder": ["fileId"], "parameters": {"fileId": {"description": "The ID of the file.", "location": "path", "required": true, "type": "string"}}, "path": "files/{fileId}/comments", "request": {"$ref": "Comment"}, "response": {"$ref": "Comment"}, "scopes": ["https://www.googleapis.com/auth/drive", "https://www.googleapis.com/auth/drive.file"]}, "list": {"description": "Lists a file's comments.", "flatPath": "files/{fileId}/comments", "httpMethod": "GET", "id": "drive.comments.list", "parameterOrder": ["fileId"], "parameters": {"fileId": {"description": "The ID of the file.", "location": "path", "required": true, "type": "string"}, "includeDeleted": {"default": "false", "description": "If set, all comments and replies, including deleted comments and replies (with content stripped) will be returned.", "location": "query", "type": "boolean"}, "maxResults": {"default": "20", "description": "The maximum number of discussions to include in the response, used for paging.", "format": "int32", "location": "query", "maximum": "100", "minimum": "0", "type": "integer"}, "pageToken": {"description": "The continuation token, used to page through large result sets. To get the next page of results, set this parameter to the value of \"nextPageToken\" from the previous response.", "location": "query", "type": "string"}, "updatedMin": {"description": "Only discussions that were updated after this timestamp will be returned. Formatted as an RFC 3339 timestamp.", "location": "query", "type": "string"}}, "path": "files/{fileId}/comments", "response": {"$ref": "CommentList"}, "scopes": ["https://www.googleapis.com/auth/drive", "https://www.googleapis.com/auth/drive.file", "https://www.googleapis.com/auth/drive.meet.readonly", "https://www.googleapis.com/auth/drive.readonly"]}, "patch": {"description": "Updates an existing comment.", "flatPath": "files/{fileId}/comments/{commentId}", "httpMethod": "PATCH", "id": "drive.comments.patch", "parameterOrder": ["fileId", "commentId"], "parameters": {"commentId": {"description": "The ID of the comment.", "location": "path", "required": true, "type": "string"}, "fileId": {"description": "The ID of the file.", "location": "path", "required": true, "type": "string"}}, "path": "files/{fileId}/comments/{commentId}", "request": {"$ref": "Comment"}, "response": {"$ref": "Comment"}, "scopes": ["https://www.googleapis.com/auth/drive", "https://www.googleapis.com/auth/drive.file"]}, "update": {"description": "Updates an existing comment.", "flatPath": "files/{fileId}/comments/{commentId}", "httpMethod": "PUT", "id": "drive.comments.update", "parameterOrder": ["fileId", "commentId"], "parameters": {"commentId": {"description": "The ID of the comment.", "location": "path", "required": true, "type": "string"}, "fileId": {"description": "The ID of the file.", "location": "path", "required": true, "type": "string"}}, "path": "files/{fileId}/comments/{commentId}", "request": {"$ref": "Comment"}, "response": {"$ref": "Comment"}, "scopes": ["https://www.googleapis.com/auth/drive", "https://www.googleapis.com/auth/drive.file"]}}}, "drives": {"methods": {"delete": {"description": "Permanently deletes a shared drive for which the user is an `organizer`. The shared drive cannot contain any untrashed items.", "flatPath": "drives/{driveId}", "httpMethod": "DELETE", "id": "drive.drives.delete", "parameterOrder": ["driveId"], "parameters": {"allowItemDeletion": {"default": "false", "description": "Whether any items inside the shared drive should also be deleted. This option is only supported when `useDomainAdminAccess` is also set to `true`.", "location": "query", "type": "boolean"}, "driveId": {"description": "The ID of the shared drive.", "location": "path", "required": true, "type": "string"}, "useDomainAdminAccess": {"default": "false", "description": "Issue the request as a domain administrator; if set to true, then the requester will be granted access if they are an administrator of the domain to which the shared drive belongs.", "location": "query", "type": "boolean"}}, "path": "drives/{driveId}", "scopes": ["https://www.googleapis.com/auth/drive"]}, "get": {"description": "Gets a shared drive's metadata by ID.", "flatPath": "drives/{driveId}", "httpMethod": "GET", "id": "drive.drives.get", "parameterOrder": ["driveId"], "parameters": {"driveId": {"description": "The ID of the shared drive.", "location": "path", "required": true, "type": "string"}, "useDomainAdminAccess": {"default": "false", "description": "Issue the request as a domain administrator; if set to true, then the requester will be granted access if they are an administrator of the domain to which the shared drive belongs.", "location": "query", "type": "boolean"}}, "path": "drives/{driveId}", "response": {"$ref": "Drive"}, "scopes": ["https://www.googleapis.com/auth/drive", "https://www.googleapis.com/auth/drive.readonly"]}, "hide": {"description": "Hides a shared drive from the default view.", "flatPath": "drives/{driveId}/hide", "httpMethod": "POST", "id": "drive.drives.hide", "parameterOrder": ["driveId"], "parameters": {"driveId": {"description": "The ID of the shared drive.", "location": "path", "required": true, "type": "string"}}, "path": "drives/{driveId}/hide", "response": {"$ref": "Drive"}, "scopes": ["https://www.googleapis.com/auth/drive"]}, "insert": {"description": "Creates a new shared drive.", "flatPath": "drives", "httpMethod": "POST", "id": "drive.drives.insert", "parameterOrder": ["requestId"], "parameters": {"requestId": {"description": "Required. An ID, such as a random UUID, which uniquely identifies this user's request for idempotent creation of a shared drive. A repeated request by the same user and with the same request ID will avoid creating duplicates by attempting to create the same shared drive. If the shared drive already exists a 409 error will be returned.", "location": "query", "required": true, "type": "string"}}, "path": "drives", "request": {"$ref": "Drive"}, "response": {"$ref": "Drive"}, "scopes": ["https://www.googleapis.com/auth/drive"]}, "list": {"description": " Lists the user's shared drives. This method accepts the `q` parameter, which is a search query combining one or more search terms. For more information, see the [Search for shared drives](/workspace/drive/api/guides/search-shareddrives) guide.", "flatPath": "drives", "httpMethod": "GET", "id": "drive.drives.list", "parameterOrder": [], "parameters": {"maxResults": {"default": "10", "description": "Maximum number of shared drives to return per page.", "format": "int32", "location": "query", "maximum": "100", "minimum": "1", "type": "integer"}, "pageToken": {"description": "Page token for shared drives.", "location": "query", "type": "string"}, "q": {"description": "Query string for searching shared drives.", "location": "query", "type": "string"}, "useDomainAdminAccess": {"default": "false", "description": "Issue the request as a domain administrator; if set to true, then all shared drives of the domain in which the requester is an administrator are returned.", "location": "query", "type": "boolean"}}, "path": "drives", "response": {"$ref": "DriveList"}, "scopes": ["https://www.googleapis.com/auth/drive", "https://www.googleapis.com/auth/drive.readonly"]}, "unhide": {"description": "Restores a shared drive to the default view.", "flatPath": "drives/{driveId}/unhide", "httpMethod": "POST", "id": "drive.drives.unhide", "parameterOrder": ["driveId"], "parameters": {"driveId": {"description": "The ID of the shared drive.", "location": "path", "required": true, "type": "string"}}, "path": "drives/{driveId}/unhide", "response": {"$ref": "Drive"}, "scopes": ["https://www.googleapis.com/auth/drive"]}, "update": {"description": "Updates the metadata for a shared drive.", "flatPath": "drives/{driveId}", "httpMethod": "PUT", "id": "drive.drives.update", "parameterOrder": ["driveId"], "parameters": {"driveId": {"description": "The ID of the shared drive.", "location": "path", "required": true, "type": "string"}, "useDomainAdminAccess": {"default": "false", "description": "Issue the request as a domain administrator; if set to true, then the requester will be granted access if they are an administrator of the domain to which the shared drive belongs.", "location": "query", "type": "boolean"}}, "path": "drives/{driveId}", "request": {"$ref": "Drive"}, "response": {"$ref": "Drive"}, "scopes": ["https://www.googleapis.com/auth/drive"]}}}, "files": {"methods": {"copy": {"description": "Creates a copy of the specified file.", "flatPath": "files/{fileId}/copy", "httpMethod": "POST", "id": "drive.files.copy", "parameterOrder": ["fileId"], "parameters": {"convert": {"default": "false", "description": "Whether to convert this file to the corresponding Docs Editors format.", "location": "query", "type": "boolean"}, "enforceSingleParent": {"default": "false", "deprecated": true, "description": "Deprecated: Copying files into multiple folders is no longer supported. Use shortcuts instead.", "location": "query", "type": "boolean"}, "fileId": {"description": "The ID of the file to copy.", "location": "path", "required": true, "type": "string"}, "includeLabels": {"description": "A comma-separated list of IDs of labels to include in the `labelInfo` part of the response.", "location": "query", "type": "string"}, "includePermissionsForView": {"description": "Specifies which additional view's permissions to include in the response. Only `published` is supported.", "location": "query", "type": "string"}, "ocr": {"default": "false", "description": "Whether to attempt OCR on .jpg, .png, .gif, or .pdf uploads.", "location": "query", "type": "boolean"}, "ocrLanguage": {"description": "If `ocr` is true, hints at the language to use. Valid values are BCP 47 codes.", "location": "query", "type": "string"}, "pinned": {"default": "false", "description": "Whether to pin the head revision of the new copy. A file can have a maximum of 200 pinned revisions.", "location": "query", "type": "boolean"}, "supportsAllDrives": {"default": "false", "description": "Whether the requesting application supports both My Drives and shared drives.", "location": "query", "type": "boolean"}, "supportsTeamDrives": {"default": "false", "deprecated": true, "description": "Deprecated: Use `supportsAllDrives` instead.", "location": "query", "type": "boolean"}, "timedTextLanguage": {"description": "The language of the timed text.", "location": "query", "type": "string"}, "timedTextTrackName": {"description": "The timed text track name.", "location": "query", "type": "string"}, "visibility": {"default": "DEFAULT", "description": "The visibility of the new file. This parameter is only relevant when the source is not a native Google Doc and convert=false.", "enum": ["DEFAULT", "PRIVATE"], "enumDescriptions": ["The visibility of the new file is determined by the user's default visibility/sharing policies.", "The new file will be visible to only the owner."], "location": "query", "type": "string"}}, "path": "files/{fileId}/copy", "request": {"$ref": "File"}, "response": {"$ref": "File"}, "scopes": ["https://www.googleapis.com/auth/drive", "https://www.googleapis.com/auth/drive.appdata", "https://www.googleapis.com/auth/drive.apps.readonly", "https://www.googleapis.com/auth/drive.file", "https://www.googleapis.com/auth/drive.photos.readonly"]}, "delete": {"description": "Permanently deletes a file owned by the user without moving it to the trash. If the file belongs to a shared drive, the user must be an `organizer` on the parent folder. If the target is a folder, all descendants owned by the user are also deleted.", "flatPath": "files/{fileId}", "httpMethod": "DELETE", "id": "drive.files.delete", "parameterOrder": ["fileId"], "parameters": {"enforceSingleParent": {"default": "false", "deprecated": true, "description": "Deprecated: If an item is not in a shared drive and its last parent is deleted but the item itself is not, the item is placed under its owner's root.", "location": "query", "type": "boolean"}, "fileId": {"description": "The ID of the file to delete.", "location": "path", "required": true, "type": "string"}, "supportsAllDrives": {"default": "false", "description": "Whether the requesting application supports both My Drives and shared drives.", "location": "query", "type": "boolean"}, "supportsTeamDrives": {"default": "false", "deprecated": true, "description": "Deprecated: Use `supportsAllDrives` instead.", "location": "query", "type": "boolean"}}, "path": "files/{fileId}", "scopes": ["https://www.googleapis.com/auth/drive", "https://www.googleapis.com/auth/drive.appdata", "https://www.googleapis.com/auth/drive.file"]}, "emptyTrash": {"description": "Permanently deletes all of the user's trashed files.", "flatPath": "files/trash", "httpMethod": "DELETE", "id": "drive.files.emptyTrash", "parameterOrder": [], "parameters": {"driveId": {"description": "If set, empties the trash of the provided shared drive.", "location": "query", "type": "string"}, "enforceSingleParent": {"default": "false", "deprecated": true, "description": "Deprecated: If an item is not in a shared drive and its last parent is deleted but the item itself is not, the item is placed under its owner's root.", "location": "query", "type": "boolean"}}, "path": "files/trash", "scopes": ["https://www.googleapis.com/auth/drive"]}, "export": {"description": "Exports a Google Workspace document to the requested MIME type and returns exported byte content. Note that the exported content is limited to 10MB.", "flatPath": "files/{fileId}/export", "httpMethod": "GET", "id": "drive.files.export", "parameterOrder": ["fileId", "mimeType"], "parameters": {"fileId": {"description": "The ID of the file.", "location": "path", "required": true, "type": "string"}, "mimeType": {"description": "Required. The MIME type of the format requested for this export.", "location": "query", "required": true, "type": "string"}}, "path": "files/{fileId}/export", "scopes": ["https://www.googleapis.com/auth/drive", "https://www.googleapis.com/auth/drive.file", "https://www.googleapis.com/auth/drive.meet.readonly", "https://www.googleapis.com/auth/drive.readonly"], "supportsMediaDownload": true, "useMediaDownloadService": true}, "generateIds": {"description": "Generates a set of file IDs which can be provided in insert or copy requests.", "flatPath": "files/generateIds", "httpMethod": "GET", "id": "drive.files.generateIds", "parameterOrder": [], "parameters": {"maxResults": {"default": "10", "description": "Maximum number of IDs to return.", "format": "int32", "location": "query", "maximum": "1000", "minimum": "1", "type": "integer"}, "space": {"default": "drive", "description": "The space in which the IDs can be used to create new files. Supported values are `drive` and `appDataFolder`. (Default: `drive`)", "location": "query", "type": "string"}, "type": {"default": "files", "description": "The type of items which the IDs can be used for. Supported values are `files` and `shortcuts`. Note that `shortcuts` are only supported in the `drive` `space`. (Default: `files`)", "location": "query", "type": "string"}}, "path": "files/generateIds", "response": {"$ref": "GeneratedIds"}, "scopes": ["https://www.googleapis.com/auth/drive", "https://www.googleapis.com/auth/drive.appdata", "https://www.googleapis.com/auth/drive.file"]}, "get": {"description": " Gets a file's metadata or content by ID. If you provide the URL parameter `alt=media`, then the response includes the file contents in the response body. Downloading content with `alt=media` only works if the file is stored in Drive. To download Google Docs, Sheets, and Slides use [`files.export`](/workspace/drive/api/reference/rest/v2/files/export) instead. For more information, see [Download & export files](/workspace/drive/api/guides/manage-downloads).", "flatPath": "files/{fileId}", "httpMethod": "GET", "id": "drive.files.get", "parameterOrder": ["fileId"], "parameters": {"acknowledgeAbuse": {"default": "false", "description": "Whether the user is acknowledging the risk of downloading known malware or other abusive files. This is only applicable when the `alt` parameter is set to `media` and the user is the owner of the file or an organizer of the shared drive in which the file resides.", "location": "query", "type": "boolean"}, "fileId": {"description": "The ID for the file in question.", "location": "path", "required": true, "type": "string"}, "includeLabels": {"description": "A comma-separated list of IDs of labels to include in the `labelInfo` part of the response.", "location": "query", "type": "string"}, "includePermissionsForView": {"description": "Specifies which additional view's permissions to include in the response. Only `published` is supported.", "location": "query", "type": "string"}, "projection": {"deprecated": true, "description": "Deprecated: This parameter has no function.", "enum": ["BASIC", "FULL"], "enumDescriptions": ["Deprecated.", "Deprecated."], "location": "query", "type": "string"}, "revisionId": {"description": "Specifies the Revision ID that should be downloaded. Ignored unless alt=media is specified.", "location": "query", "type": "string"}, "supportsAllDrives": {"default": "false", "description": "Whether the requesting application supports both My Drives and shared drives.", "location": "query", "type": "boolean"}, "supportsTeamDrives": {"default": "false", "deprecated": true, "description": "Deprecated: Use `supportsAllDrives` instead.", "location": "query", "type": "boolean"}, "updateViewedDate": {"default": "false", "deprecated": true, "description": "Deprecated: Use `files.update` with `modifiedDateBehavior=noChange, updateViewedDate=true` and an empty request body.", "location": "query", "type": "boolean"}}, "path": "files/{fileId}", "response": {"$ref": "File"}, "scopes": ["https://www.googleapis.com/auth/drive", "https://www.googleapis.com/auth/drive.appdata", "https://www.googleapis.com/auth/drive.file", "https://www.googleapis.com/auth/drive.meet.readonly", "https://www.googleapis.com/auth/drive.metadata", "https://www.googleapis.com/auth/drive.metadata.readonly", "https://www.googleapis.com/auth/drive.photos.readonly", "https://www.googleapis.com/auth/drive.readonly"], "supportsMediaDownload": true, "supportsSubscription": true, "useMediaDownloadService": true}, "insert": {"description": " Inserts a new file. This method supports an */upload* URI and accepts uploaded media with the following characteristics: - *Maximum file size:* 5,120 GB - *Accepted Media MIME types:*`*/*` Note: Specify a valid MIME type, rather than the literal `*/*` value. The literal `*/*` is only used to indicate that any valid MIME type can be uploaded. For more information on uploading files, see [Upload file data](/workspace/drive/api/guides/manage-uploads). Apps creating shortcuts with `files.insert` must specify the MIME type `application/vnd.google-apps.shortcut`. Apps should specify a file extension in the `title` property when inserting files with the API. For example, an operation to insert a JPEG file should specify something like `\"title\": \"cat.jpg\"` in the metadata. Subsequent `GET` requests include the read-only `fileExtension` property populated with the extension originally specified in the `title` property. When a Google Drive user requests to download a file, or when the file is downloaded through the sync client, <PERSON> builds a full filename (with extension) based on the title. In cases where the extension is missing, <PERSON> attempts to determine the extension based on the file's MIME type.", "flatPath": "files", "httpMethod": "POST", "id": "drive.files.insert", "mediaUpload": {"accept": ["*/*"], "maxSize": "5497558138880", "protocols": {"resumable": {"multipart": true, "path": "/resumable/upload/drive/v2/files"}, "simple": {"multipart": true, "path": "/upload/drive/v2/files"}}}, "parameterOrder": [], "parameters": {"convert": {"default": "false", "description": "Whether to convert this file to the corresponding Docs Editors format.", "location": "query", "type": "boolean"}, "enforceSingleParent": {"default": "false", "deprecated": true, "description": "Deprecated: Creating files in multiple folders is no longer supported.", "location": "query", "type": "boolean"}, "includeLabels": {"description": "A comma-separated list of IDs of labels to include in the `labelInfo` part of the response.", "location": "query", "type": "string"}, "includePermissionsForView": {"description": "Specifies which additional view's permissions to include in the response. Only `published` is supported.", "location": "query", "type": "string"}, "ocr": {"default": "false", "description": "Whether to attempt OCR on .jpg, .png, .gif, or .pdf uploads.", "location": "query", "type": "boolean"}, "ocrLanguage": {"description": "If ocr is true, hints at the language to use. Valid values are BCP 47 codes.", "location": "query", "type": "string"}, "pinned": {"default": "false", "description": "Whether to pin the head revision of the uploaded file. A file can have a maximum of 200 pinned revisions.", "location": "query", "type": "boolean"}, "supportsAllDrives": {"default": "false", "description": "Whether the requesting application supports both My Drives and shared drives.", "location": "query", "type": "boolean"}, "supportsTeamDrives": {"default": "false", "deprecated": true, "description": "Deprecated: Use `supportsAllDrives` instead.", "location": "query", "type": "boolean"}, "timedTextLanguage": {"description": "The language of the timed text.", "location": "query", "type": "string"}, "timedTextTrackName": {"description": "The timed text track name.", "location": "query", "type": "string"}, "useContentAsIndexableText": {"default": "false", "description": "Whether to use the content as indexable text.", "location": "query", "type": "boolean"}, "visibility": {"default": "DEFAULT", "description": "The visibility of the new file. This parameter is only relevant when convert=false.", "enum": ["DEFAULT", "PRIVATE"], "enumDescriptions": ["The visibility of the new file is determined by the user's default visibility/sharing policies.", "The new file will be visible to only the owner."], "location": "query", "type": "string"}}, "path": "files", "request": {"$ref": "File"}, "response": {"$ref": "File"}, "scopes": ["https://www.googleapis.com/auth/drive", "https://www.googleapis.com/auth/drive.appdata", "https://www.googleapis.com/auth/drive.apps.readonly", "https://www.googleapis.com/auth/drive.file"], "supportsMediaUpload": true}, "list": {"description": " Lists the user's files. This method accepts the `q` parameter, which is a search query combining one or more search terms. For more information, see the [Search for files & folders](/workspace/drive/api/guides/search-files) guide. *Note:* This method returns *all* files by default, including trashed files. If you don't want trashed files to appear in the list, use the `trashed=false` query parameter to remove trashed files from the results.", "flatPath": "files", "httpMethod": "GET", "id": "drive.files.list", "parameterOrder": [], "parameters": {"corpora": {"description": "Bodies of items (files/documents) to which the query applies. Supported bodies are `default`, `domain`, `drive` and `allDrives`. Prefer `default` or `drive` to `allDrives` for efficiency.", "location": "query", "type": "string"}, "corpus": {"deprecated": true, "description": "Deprecated: The body of items (files/documents) to which the query applies. Use `corpora` instead.", "enum": ["DEFAULT", "DOMAIN"], "enumDescriptions": ["The items that the user has accessed.", "Items shared to the user's domain."], "location": "query", "type": "string"}, "driveId": {"description": "ID of the shared drive to search.", "location": "query", "type": "string"}, "includeItemsFromAllDrives": {"default": "false", "description": "Whether both My Drive and shared drive items should be included in results.", "location": "query", "type": "boolean"}, "includeLabels": {"description": "A comma-separated list of IDs of labels to include in the `labelInfo` part of the response.", "location": "query", "type": "string"}, "includePermissionsForView": {"description": "Specifies which additional view's permissions to include in the response. Only `published` is supported.", "location": "query", "type": "string"}, "includeTeamDriveItems": {"default": "false", "deprecated": true, "description": "Deprecated: Use `includeItemsFromAllDrives` instead.", "location": "query", "type": "boolean"}, "maxResults": {"default": "100", "description": "The maximum number of files to return per page. Partial or empty result pages are possible even before the end of the files list has been reached.", "format": "int32", "location": "query", "minimum": "0", "type": "integer"}, "orderBy": {"description": "A comma-separated list of sort keys. Valid keys are: * `createdDate`: When the file was created. * `folder`: The folder ID. This field is sorted using alphabetical ordering. * `lastViewedByMeDate`: The last time the file was viewed by the user. * `modifiedByMeDate`: The last time the file was modified by the user. * `modifiedDate`: The last time the file was modified by anyone. * `quotaBytesUsed`: The number of storage quota bytes used by the file. * `recency`: The most recent timestamp from the file's date-time fields. * `sharedWithMeDate`: When the file was shared with the user, if applicable. * `starred`: Whether the user has starred the file. * `title`: The title of the file. This field is sorted using alphabetical ordering, so 1, 12, 2, 22. * `title_natural`: The title of the file. This field is sorted using natural sort ordering, so 1, 2, 12, 22. Each key sorts ascending by default, but can be reversed with the 'desc' modifier. Example usage: `?orderBy=folder,modifiedDate desc,title`. Note that there's a current limitation for users with approximately one million files in which the requested sort order is ignored.", "location": "query", "type": "string"}, "pageToken": {"description": "Page token for files.", "location": "query", "type": "string"}, "projection": {"deprecated": true, "description": "Deprecated: This parameter has no function.", "enum": ["BASIC", "FULL"], "enumDescriptions": ["Deprecated.", "Deprecated."], "location": "query", "type": "string"}, "q": {"description": "Query string for searching files.", "location": "query", "type": "string"}, "spaces": {"description": "A comma-separated list of spaces to query. Supported values are `drive`, and `appDataFolder`.", "location": "query", "type": "string"}, "supportsAllDrives": {"default": "false", "description": "Whether the requesting application supports both My Drives and shared drives.", "location": "query", "type": "boolean"}, "supportsTeamDrives": {"default": "false", "deprecated": true, "description": "Deprecated: Use `supportsAllDrives` instead.", "location": "query", "type": "boolean"}, "teamDriveId": {"deprecated": true, "description": "Deprecated: Use `driveId` instead.", "location": "query", "type": "string"}}, "path": "files", "response": {"$ref": "FileList"}, "scopes": ["https://www.googleapis.com/auth/drive", "https://www.googleapis.com/auth/drive.appdata", "https://www.googleapis.com/auth/drive.apps.readonly", "https://www.googleapis.com/auth/drive.file", "https://www.googleapis.com/auth/drive.meet.readonly", "https://www.googleapis.com/auth/drive.metadata", "https://www.googleapis.com/auth/drive.metadata.readonly", "https://www.googleapis.com/auth/drive.photos.readonly", "https://www.googleapis.com/auth/drive.readonly"]}, "listLabels": {"description": "Lists the labels on a file.", "flatPath": "files/{fileId}/listLabels", "httpMethod": "GET", "id": "drive.files.listLabels", "parameterOrder": ["fileId"], "parameters": {"fileId": {"description": "The ID for the file.", "location": "path", "required": true, "type": "string"}, "maxResults": {"default": "100", "description": "The maximum number of labels to return per page. When not set, defaults to 100.", "format": "int32", "location": "query", "maximum": "100", "minimum": "1", "type": "integer"}, "pageToken": {"description": "The token for continuing a previous list request on the next page. This should be set to the value of `nextPageToken` from the previous response.", "location": "query", "type": "string"}}, "path": "files/{fileId}/listLabels", "response": {"$ref": "LabelList"}, "scopes": ["https://www.googleapis.com/auth/drive", "https://www.googleapis.com/auth/drive.file", "https://www.googleapis.com/auth/drive.meet.readonly", "https://www.googleapis.com/auth/drive.metadata", "https://www.googleapis.com/auth/drive.metadata.readonly", "https://www.googleapis.com/auth/drive.readonly"]}, "modifyLabels": {"description": "Modifies the set of labels applied to a file. Returns a list of the labels that were added or modified.", "flatPath": "files/{fileId}/modifyLabels", "httpMethod": "POST", "id": "drive.files.modifyLabels", "parameterOrder": ["fileId"], "parameters": {"fileId": {"description": "The ID of the file to which the labels belong.", "location": "path", "required": true, "type": "string"}}, "path": "files/{fileId}/modifyLabels", "request": {"$ref": "ModifyLabelsRequest"}, "response": {"$ref": "ModifyLabelsResponse"}, "scopes": ["https://www.googleapis.com/auth/drive", "https://www.googleapis.com/auth/drive.file", "https://www.googleapis.com/auth/drive.metadata"]}, "patch": {"description": "Updates a file's metadata and/or content. When calling this method, only populate fields in the request that you want to modify. When updating fields, some fields might change automatically, such as modifiedDate. This method supports patch semantics.", "flatPath": "files/{fileId}", "httpMethod": "PATCH", "id": "drive.files.patch", "parameterOrder": ["fileId"], "parameters": {"addParents": {"description": "Comma-separated list of parent IDs to add.", "location": "query", "type": "string"}, "convert": {"default": "false", "deprecated": true, "description": "Deprecated: This parameter has no function.", "location": "query", "type": "boolean"}, "enforceSingleParent": {"default": "false", "deprecated": true, "description": "Deprecated: Adding files to multiple folders is no longer supported. Use `shortcuts` instead.", "location": "query", "type": "boolean"}, "fileId": {"description": "The ID of the file to update.", "location": "path", "required": true, "type": "string"}, "includeLabels": {"description": "A comma-separated list of IDs of labels to include in the `labelInfo` part of the response.", "location": "query", "type": "string"}, "includePermissionsForView": {"description": "Specifies which additional view's permissions to include in the response. Only `published` is supported.", "location": "query", "type": "string"}, "modifiedDateBehavior": {"description": "Determines the behavior in which `modifiedDate` is updated. This overrides `setModifiedDate`.", "enum": ["fromBody", "fromBodyIfNeeded", "fromBodyOrNow", "noChange", "now", "nowIfNeeded"], "enumDescriptions": ["Set `modifiedDate` to the value provided in the body of the request. No change if no value was provided.", "Set `modifiedDate` to the value provided in the body of the request depending on other contents of the update.", "Set modifiedDate to the value provided in the body of the request, or to the current time if no value was provided.", "Maintain the previous value of `modifiedDate`.", "Set `modifiedDate` to the current time.", "Set `modifiedDate` to the current time depending on contents of the update."], "location": "query", "type": "string"}, "newRevision": {"default": "true", "description": "Whether a blob upload should create a new revision. If false, the blob data in the current head revision is replaced. If true or not set, a new blob is created as head revision, and previous unpinned revisions are preserved for a short period of time. Pinned revisions are stored indefinitely, using additional storage quota, up to a maximum of 200 revisions. For details on how revisions are retained, see the [Drive Help Center](https://support.google.com/drive/answer/2409045). Note that this field is ignored if there is no payload in the request.", "location": "query", "type": "boolean"}, "ocr": {"default": "false", "description": "Whether to attempt OCR on .jpg, .png, .gif, or .pdf uploads.", "location": "query", "type": "boolean"}, "ocrLanguage": {"description": "If ocr is true, hints at the language to use. Valid values are BCP 47 codes.", "location": "query", "type": "string"}, "pinned": {"default": "false", "description": "Whether to pin the new revision. A file can have a maximum of 200 pinned revisions. Note that this field is ignored if there is no payload in the request.", "location": "query", "type": "boolean"}, "removeParents": {"description": "Comma-separated list of parent IDs to remove.", "location": "query", "type": "string"}, "setModifiedDate": {"default": "false", "description": "Whether to set the modified date using the value supplied in the request body. Setting this field to `true` is equivalent to `modifiedDateBehavior=fromBodyOrNow`, and `false` is equivalent to `modifiedDateBehavior=now`. To prevent any changes to the modified date set `modifiedDateBehavior=noChange`.", "location": "query", "type": "boolean"}, "supportsAllDrives": {"default": "false", "description": "Whether the requesting application supports both My Drives and shared drives.", "location": "query", "type": "boolean"}, "supportsTeamDrives": {"default": "false", "deprecated": true, "description": "Deprecated: Use `supportsAllDrives` instead.", "location": "query", "type": "boolean"}, "timedTextLanguage": {"description": "The language of the timed text.", "location": "query", "type": "string"}, "timedTextTrackName": {"description": "The timed text track name.", "location": "query", "type": "string"}, "updateViewedDate": {"default": "true", "description": "Whether to update the view date after successfully updating the file.", "location": "query", "type": "boolean"}, "useContentAsIndexableText": {"default": "false", "description": "Whether to use the content as indexable text.", "location": "query", "type": "boolean"}}, "path": "files/{fileId}", "request": {"$ref": "File"}, "response": {"$ref": "File"}, "scopes": ["https://www.googleapis.com/auth/drive", "https://www.googleapis.com/auth/drive.appdata", "https://www.googleapis.com/auth/drive.apps.readonly", "https://www.googleapis.com/auth/drive.file", "https://www.googleapis.com/auth/drive.metadata", "https://www.googleapis.com/auth/drive.scripts"]}, "touch": {"description": "Set the file's updated time to the current server time.", "flatPath": "files/{fileId}/touch", "httpMethod": "POST", "id": "drive.files.touch", "parameterOrder": ["fileId"], "parameters": {"fileId": {"description": "The ID of the file to update.", "location": "path", "required": true, "type": "string"}, "includeLabels": {"description": "A comma-separated list of IDs of labels to include in the `labelInfo` part of the response.", "location": "query", "type": "string"}, "includePermissionsForView": {"description": "Specifies which additional view's permissions to include in the response. Only `published` is supported.", "location": "query", "type": "string"}, "supportsAllDrives": {"default": "false", "description": "Whether the requesting application supports both My Drives and shared drives.", "location": "query", "type": "boolean"}, "supportsTeamDrives": {"default": "false", "deprecated": true, "description": "Deprecated: Use `supportsAllDrives` instead.", "location": "query", "type": "boolean"}}, "path": "files/{fileId}/touch", "response": {"$ref": "File"}, "scopes": ["https://www.googleapis.com/auth/drive", "https://www.googleapis.com/auth/drive.appdata", "https://www.googleapis.com/auth/drive.apps.readonly", "https://www.googleapis.com/auth/drive.file", "https://www.googleapis.com/auth/drive.metadata"]}, "trash": {"description": "Moves a file to the trash. The currently authenticated user must own the file or be at least a `fileOrganizer` on the parent for shared drive files.", "flatPath": "files/{fileId}/trash", "httpMethod": "POST", "id": "drive.files.trash", "parameterOrder": ["fileId"], "parameters": {"fileId": {"description": "The ID of the file to trash.", "location": "path", "required": true, "type": "string"}, "includeLabels": {"description": "A comma-separated list of IDs of labels to include in the `labelInfo` part of the response.", "location": "query", "type": "string"}, "includePermissionsForView": {"description": "Specifies which additional view's permissions to include in the response. Only `published` is supported.", "location": "query", "type": "string"}, "supportsAllDrives": {"default": "false", "description": "Whether the requesting application supports both My Drives and shared drives.", "location": "query", "type": "boolean"}, "supportsTeamDrives": {"default": "false", "deprecated": true, "description": "Deprecated: Use `supportsAllDrives` instead.", "location": "query", "type": "boolean"}}, "path": "files/{fileId}/trash", "response": {"$ref": "File"}, "scopes": ["https://www.googleapis.com/auth/drive", "https://www.googleapis.com/auth/drive.appdata", "https://www.googleapis.com/auth/drive.apps.readonly", "https://www.googleapis.com/auth/drive.file"]}, "untrash": {"description": "Restores a file from the trash. The currently authenticated user must own the file or be at least a `fileOrganizer` on the parent for shared drive files.", "flatPath": "files/{fileId}/untrash", "httpMethod": "POST", "id": "drive.files.untrash", "parameterOrder": ["fileId"], "parameters": {"fileId": {"description": "The ID of the file to untrash.", "location": "path", "required": true, "type": "string"}, "includeLabels": {"description": "A comma-separated list of IDs of labels to include in the `labelInfo` part of the response.", "location": "query", "type": "string"}, "includePermissionsForView": {"description": "Specifies which additional view's permissions to include in the response. Only `published` is supported.", "location": "query", "type": "string"}, "supportsAllDrives": {"default": "false", "description": "Whether the requesting application supports both My Drives and shared drives.", "location": "query", "type": "boolean"}, "supportsTeamDrives": {"default": "false", "deprecated": true, "description": "Deprecated: Use `supportsAllDrives` instead.", "location": "query", "type": "boolean"}}, "path": "files/{fileId}/untrash", "response": {"$ref": "File"}, "scopes": ["https://www.googleapis.com/auth/drive", "https://www.googleapis.com/auth/drive.appdata", "https://www.googleapis.com/auth/drive.apps.readonly", "https://www.googleapis.com/auth/drive.file"]}, "update": {"description": " Updates a file's metadata and/or content. When calling this method, only populate fields in the request that you want to modify. When updating fields, some fields might be changed automatically, such as `modifiedDate`. This method supports patch semantics. This method supports an */upload* URI and accepts uploaded media with the following characteristics: - *Maximum file size:* 5,120 GB - *Accepted Media MIME types:*`*/*` Note: Specify a valid MIME type, rather than the literal `*/*` value. The literal `*/*` is only used to indicate that any valid MIME type can be uploaded. For more information on uploading files, see [Upload file data](/workspace/drive/api/guides/manage-uploads).", "flatPath": "files/{fileId}", "httpMethod": "PUT", "id": "drive.files.update", "mediaUpload": {"accept": ["*/*"], "maxSize": "5497558138880", "protocols": {"resumable": {"multipart": true, "path": "/resumable/upload/drive/v2/files/{fileId}"}, "simple": {"multipart": true, "path": "/upload/drive/v2/files/{fileId}"}}}, "parameterOrder": ["fileId"], "parameters": {"addParents": {"description": "Comma-separated list of parent IDs to add.", "location": "query", "type": "string"}, "convert": {"default": "false", "deprecated": true, "description": "Deprecated: This parameter has no function.", "location": "query", "type": "boolean"}, "enforceSingleParent": {"default": "false", "deprecated": true, "description": "Deprecated: Adding files to multiple folders is no longer supported. Use `shortcuts` instead.", "location": "query", "type": "boolean"}, "fileId": {"description": "The ID of the file to update.", "location": "path", "required": true, "type": "string"}, "includeLabels": {"description": "A comma-separated list of IDs of labels to include in the `labelInfo` part of the response.", "location": "query", "type": "string"}, "includePermissionsForView": {"description": "Specifies which additional view's permissions to include in the response. Only `published` is supported.", "location": "query", "type": "string"}, "modifiedDateBehavior": {"description": "Determines the behavior in which `modifiedDate` is updated. This overrides `setModifiedDate`.", "enum": ["fromBody", "fromBodyIfNeeded", "fromBodyOrNow", "noChange", "now", "nowIfNeeded"], "enumDescriptions": ["Set `modifiedDate` to the value provided in the body of the request. No change if no value was provided.", "Set `modifiedDate` to the value provided in the body of the request depending on other contents of the update.", "Set modifiedDate to the value provided in the body of the request, or to the current time if no value was provided.", "Maintain the previous value of `modifiedDate`.", "Set `modifiedDate` to the current time.", "Set `modifiedDate` to the current time depending on contents of the update."], "location": "query", "type": "string"}, "newRevision": {"default": "true", "description": "Whether a blob upload should create a new revision. If false, the blob data in the current head revision is replaced. If true or not set, a new blob is created as head revision, and previous unpinned revisions are preserved for a short period of time. Pinned revisions are stored indefinitely, using additional storage quota, up to a maximum of 200 revisions. For details on how revisions are retained, see the [Drive Help Center](https://support.google.com/drive/answer/2409045).", "location": "query", "type": "boolean"}, "ocr": {"default": "false", "description": "Whether to attempt OCR on .jpg, .png, .gif, or .pdf uploads.", "location": "query", "type": "boolean"}, "ocrLanguage": {"description": "If ocr is true, hints at the language to use. Valid values are BCP 47 codes.", "location": "query", "type": "string"}, "pinned": {"default": "false", "description": "Whether to pin the new revision. A file can have a maximum of 200 pinned revisions.", "location": "query", "type": "boolean"}, "removeParents": {"description": "Comma-separated list of parent IDs to remove.", "location": "query", "type": "string"}, "setModifiedDate": {"default": "false", "description": "Whether to set the modified date using the value supplied in the request body. Setting this field to `true` is equivalent to `modifiedDateBehavior=fromBodyOrNow`, and `false` is equivalent to `modifiedDateBehavior=now`. To prevent any changes to the modified date set `modifiedDateBehavior=noChange`.", "location": "query", "type": "boolean"}, "supportsAllDrives": {"default": "false", "description": "Whether the requesting application supports both My Drives and shared drives.", "location": "query", "type": "boolean"}, "supportsTeamDrives": {"default": "false", "deprecated": true, "description": "Deprecated: Use `supportsAllDrives` instead.", "location": "query", "type": "boolean"}, "timedTextLanguage": {"description": "The language of the timed text.", "location": "query", "type": "string"}, "timedTextTrackName": {"description": "The timed text track name.", "location": "query", "type": "string"}, "updateViewedDate": {"default": "true", "description": "Whether to update the view date after successfully updating the file.", "location": "query", "type": "boolean"}, "useContentAsIndexableText": {"default": "false", "description": "Whether to use the content as indexable text.", "location": "query", "type": "boolean"}}, "path": "files/{fileId}", "request": {"$ref": "File"}, "response": {"$ref": "File"}, "scopes": ["https://www.googleapis.com/auth/drive", "https://www.googleapis.com/auth/drive.appdata", "https://www.googleapis.com/auth/drive.apps.readonly", "https://www.googleapis.com/auth/drive.file", "https://www.googleapis.com/auth/drive.metadata", "https://www.googleapis.com/auth/drive.scripts"], "supportsMediaUpload": true}, "watch": {"description": "Subscribes to changes to a file.", "flatPath": "files/{fileId}/watch", "httpMethod": "POST", "id": "drive.files.watch", "parameterOrder": ["fileId"], "parameters": {"acknowledgeAbuse": {"default": "false", "description": "Whether the user is acknowledging the risk of downloading known malware or other abusive files. This is only applicable when the `alt` parameter is set to `media` and the user is the owner of the file or an organizer of the shared drive in which the file resides.", "location": "query", "type": "boolean"}, "fileId": {"description": "The ID for the file in question.", "location": "path", "required": true, "type": "string"}, "includeLabels": {"description": "A comma-separated list of IDs of labels to include in the `labelInfo` part of the response.", "location": "query", "type": "string"}, "includePermissionsForView": {"description": "Specifies which additional view's permissions to include in the response. Only `published` is supported.", "location": "query", "type": "string"}, "projection": {"deprecated": true, "description": "Deprecated: This parameter has no function.", "enum": ["BASIC", "FULL"], "enumDescriptions": ["Deprecated.", "Deprecated."], "location": "query", "type": "string"}, "revisionId": {"description": "Specifies the Revision ID that should be downloaded. Ignored unless alt=media is specified.", "location": "query", "type": "string"}, "supportsAllDrives": {"default": "false", "description": "Whether the requesting application supports both My Drives and shared drives.", "location": "query", "type": "boolean"}, "supportsTeamDrives": {"default": "false", "deprecated": true, "description": "Deprecated: Use `supportsAllDrives` instead.", "location": "query", "type": "boolean"}, "updateViewedDate": {"default": "false", "deprecated": true, "description": "Deprecated: Use files.update with modifiedDateBehavior=noChange, updateViewedDate=true and an empty request body.", "location": "query", "type": "boolean"}}, "path": "files/{fileId}/watch", "request": {"$ref": "Channel", "parameterName": "resource"}, "response": {"$ref": "Channel"}, "scopes": ["https://www.googleapis.com/auth/drive", "https://www.googleapis.com/auth/drive.appdata", "https://www.googleapis.com/auth/drive.file", "https://www.googleapis.com/auth/drive.meet.readonly", "https://www.googleapis.com/auth/drive.metadata", "https://www.googleapis.com/auth/drive.metadata.readonly", "https://www.googleapis.com/auth/drive.photos.readonly", "https://www.googleapis.com/auth/drive.readonly"], "supportsSubscription": true}}}, "parents": {"methods": {"delete": {"description": "Removes a parent from a file.", "flatPath": "files/{fileId}/parents/{parentId}", "httpMethod": "DELETE", "id": "drive.parents.delete", "parameterOrder": ["fileId", "parentId"], "parameters": {"enforceSingleParent": {"default": "false", "deprecated": true, "description": "Deprecated: If an item is not in a shared drive and its last parent is removed, the item is placed under its owner's root.", "location": "query", "type": "boolean"}, "fileId": {"description": "The ID of the file.", "location": "path", "required": true, "type": "string"}, "parentId": {"description": "The ID of the parent.", "location": "path", "required": true, "type": "string"}}, "path": "files/{fileId}/parents/{parentId}", "scopes": ["https://www.googleapis.com/auth/drive", "https://www.googleapis.com/auth/drive.file"]}, "get": {"description": "Gets a specific parent reference.", "flatPath": "files/{fileId}/parents/{parentId}", "httpMethod": "GET", "id": "drive.parents.get", "parameterOrder": ["fileId", "parentId"], "parameters": {"fileId": {"description": "The ID of the file.", "location": "path", "required": true, "type": "string"}, "parentId": {"description": "The ID of the parent.", "location": "path", "required": true, "type": "string"}}, "path": "files/{fileId}/parents/{parentId}", "response": {"$ref": "ParentReference"}, "scopes": ["https://www.googleapis.com/auth/drive", "https://www.googleapis.com/auth/drive.appdata", "https://www.googleapis.com/auth/drive.file", "https://www.googleapis.com/auth/drive.meet.readonly", "https://www.googleapis.com/auth/drive.metadata", "https://www.googleapis.com/auth/drive.metadata.readonly", "https://www.googleapis.com/auth/drive.photos.readonly", "https://www.googleapis.com/auth/drive.readonly"]}, "insert": {"description": "Adds a parent folder for a file.", "flatPath": "files/{fileId}/parents", "httpMethod": "POST", "id": "drive.parents.insert", "parameterOrder": ["fileId"], "parameters": {"enforceSingleParent": {"default": "false", "deprecated": true, "description": "Deprecated: Adding files to multiple folders is no longer supported. Use `shortcuts` instead.", "location": "query", "type": "boolean"}, "fileId": {"description": "The ID of the file.", "location": "path", "required": true, "type": "string"}, "supportsAllDrives": {"default": "false", "description": "Whether the requesting application supports both My Drives and shared drives.", "location": "query", "type": "boolean"}, "supportsTeamDrives": {"default": "false", "deprecated": true, "description": "Deprecated: Use `supportsAllDrives` instead.", "location": "query", "type": "boolean"}}, "path": "files/{fileId}/parents", "request": {"$ref": "ParentReference"}, "response": {"$ref": "ParentReference"}, "scopes": ["https://www.googleapis.com/auth/drive", "https://www.googleapis.com/auth/drive.appdata", "https://www.googleapis.com/auth/drive.file"]}, "list": {"description": "Lists a file's parents.", "flatPath": "files/{fileId}/parents", "httpMethod": "GET", "id": "drive.parents.list", "parameterOrder": ["fileId"], "parameters": {"fileId": {"description": "The ID of the file.", "location": "path", "required": true, "type": "string"}}, "path": "files/{fileId}/parents", "response": {"$ref": "ParentList"}, "scopes": ["https://www.googleapis.com/auth/drive", "https://www.googleapis.com/auth/drive.appdata", "https://www.googleapis.com/auth/drive.file", "https://www.googleapis.com/auth/drive.meet.readonly", "https://www.googleapis.com/auth/drive.metadata", "https://www.googleapis.com/auth/drive.metadata.readonly", "https://www.googleapis.com/auth/drive.photos.readonly", "https://www.googleapis.com/auth/drive.readonly"]}}}, "permissions": {"methods": {"delete": {"description": "Deletes a permission from a file or shared drive. **Warning:** Concurrent permissions operations on the same file are not supported; only the last update is applied.", "flatPath": "files/{fileId}/permissions/{permissionId}", "httpMethod": "DELETE", "id": "drive.permissions.delete", "parameterOrder": ["fileId", "permissionId"], "parameters": {"enforceExpansiveAccess": {"default": "false", "description": "Whether the request should enforce expansive access rules.", "location": "query", "type": "boolean"}, "fileId": {"description": "The ID for the file or shared drive.", "location": "path", "required": true, "type": "string"}, "permissionId": {"description": "The ID for the permission.", "location": "path", "required": true, "type": "string"}, "supportsAllDrives": {"default": "false", "description": "Whether the requesting application supports both My Drives and shared drives.", "location": "query", "type": "boolean"}, "supportsTeamDrives": {"default": "false", "deprecated": true, "description": "Deprecated: Use `supportsAllDrives` instead.", "location": "query", "type": "boolean"}, "useDomainAdminAccess": {"default": "false", "description": "Issue the request as a domain administrator; if set to true, then the requester will be granted access if the file ID parameter refers to a shared drive and the requester is an administrator of the domain to which the shared drive belongs.", "location": "query", "type": "boolean"}}, "path": "files/{fileId}/permissions/{permissionId}", "scopes": ["https://www.googleapis.com/auth/drive", "https://www.googleapis.com/auth/drive.file"]}, "get": {"description": "Gets a permission by ID.", "flatPath": "files/{fileId}/permissions/{permissionId}", "httpMethod": "GET", "id": "drive.permissions.get", "parameterOrder": ["fileId", "permissionId"], "parameters": {"fileId": {"description": "The ID for the file or shared drive.", "location": "path", "required": true, "type": "string"}, "permissionId": {"description": "The ID for the permission.", "location": "path", "required": true, "type": "string"}, "supportsAllDrives": {"default": "false", "description": "Whether the requesting application supports both My Drives and shared drives.", "location": "query", "type": "boolean"}, "supportsTeamDrives": {"default": "false", "deprecated": true, "description": "Deprecated: Use `supportsAllDrives` instead.", "location": "query", "type": "boolean"}, "useDomainAdminAccess": {"default": "false", "description": "Issue the request as a domain administrator; if set to true, then the requester will be granted access if the file ID parameter refers to a shared drive and the requester is an administrator of the domain to which the shared drive belongs.", "location": "query", "type": "boolean"}}, "path": "files/{fileId}/permissions/{permissionId}", "response": {"$ref": "Permission"}, "scopes": ["https://www.googleapis.com/auth/drive", "https://www.googleapis.com/auth/drive.file", "https://www.googleapis.com/auth/drive.meet.readonly", "https://www.googleapis.com/auth/drive.metadata", "https://www.googleapis.com/auth/drive.metadata.readonly", "https://www.googleapis.com/auth/drive.photos.readonly", "https://www.googleapis.com/auth/drive.readonly"]}, "getIdForEmail": {"description": "Returns the permission ID for an email address.", "flatPath": "permissionIds/{email}", "httpMethod": "GET", "id": "drive.permissions.getIdForEmail", "parameterOrder": ["email"], "parameters": {"email": {"description": "The email address for which to return a permission ID", "location": "path", "required": true, "type": "string"}}, "path": "permissionIds/{email}", "response": {"$ref": "PermissionId"}, "scopes": ["https://www.googleapis.com/auth/drive", "https://www.googleapis.com/auth/drive.appdata", "https://www.googleapis.com/auth/drive.apps.readonly", "https://www.googleapis.com/auth/drive.file", "https://www.googleapis.com/auth/drive.metadata", "https://www.googleapis.com/auth/drive.metadata.readonly", "https://www.googleapis.com/auth/drive.photos.readonly", "https://www.googleapis.com/auth/drive.readonly"]}, "insert": {"description": "Inserts a permission for a file or shared drive. **Warning:** Concurrent permissions operations on the same file are not supported; only the last update is applied.", "flatPath": "files/{fileId}/permissions", "httpMethod": "POST", "id": "drive.permissions.insert", "parameterOrder": ["fileId"], "parameters": {"emailMessage": {"description": "A plain text custom message to include in notification emails.", "location": "query", "type": "string"}, "enforceExpansiveAccess": {"default": "false", "description": "Whether the request should enforce expansive access rules.", "location": "query", "type": "boolean"}, "enforceSingleParent": {"default": "false", "deprecated": true, "description": "Deprecated: See `moveToNewOwnersRoot` for details.", "location": "query", "type": "boolean"}, "fileId": {"description": "The ID for the file or shared drive.", "location": "path", "required": true, "type": "string"}, "moveToNewOwnersRoot": {"default": "false", "description": "This parameter will only take effect if the item is not in a shared drive and the request is attempting to transfer the ownership of the item. If set to `true`, the item will be moved to the new owner's My Drive root folder and all prior parents removed. If set to `false`, parents are not changed.", "location": "query", "type": "boolean"}, "sendNotificationEmails": {"default": "true", "description": "Whether to send notification emails when sharing to users or groups. This parameter is ignored and an email is sent if the `role` is `owner`.", "location": "query", "type": "boolean"}, "supportsAllDrives": {"default": "false", "description": "Whether the requesting application supports both My Drives and shared drives.", "location": "query", "type": "boolean"}, "supportsTeamDrives": {"default": "false", "deprecated": true, "description": "Deprecated: Use `supportsAllDrives` instead.", "location": "query", "type": "boolean"}, "useDomainAdminAccess": {"default": "false", "description": "Issue the request as a domain administrator; if set to true, then the requester will be granted access if the file ID parameter refers to a shared drive and the requester is an administrator of the domain to which the shared drive belongs.", "location": "query", "type": "boolean"}}, "path": "files/{fileId}/permissions", "request": {"$ref": "Permission"}, "response": {"$ref": "Permission"}, "scopes": ["https://www.googleapis.com/auth/drive", "https://www.googleapis.com/auth/drive.file"]}, "list": {"description": "Lists a file's or shared drive's permissions.", "flatPath": "files/{fileId}/permissions", "httpMethod": "GET", "id": "drive.permissions.list", "parameterOrder": ["fileId"], "parameters": {"fileId": {"description": "The ID for the file or shared drive.", "location": "path", "required": true, "type": "string"}, "includePermissionsForView": {"description": "Specifies which additional view's permissions to include in the response. Only `published` is supported.", "location": "query", "type": "string"}, "maxResults": {"description": "The maximum number of permissions to return per page. When not set for files in a shared drive, at most 100 results will be returned. When not set for files that are not in a shared drive, the entire list will be returned.", "format": "int32", "location": "query", "maximum": "100", "minimum": "1", "type": "integer"}, "pageToken": {"description": "The token for continuing a previous list request on the next page. This should be set to the value of `nextPageToken` from the previous response.", "location": "query", "type": "string"}, "supportsAllDrives": {"default": "false", "description": "Whether the requesting application supports both My Drives and shared drives.", "location": "query", "type": "boolean"}, "supportsTeamDrives": {"default": "false", "deprecated": true, "description": "Deprecated: Use `supportsAllDrives` instead.", "location": "query", "type": "boolean"}, "useDomainAdminAccess": {"default": "false", "description": "Issue the request as a domain administrator; if set to true, then the requester will be granted access if the file ID parameter refers to a shared drive and the requester is an administrator of the domain to which the shared drive belongs.", "location": "query", "type": "boolean"}}, "path": "files/{fileId}/permissions", "response": {"$ref": "PermissionList"}, "scopes": ["https://www.googleapis.com/auth/drive", "https://www.googleapis.com/auth/drive.file", "https://www.googleapis.com/auth/drive.meet.readonly", "https://www.googleapis.com/auth/drive.metadata", "https://www.googleapis.com/auth/drive.metadata.readonly", "https://www.googleapis.com/auth/drive.photos.readonly", "https://www.googleapis.com/auth/drive.readonly"]}, "patch": {"description": "Updates a permission using patch semantics. **Warning:** Concurrent permissions operations on the same file are not supported; only the last update is applied.", "flatPath": "files/{fileId}/permissions/{permissionId}", "httpMethod": "PATCH", "id": "drive.permissions.patch", "parameterOrder": ["fileId", "permissionId"], "parameters": {"enforceExpansiveAccess": {"default": "false", "description": "Whether the request should enforce expansive access rules.", "location": "query", "type": "boolean"}, "fileId": {"description": "The ID for the file or shared drive.", "location": "path", "required": true, "type": "string"}, "permissionId": {"description": "The ID for the permission.", "location": "path", "required": true, "type": "string"}, "removeExpiration": {"default": "false", "description": "Whether to remove the expiration date.", "location": "query", "type": "boolean"}, "supportsAllDrives": {"default": "false", "description": "Whether the requesting application supports both My Drives and shared drives.", "location": "query", "type": "boolean"}, "supportsTeamDrives": {"default": "false", "deprecated": true, "description": "Deprecated: Use `supportsAllDrives` instead.", "location": "query", "type": "boolean"}, "transferOwnership": {"default": "false", "description": "Whether changing a role to `owner` downgrades the current owners to writers. Does nothing if the specified role is not `owner`.", "location": "query", "type": "boolean"}, "useDomainAdminAccess": {"default": "false", "description": "Issue the request as a domain administrator; if set to true, then the requester will be granted access if the file ID parameter refers to a shared drive and the requester is an administrator of the domain to which the shared drive belongs.", "location": "query", "type": "boolean"}}, "path": "files/{fileId}/permissions/{permissionId}", "request": {"$ref": "Permission"}, "response": {"$ref": "Permission"}, "scopes": ["https://www.googleapis.com/auth/drive", "https://www.googleapis.com/auth/drive.file"]}, "update": {"description": "Updates a permission. **Warning:** Concurrent permissions operations on the same file are not supported; only the last update is applied.", "flatPath": "files/{fileId}/permissions/{permissionId}", "httpMethod": "PUT", "id": "drive.permissions.update", "parameterOrder": ["fileId", "permissionId"], "parameters": {"enforceExpansiveAccess": {"default": "false", "description": "Whether the request should enforce expansive access rules.", "location": "query", "type": "boolean"}, "fileId": {"description": "The ID for the file or shared drive.", "location": "path", "required": true, "type": "string"}, "permissionId": {"description": "The ID for the permission.", "location": "path", "required": true, "type": "string"}, "removeExpiration": {"default": "false", "description": "Whether to remove the expiration date.", "location": "query", "type": "boolean"}, "supportsAllDrives": {"default": "false", "description": "Whether the requesting application supports both My Drives and shared drives.", "location": "query", "type": "boolean"}, "supportsTeamDrives": {"default": "false", "deprecated": true, "description": "Deprecated: Use `supportsAllDrives` instead.", "location": "query", "type": "boolean"}, "transferOwnership": {"default": "false", "description": "Whether changing a role to `owner` downgrades the current owners to writers. Does nothing if the specified role is not `owner`.", "location": "query", "type": "boolean"}, "useDomainAdminAccess": {"default": "false", "description": "Issue the request as a domain administrator; if set to true, then the requester will be granted access if the file ID parameter refers to a shared drive and the requester is an administrator of the domain to which the shared drive belongs.", "location": "query", "type": "boolean"}}, "path": "files/{fileId}/permissions/{permissionId}", "request": {"$ref": "Permission"}, "response": {"$ref": "Permission"}, "scopes": ["https://www.googleapis.com/auth/drive", "https://www.googleapis.com/auth/drive.file"]}}}, "properties": {"methods": {"delete": {"description": "Deletes a property.", "flatPath": "files/{fileId}/properties/{propertyKey}", "httpMethod": "DELETE", "id": "drive.properties.delete", "parameterOrder": ["fileId", "propertyKey"], "parameters": {"fileId": {"description": "The ID of the file.", "location": "path", "required": true, "type": "string"}, "propertyKey": {"description": "The key of the property.", "location": "path", "required": true, "type": "string"}, "visibility": {"default": "private", "description": "The visibility of the property.", "location": "query", "type": "string"}}, "path": "files/{fileId}/properties/{propertyKey}", "scopes": ["https://www.googleapis.com/auth/drive", "https://www.googleapis.com/auth/drive.appdata", "https://www.googleapis.com/auth/drive.file", "https://www.googleapis.com/auth/drive.metadata"]}, "get": {"description": "Gets a property by its key.", "flatPath": "files/{fileId}/properties/{propertyKey}", "httpMethod": "GET", "id": "drive.properties.get", "parameterOrder": ["fileId", "propertyKey"], "parameters": {"fileId": {"description": "The ID of the file.", "location": "path", "required": true, "type": "string"}, "propertyKey": {"description": "The key of the property.", "location": "path", "required": true, "type": "string"}, "visibility": {"default": "private", "description": "The visibility of the property.", "location": "query", "type": "string"}}, "path": "files/{fileId}/properties/{propertyKey}", "response": {"$ref": "Property"}, "scopes": ["https://www.googleapis.com/auth/drive", "https://www.googleapis.com/auth/drive.appdata", "https://www.googleapis.com/auth/drive.file", "https://www.googleapis.com/auth/drive.meet.readonly", "https://www.googleapis.com/auth/drive.metadata", "https://www.googleapis.com/auth/drive.metadata.readonly", "https://www.googleapis.com/auth/drive.photos.readonly", "https://www.googleapis.com/auth/drive.readonly"]}, "insert": {"description": "Adds a property to a file, or updates it if it already exists.", "flatPath": "files/{fileId}/properties", "httpMethod": "POST", "id": "drive.properties.insert", "parameterOrder": ["fileId"], "parameters": {"fileId": {"description": "The ID of the file.", "location": "path", "required": true, "type": "string"}}, "path": "files/{fileId}/properties", "request": {"$ref": "Property"}, "response": {"$ref": "Property"}, "scopes": ["https://www.googleapis.com/auth/drive", "https://www.googleapis.com/auth/drive.appdata", "https://www.googleapis.com/auth/drive.file", "https://www.googleapis.com/auth/drive.metadata"]}, "list": {"description": "Lists a file's properties.", "flatPath": "files/{fileId}/properties", "httpMethod": "GET", "id": "drive.properties.list", "parameterOrder": ["fileId"], "parameters": {"fileId": {"description": "The ID of the file.", "location": "path", "required": true, "type": "string"}}, "path": "files/{fileId}/properties", "response": {"$ref": "PropertyList"}, "scopes": ["https://www.googleapis.com/auth/drive", "https://www.googleapis.com/auth/drive.appdata", "https://www.googleapis.com/auth/drive.file", "https://www.googleapis.com/auth/drive.meet.readonly", "https://www.googleapis.com/auth/drive.metadata", "https://www.googleapis.com/auth/drive.metadata.readonly", "https://www.googleapis.com/auth/drive.photos.readonly", "https://www.googleapis.com/auth/drive.readonly"]}, "patch": {"description": "Updates a property.", "flatPath": "files/{fileId}/properties/{propertyKey}", "httpMethod": "PATCH", "id": "drive.properties.patch", "parameterOrder": ["fileId", "propertyKey"], "parameters": {"fileId": {"description": "The ID of the file.", "location": "path", "required": true, "type": "string"}, "propertyKey": {"description": "The key of the property.", "location": "path", "required": true, "type": "string"}, "visibility": {"default": "private", "description": "The visibility of the property. Allowed values are PRIVATE and PUBLIC. (Default: PRIVATE)", "location": "query", "type": "string"}}, "path": "files/{fileId}/properties/{propertyKey}", "request": {"$ref": "Property"}, "response": {"$ref": "Property"}, "scopes": ["https://www.googleapis.com/auth/drive", "https://www.googleapis.com/auth/drive.appdata", "https://www.googleapis.com/auth/drive.file", "https://www.googleapis.com/auth/drive.metadata"]}, "update": {"description": "Updates a property.", "flatPath": "files/{fileId}/properties/{propertyKey}", "httpMethod": "PUT", "id": "drive.properties.update", "parameterOrder": ["fileId", "propertyKey"], "parameters": {"fileId": {"description": "The ID of the file.", "location": "path", "required": true, "type": "string"}, "propertyKey": {"description": "The key of the property.", "location": "path", "required": true, "type": "string"}, "visibility": {"default": "private", "description": "The visibility of the property. Allowed values are PRIVATE and PUBLIC. (Default: PRIVATE)", "location": "query", "type": "string"}}, "path": "files/{fileId}/properties/{propertyKey}", "request": {"$ref": "Property"}, "response": {"$ref": "Property"}, "scopes": ["https://www.googleapis.com/auth/drive", "https://www.googleapis.com/auth/drive.appdata", "https://www.googleapis.com/auth/drive.file", "https://www.googleapis.com/auth/drive.metadata"]}}}, "replies": {"methods": {"delete": {"description": "Deletes a reply.", "flatPath": "files/{fileId}/comments/{commentId}/replies/{replyId}", "httpMethod": "DELETE", "id": "drive.replies.delete", "parameterOrder": ["fileId", "commentId", "replyId"], "parameters": {"commentId": {"description": "The ID of the comment.", "location": "path", "required": true, "type": "string"}, "fileId": {"description": "The ID of the file.", "location": "path", "required": true, "type": "string"}, "replyId": {"description": "The ID of the reply.", "location": "path", "required": true, "type": "string"}}, "path": "files/{fileId}/comments/{commentId}/replies/{replyId}", "scopes": ["https://www.googleapis.com/auth/drive", "https://www.googleapis.com/auth/drive.file"]}, "get": {"description": "Gets a reply.", "flatPath": "files/{fileId}/comments/{commentId}/replies/{replyId}", "httpMethod": "GET", "id": "drive.replies.get", "parameterOrder": ["fileId", "commentId", "replyId"], "parameters": {"commentId": {"description": "The ID of the comment.", "location": "path", "required": true, "type": "string"}, "fileId": {"description": "The ID of the file.", "location": "path", "required": true, "type": "string"}, "includeDeleted": {"default": "false", "description": "If set, this will succeed when retrieving a deleted reply.", "location": "query", "type": "boolean"}, "replyId": {"description": "The ID of the reply.", "location": "path", "required": true, "type": "string"}}, "path": "files/{fileId}/comments/{commentId}/replies/{replyId}", "response": {"$ref": "CommentReply"}, "scopes": ["https://www.googleapis.com/auth/drive", "https://www.googleapis.com/auth/drive.file", "https://www.googleapis.com/auth/drive.meet.readonly", "https://www.googleapis.com/auth/drive.readonly"]}, "insert": {"description": "Creates a new reply to the given comment.", "flatPath": "files/{fileId}/comments/{commentId}/replies", "httpMethod": "POST", "id": "drive.replies.insert", "parameterOrder": ["fileId", "commentId"], "parameters": {"commentId": {"description": "The ID of the comment.", "location": "path", "required": true, "type": "string"}, "fileId": {"description": "The ID of the file.", "location": "path", "required": true, "type": "string"}}, "path": "files/{fileId}/comments/{commentId}/replies", "request": {"$ref": "CommentReply"}, "response": {"$ref": "CommentReply"}, "scopes": ["https://www.googleapis.com/auth/drive", "https://www.googleapis.com/auth/drive.file"]}, "list": {"description": "Lists all of the replies to a comment.", "flatPath": "files/{fileId}/comments/{commentId}/replies", "httpMethod": "GET", "id": "drive.replies.list", "parameterOrder": ["fileId", "commentId"], "parameters": {"commentId": {"description": "The ID of the comment.", "location": "path", "required": true, "type": "string"}, "fileId": {"description": "The ID of the file.", "location": "path", "required": true, "type": "string"}, "includeDeleted": {"default": "false", "description": "If set, all replies, including deleted replies (with content stripped) will be returned.", "location": "query", "type": "boolean"}, "maxResults": {"default": "20", "description": "The maximum number of replies to include in the response, used for paging.", "format": "int32", "location": "query", "maximum": "100", "minimum": "0", "type": "integer"}, "pageToken": {"description": "The continuation token, used to page through large result sets. To get the next page of results, set this parameter to the value of \"nextPageToken\" from the previous response.", "location": "query", "type": "string"}}, "path": "files/{fileId}/comments/{commentId}/replies", "response": {"$ref": "CommentReplyList"}, "scopes": ["https://www.googleapis.com/auth/drive", "https://www.googleapis.com/auth/drive.file", "https://www.googleapis.com/auth/drive.meet.readonly", "https://www.googleapis.com/auth/drive.readonly"]}, "patch": {"description": "Updates an existing reply.", "flatPath": "files/{fileId}/comments/{commentId}/replies/{replyId}", "httpMethod": "PATCH", "id": "drive.replies.patch", "parameterOrder": ["fileId", "commentId", "replyId"], "parameters": {"commentId": {"description": "The ID of the comment.", "location": "path", "required": true, "type": "string"}, "fileId": {"description": "The ID of the file.", "location": "path", "required": true, "type": "string"}, "replyId": {"description": "The ID of the reply.", "location": "path", "required": true, "type": "string"}}, "path": "files/{fileId}/comments/{commentId}/replies/{replyId}", "request": {"$ref": "CommentReply"}, "response": {"$ref": "CommentReply"}, "scopes": ["https://www.googleapis.com/auth/drive", "https://www.googleapis.com/auth/drive.file"]}, "update": {"description": "Updates an existing reply.", "flatPath": "files/{fileId}/comments/{commentId}/replies/{replyId}", "httpMethod": "PUT", "id": "drive.replies.update", "parameterOrder": ["fileId", "commentId", "replyId"], "parameters": {"commentId": {"description": "The ID of the comment.", "location": "path", "required": true, "type": "string"}, "fileId": {"description": "The ID of the file.", "location": "path", "required": true, "type": "string"}, "replyId": {"description": "The ID of the reply.", "location": "path", "required": true, "type": "string"}}, "path": "files/{fileId}/comments/{commentId}/replies/{replyId}", "request": {"$ref": "CommentReply"}, "response": {"$ref": "CommentReply"}, "scopes": ["https://www.googleapis.com/auth/drive", "https://www.googleapis.com/auth/drive.file"]}}}, "revisions": {"methods": {"delete": {"description": "Permanently deletes a file version. You can only delete revisions for files with binary content, like images or videos. Revisions for other files, like Google Docs or Sheets, and the last remaining file version can't be deleted.", "flatPath": "files/{fileId}/revisions/{revisionId}", "httpMethod": "DELETE", "id": "drive.revisions.delete", "parameterOrder": ["fileId", "revisionId"], "parameters": {"fileId": {"description": "The ID of the file.", "location": "path", "required": true, "type": "string"}, "revisionId": {"description": "The ID of the revision.", "location": "path", "required": true, "type": "string"}}, "path": "files/{fileId}/revisions/{revisionId}", "scopes": ["https://www.googleapis.com/auth/drive", "https://www.googleapis.com/auth/drive.appdata", "https://www.googleapis.com/auth/drive.file"]}, "get": {"description": "Gets a specific revision.", "flatPath": "files/{fileId}/revisions/{revisionId}", "httpMethod": "GET", "id": "drive.revisions.get", "parameterOrder": ["fileId", "revisionId"], "parameters": {"fileId": {"description": "The ID of the file.", "location": "path", "required": true, "type": "string"}, "revisionId": {"description": "The ID of the revision.", "location": "path", "required": true, "type": "string"}}, "path": "files/{fileId}/revisions/{revisionId}", "response": {"$ref": "Revision"}, "scopes": ["https://www.googleapis.com/auth/drive", "https://www.googleapis.com/auth/drive.appdata", "https://www.googleapis.com/auth/drive.file", "https://www.googleapis.com/auth/drive.meet.readonly", "https://www.googleapis.com/auth/drive.metadata", "https://www.googleapis.com/auth/drive.metadata.readonly", "https://www.googleapis.com/auth/drive.photos.readonly", "https://www.googleapis.com/auth/drive.readonly"]}, "list": {"description": "Lists a file's revisions.", "flatPath": "files/{fileId}/revisions", "httpMethod": "GET", "id": "drive.revisions.list", "parameterOrder": ["fileId"], "parameters": {"fileId": {"description": "The ID of the file.", "location": "path", "required": true, "type": "string"}, "maxResults": {"default": "200", "description": "Maximum number of revisions to return.", "format": "int32", "location": "query", "maximum": "1000", "minimum": "1", "type": "integer"}, "pageToken": {"description": "Page token for revisions. To get the next page of results, set this parameter to the value of \"nextPageToken\" from the previous response.", "location": "query", "type": "string"}}, "path": "files/{fileId}/revisions", "response": {"$ref": "RevisionList"}, "scopes": ["https://www.googleapis.com/auth/drive", "https://www.googleapis.com/auth/drive.appdata", "https://www.googleapis.com/auth/drive.file", "https://www.googleapis.com/auth/drive.meet.readonly", "https://www.googleapis.com/auth/drive.metadata", "https://www.googleapis.com/auth/drive.metadata.readonly", "https://www.googleapis.com/auth/drive.photos.readonly", "https://www.googleapis.com/auth/drive.readonly"]}, "patch": {"description": "Updates a revision.", "flatPath": "files/{fileId}/revisions/{revisionId}", "httpMethod": "PATCH", "id": "drive.revisions.patch", "parameterOrder": ["fileId", "revisionId"], "parameters": {"fileId": {"description": "The ID for the file.", "location": "path", "required": true, "type": "string"}, "revisionId": {"description": "The ID for the revision.", "location": "path", "required": true, "type": "string"}}, "path": "files/{fileId}/revisions/{revisionId}", "request": {"$ref": "Revision"}, "response": {"$ref": "Revision"}, "scopes": ["https://www.googleapis.com/auth/drive", "https://www.googleapis.com/auth/drive.appdata", "https://www.googleapis.com/auth/drive.file"]}, "update": {"description": "Updates a revision.", "flatPath": "files/{fileId}/revisions/{revisionId}", "httpMethod": "PUT", "id": "drive.revisions.update", "parameterOrder": ["fileId", "revisionId"], "parameters": {"fileId": {"description": "The ID for the file.", "location": "path", "required": true, "type": "string"}, "revisionId": {"description": "The ID for the revision.", "location": "path", "required": true, "type": "string"}}, "path": "files/{fileId}/revisions/{revisionId}", "request": {"$ref": "Revision"}, "response": {"$ref": "Revision"}, "scopes": ["https://www.googleapis.com/auth/drive", "https://www.googleapis.com/auth/drive.appdata", "https://www.googleapis.com/auth/drive.file"]}}}, "teamdrives": {"methods": {"delete": {"description": "Deprecated: Use `drives.delete` instead.", "flatPath": "teamdrives/{teamDriveId}", "httpMethod": "DELETE", "id": "drive.teamdrives.delete", "parameterOrder": ["teamDriveId"], "parameters": {"teamDriveId": {"description": "The ID of the Team Drive", "location": "path", "required": true, "type": "string"}}, "path": "teamdrives/{teamDriveId}", "scopes": ["https://www.googleapis.com/auth/drive"]}, "get": {"description": "Deprecated: Use `drives.get` instead.", "flatPath": "teamdrives/{teamDriveId}", "httpMethod": "GET", "id": "drive.teamdrives.get", "parameterOrder": ["teamDriveId"], "parameters": {"teamDriveId": {"description": "The ID of the Team Drive", "location": "path", "required": true, "type": "string"}, "useDomainAdminAccess": {"default": "false", "description": "Issue the request as a domain administrator; if set to true, then the requester will be granted access if they are an administrator of the domain to which the Team Drive belongs.", "location": "query", "type": "boolean"}}, "path": "teamdrives/{teamDriveId}", "response": {"$ref": "TeamDrive"}, "scopes": ["https://www.googleapis.com/auth/drive", "https://www.googleapis.com/auth/drive.readonly"]}, "insert": {"description": "Deprecated: Use `drives.insert` instead.", "flatPath": "teamdrives", "httpMethod": "POST", "id": "drive.teamdrives.insert", "parameterOrder": ["requestId"], "parameters": {"requestId": {"description": "Required. An ID, such as a random UUID, which uniquely identifies this user's request for idempotent creation of a Team Drive. A repeated request by the same user and with the same request ID will avoid creating duplicates by attempting to create the same Team Drive. If the Team Drive already exists a 409 error will be returned.", "location": "query", "required": true, "type": "string"}}, "path": "teamdrives", "request": {"$ref": "TeamDrive"}, "response": {"$ref": "TeamDrive"}, "scopes": ["https://www.googleapis.com/auth/drive"]}, "list": {"description": "Deprecated: Use `drives.list` instead.", "flatPath": "teamdrives", "httpMethod": "GET", "id": "drive.teamdrives.list", "parameterOrder": [], "parameters": {"maxResults": {"default": "10", "description": "Maximum number of Team Drives to return.", "format": "int32", "location": "query", "maximum": "100", "minimum": "1", "type": "integer"}, "pageToken": {"description": "Page token for Team Drives.", "location": "query", "type": "string"}, "q": {"description": "Query string for searching Team Drives.", "location": "query", "type": "string"}, "useDomainAdminAccess": {"default": "false", "description": "Issue the request as a domain administrator; if set to true, then all Team Drives of the domain in which the requester is an administrator are returned.", "location": "query", "type": "boolean"}}, "path": "teamdrives", "response": {"$ref": "TeamDriveList"}, "scopes": ["https://www.googleapis.com/auth/drive", "https://www.googleapis.com/auth/drive.readonly"]}, "update": {"description": "Deprecated: Use `drives.update` instead.", "flatPath": "teamdrives/{teamDriveId}", "httpMethod": "PUT", "id": "drive.teamdrives.update", "parameterOrder": ["teamDriveId"], "parameters": {"teamDriveId": {"description": "The ID of the Team Drive", "location": "path", "required": true, "type": "string"}, "useDomainAdminAccess": {"default": "false", "description": "Issue the request as a domain administrator; if set to true, then the requester will be granted access if they are an administrator of the domain to which the Team Drive belongs.", "location": "query", "type": "boolean"}}, "path": "teamdrives/{teamDriveId}", "request": {"$ref": "TeamDrive"}, "response": {"$ref": "TeamDrive"}, "scopes": ["https://www.googleapis.com/auth/drive"]}}}}, "revision": "20250506", "rootUrl": "https://www.googleapis.com/", "schemas": {"About": {"description": "An item with user information and settings.", "id": "About", "properties": {"additionalRoleInfo": {"description": "Information about supported additional roles per file type. The most specific type takes precedence.", "items": {"properties": {"roleSets": {"description": "The supported additional roles per primary role.", "items": {"properties": {"additionalRoles": {"description": "The supported additional roles with the primary role.", "items": {"type": "string"}, "type": "array"}, "primaryRole": {"description": "A primary permission role.", "type": "string"}}, "type": "object"}, "type": "array"}, "type": {"description": "The content type that this additional role info applies to.", "type": "string"}}, "type": "object"}, "type": "array"}, "canCreateDrives": {"description": "Whether the user can create shared drives.", "type": "boolean"}, "canCreateTeamDrives": {"deprecated": true, "description": "Deprecated: Use `canCreateDrives` instead.", "type": "boolean"}, "domainSharingPolicy": {"description": "The domain sharing policy for the current user. Possible values are: * `allowed` * `allowedWithWarning` * `incomingOnly` * `disallowed`", "type": "string"}, "driveThemes": {"description": "A list of themes that are supported for shared drives.", "items": {"properties": {"backgroundImageLink": {"description": "A link to this theme's background image.", "type": "string"}, "colorRgb": {"description": "The color of this theme as an RGB hex string.", "type": "string"}, "id": {"description": "The ID of the theme.", "type": "string"}}, "type": "object"}, "type": "array"}, "etag": {"description": "The ETag of the item.", "type": "string"}, "exportFormats": {"description": "The allowable export formats.", "items": {"properties": {"source": {"description": "The content type to convert from.", "type": "string"}, "targets": {"description": "The possible content types to convert to.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "type": "array"}, "features": {"description": "List of additional features enabled on this account.", "items": {"properties": {"featureName": {"description": "The name of the feature.", "type": "string"}, "featureRate": {"description": "The request limit rate for this feature, in queries per second.", "format": "double", "type": "number"}}, "type": "object"}, "type": "array"}, "folderColorPalette": {"description": "The palette of allowable folder colors as RGB hex strings.", "items": {"type": "string"}, "type": "array"}, "importFormats": {"description": "The allowable import formats.", "items": {"properties": {"source": {"description": "The imported file's content type to convert from.", "type": "string"}, "targets": {"description": "The possible content types to convert to.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "type": "array"}, "isCurrentAppInstalled": {"description": "A boolean indicating whether the authenticated app is installed by the authenticated user.", "type": "boolean"}, "kind": {"default": "drive#about", "description": "This is always `drive#about`.", "type": "string"}, "languageCode": {"description": "The user's language or locale code, as defined by BCP 47, with some extensions from Unicode's LDML format (http://www.unicode.org/reports/tr35/).", "type": "string"}, "largestChangeId": {"description": "The largest change id.", "format": "int64", "type": "string"}, "maxUploadSizes": {"description": "List of max upload sizes for each file type. The most specific type takes precedence.", "items": {"properties": {"size": {"description": "The max upload size for this type.", "format": "int64", "type": "string"}, "type": {"description": "The file type.", "type": "string"}}, "type": "object"}, "type": "array"}, "name": {"description": "The name of the current user.", "type": "string"}, "permissionId": {"description": "The current user's ID as visible in the permissions collection.", "type": "string"}, "quotaBytesByService": {"description": "The amount of storage quota used by different Google services.", "items": {"properties": {"bytesUsed": {"description": "The storage quota bytes used by the service.", "format": "int64", "type": "string"}, "serviceName": {"description": "The service's name, e.g. DRIVE, GMAIL, or PHOTOS.", "type": "string"}}, "type": "object"}, "type": "array"}, "quotaBytesTotal": {"description": "The total number of quota bytes. This is only relevant when quotaType is LIMITED.", "format": "int64", "type": "string"}, "quotaBytesUsed": {"description": "The number of quota bytes used by Google Drive.", "format": "int64", "type": "string"}, "quotaBytesUsedAggregate": {"description": "The number of quota bytes used by all Google apps (Drive, Picasa, etc.).", "format": "int64", "type": "string"}, "quotaBytesUsedInTrash": {"description": "The number of quota bytes used by trashed items.", "format": "int64", "type": "string"}, "quotaType": {"description": "The type of the user's storage quota. Possible values are: * `LIMITED` * `UNLIMITED`", "type": "string"}, "remainingChangeIds": {"description": "The number of remaining change ids, limited to no more than 2500.", "format": "int64", "type": "string"}, "rootFolderId": {"description": "The id of the root folder.", "type": "string"}, "selfLink": {"description": "A link back to this item.", "type": "string"}, "teamDriveThemes": {"deprecated": true, "description": "Deprecated: Use `driveThemes` instead.", "items": {"properties": {"backgroundImageLink": {"deprecated": true, "description": "Deprecated: Use `driveThemes/backgroundImageLink` instead.", "type": "string"}, "colorRgb": {"deprecated": true, "description": "Deprecated: Use `driveThemes/colorRgb` instead.", "type": "string"}, "id": {"deprecated": true, "description": "Deprecated: Use `driveThemes/id` instead.", "type": "string"}}, "type": "object"}, "type": "array"}, "user": {"$ref": "User", "description": "The authenticated user."}}, "type": "object"}, "App": {"description": "The apps resource provides a list of the apps that a user has installed, with information about each app's supported MIME types, file extensions, and other details. Some resource methods (such as `apps.get`) require an `appId`. Use the `apps.list` method to retrieve the ID for an installed application.", "id": "App", "properties": {"authorized": {"description": "Whether the app is authorized to access data on the user's Drive.", "type": "boolean"}, "createInFolderTemplate": {"description": "The template url to create a new file with this app in a given folder. The template will contain {folderId} to be replaced by the folder to create the new file in.", "type": "string"}, "createUrl": {"description": "The url to create a new file with this app.", "type": "string"}, "hasDriveWideScope": {"description": "Whether the app has drive-wide scope. An app with drive-wide scope can access all files in the user's drive.", "type": "boolean"}, "icons": {"description": "The various icons for the app.", "items": {"properties": {"category": {"description": "Category of the icon. Allowed values are: * `application` - icon for the application * `document` - icon for a file associated with the app * `documentShared` - icon for a shared file associated with the app", "type": "string"}, "iconUrl": {"description": "URL for the icon.", "type": "string"}, "size": {"description": "Size of the icon. Represented as the maximum of the width and height.", "format": "int32", "type": "integer"}}, "type": "object"}, "type": "array"}, "id": {"description": "The ID of the app.", "type": "string"}, "installed": {"description": "Whether the app is installed.", "type": "boolean"}, "kind": {"default": "drive#app", "description": "This is always `drive#app`.", "type": "string"}, "longDescription": {"description": "A long description of the app.", "type": "string"}, "name": {"description": "The name of the app.", "type": "string"}, "objectType": {"description": "The type of object this app creates (e.g. Chart). If empty, the app name should be used instead.", "type": "string"}, "openUrlTemplate": {"description": "The template url for opening files with this app. The template will contain `{ids}` and/or `{exportIds}` to be replaced by the actual file ids. See Open Files for the full documentation.", "type": "string"}, "primaryFileExtensions": {"description": "The list of primary file extensions.", "items": {"type": "string"}, "type": "array"}, "primaryMimeTypes": {"description": "The list of primary mime types.", "items": {"type": "string"}, "type": "array"}, "productId": {"description": "The ID of the product listing for this app.", "type": "string"}, "productUrl": {"description": "A link to the product listing for this app.", "type": "string"}, "secondaryFileExtensions": {"description": "The list of secondary file extensions.", "items": {"type": "string"}, "type": "array"}, "secondaryMimeTypes": {"description": "The list of secondary mime types.", "items": {"type": "string"}, "type": "array"}, "shortDescription": {"description": "A short description of the app.", "type": "string"}, "supportsCreate": {"description": "Whether this app supports creating new objects.", "type": "boolean"}, "supportsImport": {"description": "Whether this app supports importing from Docs Editors.", "type": "boolean"}, "supportsMultiOpen": {"description": "Whether this app supports opening more than one file.", "type": "boolean"}, "supportsOfflineCreate": {"description": "Whether this app supports creating new files when offline.", "type": "boolean"}, "useByDefault": {"description": "Whether the app is selected as the default handler for the types it supports.", "type": "boolean"}}, "type": "object"}, "AppList": {"description": "A list of third-party applications which the user has installed or given access to Google Drive.", "id": "AppList", "properties": {"defaultAppIds": {"description": "List of app IDs that the user has specified to use by default. The list is in reverse-priority order (lowest to highest).", "items": {"type": "string"}, "type": "array"}, "etag": {"description": "The ETag of the list.", "type": "string"}, "items": {"description": "The list of apps.", "items": {"$ref": "App"}, "type": "array"}, "kind": {"default": "drive#appList", "description": "This is always `drive#appList`.", "type": "string"}, "selfLink": {"description": "A link back to this list.", "type": "string"}}, "type": "object"}, "Change": {"description": "Representation of a change to a file or shared drive.", "id": "Change", "properties": {"changeType": {"description": "The type of the change. Possible values are `file` and `drive`.", "type": "string"}, "deleted": {"description": "Whether the file or shared drive has been removed from this list of changes, for example by deletion or loss of access.", "type": "boolean"}, "drive": {"$ref": "Drive", "description": "The updated state of the shared drive. Present if the changeType is drive, the user is still a member of the shared drive, and the shared drive has not been deleted."}, "driveId": {"description": "The ID of the shared drive associated with this change.", "type": "string"}, "file": {"$ref": "File", "description": "The updated state of the file. Present if the type is file and the file has not been removed from this list of changes."}, "fileId": {"description": "The ID of the file associated with this change.", "type": "string"}, "id": {"description": "The ID of the change.", "format": "int64", "type": "string"}, "kind": {"default": "drive#change", "description": "This is always `drive#change`.", "type": "string"}, "modificationDate": {"description": "The time of this modification.", "format": "date-time", "type": "string"}, "selfLink": {"description": "A link back to this change.", "type": "string"}, "teamDrive": {"$ref": "TeamDrive", "deprecated": true, "description": "Deprecated: Use `drive` instead."}, "teamDriveId": {"deprecated": true, "description": "Deprecated: Use `driveId` instead.", "type": "string"}, "type": {"deprecated": true, "description": "Deprecated: Use `changeType` instead.", "type": "string"}}, "type": "object"}, "ChangeList": {"description": "A list of changes for a user.", "id": "ChangeList", "properties": {"etag": {"description": "The ETag of the list.", "type": "string"}, "items": {"description": "The list of changes. If nextPageToken is populated, then this list may be incomplete and an additional page of results should be fetched.", "items": {"$ref": "Change"}, "type": "array"}, "kind": {"default": "drive#changeList", "description": "This is always `drive#changeList`.", "type": "string"}, "largestChangeId": {"description": "The current largest change ID.", "format": "int64", "type": "string"}, "newStartPageToken": {"description": "The starting page token for future changes. This will be present only if the end of the current changes list has been reached.", "type": "string"}, "nextLink": {"description": "A link to the next page of changes.", "type": "string"}, "nextPageToken": {"description": "The page token for the next page of changes. This will be absent if the end of the changes list has been reached. If the token is rejected for any reason, it should be discarded, and pagination should be restarted from the first page of results.", "type": "string"}, "selfLink": {"description": "A link back to this list.", "type": "string"}}, "type": "object"}, "Channel": {"description": "A notification channel used to watch for resource changes.", "id": "Channel", "properties": {"address": {"description": "The address where notifications are delivered for this channel.", "type": "string"}, "expiration": {"description": "Date and time of notification channel expiration, expressed as a Unix timestamp, in milliseconds. Optional.", "format": "int64", "type": "string"}, "id": {"description": "A UUID or similar unique string that identifies this channel.", "type": "string"}, "kind": {"default": "api#channel", "description": "Identifies this as a notification channel used to watch for changes to a resource, which is `api#channel`.", "type": "string"}, "params": {"additionalProperties": {"type": "string"}, "description": "Additional parameters controlling delivery channel behavior. Optional.", "type": "object"}, "payload": {"description": "A Boolean value to indicate whether payload is wanted. Optional.", "type": "boolean"}, "resourceId": {"description": "An opaque ID that identifies the resource being watched on this channel. Stable across different API versions.", "type": "string"}, "resourceUri": {"description": "A version-specific identifier for the watched resource.", "type": "string"}, "token": {"description": "An arbitrary string delivered to the target address with each notification delivered over this channel. Optional.", "type": "string"}, "type": {"description": "The type of delivery mechanism used for this channel. Valid values are \"web_hook\" or \"webhook\".", "type": "string"}}, "type": "object"}, "ChildList": {"description": "A list of children of a file.", "id": "ChildList", "properties": {"etag": {"description": "The ETag of the list.", "type": "string"}, "items": {"description": "The list of children. If nextPageToken is populated, then this list may be incomplete and an additional page of results should be fetched.", "items": {"$ref": "ChildReference"}, "type": "array"}, "kind": {"default": "drive#childList", "description": "This is always `drive#childList`.", "type": "string"}, "nextLink": {"description": "A link to the next page of children.", "type": "string"}, "nextPageToken": {"description": "The page token for the next page of children. This will be absent if the end of the children list has been reached. If the token is rejected for any reason, it should be discarded, and pagination should be restarted from the first page of results.", "type": "string"}, "selfLink": {"description": "A link back to this list.", "type": "string"}}, "type": "object"}, "ChildReference": {"description": "A reference to a folder's child. Some resource methods (such as `children.get`) require a `childId`. Use the `children.list` method to retrieve the ID of the child.", "id": "ChildReference", "properties": {"childLink": {"description": "Output only. A link to the child.", "type": "string"}, "id": {"annotations": {"required": ["drive.children.insert"]}, "description": "The ID of the child.", "type": "string"}, "kind": {"default": "drive#childReference", "description": "Output only. This is always `drive#childReference`.", "type": "string"}, "selfLink": {"description": "Output only. A link back to this reference.", "type": "string"}}, "type": "object"}, "Comment": {"description": "A comment on a file in Google Drive. Some resource methods (such as `comments.update`) require a `commentId`. Use the `comments.list` method to retrieve the ID for a comment in a file.", "id": "Comment", "properties": {"anchor": {"description": "A region of the document represented as a JSON string. For details on defining anchor properties, refer to [Add comments and replies](https://developers.google.com/workspace/drive/api/v2/manage-comments).", "type": "string"}, "author": {"$ref": "User", "description": "Output only. The author of the comment. The author's email address and permission ID will not be populated."}, "commentId": {"description": "Output only. The ID of the comment.", "type": "string"}, "content": {"annotations": {"required": ["drive.comments.insert", "drive.comments.patch", "drive.comments.update"]}, "description": "The plain text content used to create this comment. This is not HTML safe and should only be used as a starting point to make edits to a comment's content.", "type": "string"}, "context": {"description": "The context of the file which is being commented on.", "properties": {"type": {"description": "The MIME type of the context snippet.", "type": "string"}, "value": {"description": "Data representation of the segment of the file being commented on. In the case of a text file for example, this would be the actual text that the comment is about.", "type": "string"}}, "type": "object"}, "createdDate": {"description": "The date when this comment was first created.", "format": "date-time", "type": "string"}, "deleted": {"description": "Output only. Whether this comment has been deleted. If a comment has been deleted the content will be cleared and this will only represent a comment that once existed.", "type": "boolean"}, "fileId": {"description": "Output only. The file which this comment is addressing.", "type": "string"}, "fileTitle": {"description": "Output only. The title of the file which this comment is addressing.", "type": "string"}, "htmlContent": {"description": "Output only. HTML formatted content for this comment.", "type": "string"}, "kind": {"default": "drive#comment", "description": "Output only. This is always `drive#comment`.", "type": "string"}, "modifiedDate": {"description": "The date when this comment or any of its replies were last modified.", "format": "date-time", "type": "string"}, "replies": {"description": "Output only. Replies to this post.", "items": {"$ref": "CommentReply"}, "type": "array"}, "selfLink": {"description": "Output only. A link back to this comment.", "type": "string"}, "status": {"description": "Output only. The status of this comment. Status can be changed by posting a reply to a comment with the desired status. * `open` - The comment is still open. * `resolved` - The comment has been resolved by one of its replies.", "type": "string"}}, "type": "object"}, "CommentList": {"description": "A list of comments on a file in Google Drive.", "id": "CommentList", "properties": {"items": {"description": "The list of comments. If nextPageToken is populated, then this list may be incomplete and an additional page of results should be fetched.", "items": {"$ref": "Comment"}, "type": "array"}, "kind": {"default": "drive#commentList", "description": "This is always `drive#commentList`.", "type": "string"}, "nextLink": {"description": "A link to the next page of comments.", "type": "string"}, "nextPageToken": {"description": "The page token for the next page of comments. This will be absent if the end of the comments list has been reached. If the token is rejected for any reason, it should be discarded, and pagination should be restarted from the first page of results.", "type": "string"}, "selfLink": {"description": "A link back to this list.", "type": "string"}}, "type": "object"}, "CommentReply": {"description": "A comment on a file in Google Drive. Some resource methods (such as `replies.update`) require a `replyId`. Use the `replies.list` method to retrieve the ID for a reply.", "id": "CommentReply", "properties": {"author": {"$ref": "User", "description": "Output only. The author of the reply. The author's email address and permission ID will not be populated."}, "content": {"annotations": {"required": ["drive.replies.patch", "drive.replies.update"]}, "description": "The plain text content used to create this reply. This is not HTML safe and should only be used as a starting point to make edits to a reply's content. This field is required on inserts if no verb is specified (resolve/reopen).", "type": "string"}, "createdDate": {"description": "The date when this reply was first created.", "format": "date-time", "type": "string"}, "deleted": {"description": "Output only. Whether this reply has been deleted. If a reply has been deleted the content will be cleared and this will only represent a reply that once existed.", "type": "boolean"}, "htmlContent": {"description": "Output only. HTML formatted content for this reply.", "type": "string"}, "kind": {"default": "drive#commentReply", "description": "Output only. This is always `drive#commentReply`.", "type": "string"}, "modifiedDate": {"description": "The date when this reply was last modified.", "format": "date-time", "type": "string"}, "replyId": {"description": "Output only. The ID of the reply.", "type": "string"}, "verb": {"description": "The action this reply performed to the parent comment. When creating a new reply this is the action to be perform to the parent comment. Possible values are: * `resolve` - To resolve a comment. * `reopen` - To reopen (un-resolve) a comment.", "type": "string"}}, "type": "object"}, "CommentReplyList": {"description": "A list of replies to a comment on a file in Google Drive.", "id": "CommentReplyList", "properties": {"items": {"description": "The list of replies. If nextPageToken is populated, then this list may be incomplete and an additional page of results should be fetched.", "items": {"$ref": "CommentReply"}, "type": "array"}, "kind": {"default": "drive#commentReplyList", "description": "This is always `drive#commentReplyList`.", "type": "string"}, "nextLink": {"description": "A link to the next page of replies.", "type": "string"}, "nextPageToken": {"description": "The page token for the next page of replies. This will be absent if the end of the replies list has been reached. If the token is rejected for any reason, it should be discarded, and pagination should be restarted from the first page of results.", "type": "string"}, "selfLink": {"description": "A link back to this list.", "type": "string"}}, "type": "object"}, "ContentRestriction": {"description": "A restriction for accessing the content of the file.", "id": "ContentRestriction", "properties": {"ownerRestricted": {"description": "Whether the content restriction can only be modified or removed by a user who owns the file. For files in shared drives, any user with `organizer` capabilities can modify or remove this content restriction.", "type": "boolean"}, "readOnly": {"description": "Whether the content of the file is read-only. If a file is read-only, a new revision of the file may not be added, comments may not be added or modified, and the title of the file may not be modified.", "type": "boolean"}, "reason": {"description": "Reason for why the content of the file is restricted. This is only mutable on requests that also set `readOnly=true`.", "type": "string"}, "restrictingUser": {"$ref": "User", "description": "Output only. The user who set the content restriction. Only populated if `readOnly` is true."}, "restrictionDate": {"description": "The time at which the content restriction was set (formatted RFC 3339 timestamp). Only populated if readOnly is true.", "format": "date-time", "type": "string"}, "systemRestricted": {"description": "Output only. Whether the content restriction was applied by the system, for example due to an esignature. Users cannot modify or remove system restricted content restrictions.", "type": "boolean"}, "type": {"description": "Output only. The type of the content restriction. Currently the only possible value is `globalContentRestriction`.", "type": "string"}}, "type": "object"}, "Drive": {"description": "Representation of a shared drive. Some resource methods (such as `drives.update`) require a `driveId`. Use the `drives.list` method to retrieve the ID for a shared drive.", "id": "Drive", "properties": {"backgroundImageFile": {"description": "An image file and cropping parameters from which a background image for this shared drive is set. This is a write only field; it can only be set on `drive.drives.update` requests that don't set `themeId`. When specified, all fields of the `backgroundImageFile` must be set.", "properties": {"id": {"description": "The ID of an image file in Google Drive to use for the background image.", "type": "string"}, "width": {"description": "The width of the cropped image in the closed range of 0 to 1. This value represents the width of the cropped image divided by the width of the entire image. The height is computed by applying a width to height aspect ratio of 80 to 9. The resulting image must be at least 1280 pixels wide and 144 pixels high.", "format": "float", "type": "number"}, "xCoordinate": {"description": "The X coordinate of the upper left corner of the cropping area in the background image. This is a value in the closed range of 0 to 1. This value represents the horizontal distance from the left side of the entire image to the left side of the cropping area divided by the width of the entire image.", "format": "float", "type": "number"}, "yCoordinate": {"description": "The Y coordinate of the upper left corner of the cropping area in the background image. This is a value in the closed range of 0 to 1. This value represents the vertical distance from the top side of the entire image to the top side of the cropping area divided by the height of the entire image.", "format": "float", "type": "number"}}, "type": "object"}, "backgroundImageLink": {"description": "Output only. A short-lived link to this shared drive's background image.", "type": "string"}, "capabilities": {"description": "Output only. Capabilities the current user has on this shared drive.", "properties": {"canAddChildren": {"description": "Output only. Whether the current user can add children to folders in this shared drive.", "type": "boolean"}, "canChangeCopyRequiresWriterPermissionRestriction": {"description": "Output only. Whether the current user can change the `copyRequiresWriterPermission` restriction of this shared drive.", "type": "boolean"}, "canChangeDomainUsersOnlyRestriction": {"description": "Output only. Whether the current user can change the `domainUsersOnly` restriction of this shared drive.", "type": "boolean"}, "canChangeDriveBackground": {"description": "Output only. Whether the current user can change the background of this shared drive.", "type": "boolean"}, "canChangeDriveMembersOnlyRestriction": {"description": "Output only. Whether the current user can change the `driveMembersOnly` restriction of this shared drive.", "type": "boolean"}, "canChangeSharingFoldersRequiresOrganizerPermissionRestriction": {"description": "Output only. Whether the current user can change the `sharingFoldersRequiresOrganizerPermission` restriction of this shared drive.", "type": "boolean"}, "canComment": {"description": "Output only. Whether the current user can comment on files in this shared drive.", "type": "boolean"}, "canCopy": {"description": "Output only. Whether the current user can copy files in this shared drive.", "type": "boolean"}, "canDeleteChildren": {"description": "Output only. Whether the current user can delete children from folders in this shared drive.", "type": "boolean"}, "canDeleteDrive": {"description": "Output only. Whether the current user can delete this shared drive. Attempting to delete the shared drive may still fail if there are untrashed items inside the shared drive.", "type": "boolean"}, "canDownload": {"description": "Output only. Whether the current user can download files in this shared drive.", "type": "boolean"}, "canEdit": {"description": "Output only. Whether the current user can edit files in this shared drive", "type": "boolean"}, "canListChildren": {"description": "Output only. Whether the current user can list the children of folders in this shared drive.", "type": "boolean"}, "canManageMembers": {"description": "Output only. Whether the current user can add members to this shared drive or remove them or change their role.", "type": "boolean"}, "canReadRevisions": {"description": "Output only. Whether the current user can read the revisions resource of files in this shared drive.", "type": "boolean"}, "canRename": {"description": "Output only. Whether the current user can rename files or folders in this shared drive.", "type": "boolean"}, "canRenameDrive": {"description": "Output only. Whether the current user can rename this shared drive.", "type": "boolean"}, "canResetDriveRestrictions": {"description": "Output only. Whether the current user can reset the shared drive restrictions to defaults.", "type": "boolean"}, "canShare": {"description": "Output only. Whether the current user can share files or folders in this shared drive.", "type": "boolean"}, "canTrashChildren": {"description": "Output only. Whether the current user can trash children from folders in this shared drive.", "type": "boolean"}}, "type": "object"}, "colorRgb": {"description": "The color of this shared drive as an RGB hex string. It can only be set on a `drive.drives.update` request that does not set `themeId`.", "type": "string"}, "createdDate": {"description": "The time at which the shared drive was created (RFC 3339 date-time).", "format": "date-time", "type": "string"}, "hidden": {"description": "Whether the shared drive is hidden from default view.", "type": "boolean"}, "id": {"description": "Output only. The ID of this shared drive which is also the ID of the top level folder of this shared drive.", "type": "string"}, "kind": {"default": "drive#drive", "description": "Output only. This is always `drive#drive`", "type": "string"}, "name": {"annotations": {"required": ["drive.drives.insert"]}, "description": "The name of this shared drive.", "type": "string"}, "orgUnitId": {"description": "Output only. The organizational unit of this shared drive. This field is only populated on `drives.list` responses when the `useDomainAdminAccess` parameter is set to `true`.", "type": "string"}, "restrictions": {"description": "A set of restrictions that apply to this shared drive or items inside this shared drive.", "properties": {"adminManagedRestrictions": {"description": "Whether administrative privileges on this shared drive are required to modify restrictions.", "type": "boolean"}, "copyRequiresWriterPermission": {"description": "Whether the options to copy, print, or download files inside this shared drive, should be disabled for readers and commenters. When this restriction is set to `true`, it will override the similarly named field to `true` for any file inside this shared drive.", "type": "boolean"}, "domainUsersOnly": {"description": "Whether access to this shared drive and items inside this shared drive is restricted to users of the domain to which this shared drive belongs. This restriction may be overridden by other sharing policies controlled outside of this shared drive.", "type": "boolean"}, "driveMembersOnly": {"description": "Whether access to items inside this shared drive is restricted to its members.", "type": "boolean"}, "sharingFoldersRequiresOrganizerPermission": {"description": "If true, only users with the organizer role can share folders. If false, users with either the organizer role or the file organizer role can share folders.", "type": "boolean"}}, "type": "object"}, "themeId": {"description": "The ID of the theme from which the background image and color will be set. The set of possible `driveThemes` can be retrieved from a `drive.about.get` response. When not specified on a `drive.drives.insert` request, a random theme is chosen from which the background image and color are set. This is a write-only field; it can only be set on requests that don't set `colorRgb` or `backgroundImageFile`.", "type": "string"}}, "type": "object"}, "DriveList": {"description": "A list of shared drives.", "id": "DriveList", "properties": {"items": {"description": "The list of shared drives. If nextPageToken is populated, then this list may be incomplete and an additional page of results should be fetched.", "items": {"$ref": "Drive"}, "type": "array"}, "kind": {"default": "drive#driveList", "description": "This is always `drive#driveList`", "type": "string"}, "nextPageToken": {"description": "The page token for the next page of shared drives. This will be absent if the end of the list has been reached. If the token is rejected for any reason, it should be discarded, and pagination should be restarted from the first page of results.", "type": "string"}}, "type": "object"}, "File": {"description": "The metadata for a file. Some resource methods (such as `files.update`) require a `fileId`. Use the `files.list` method to retrieve the ID for a file.", "id": "File", "properties": {"alternateLink": {"description": "Output only. A link for opening the file in a relevant Google editor or viewer.", "type": "string"}, "appDataContents": {"description": "Output only. Whether this file is in the Application Data folder.", "type": "boolean"}, "canComment": {"deprecated": true, "description": "Output only. Deprecated: Use `capabilities/canComment` instead.", "type": "boolean"}, "canReadRevisions": {"deprecated": true, "description": "Output only. Deprecated: Use `capabilities/canReadRevisions` instead.", "type": "boolean"}, "capabilities": {"description": "Output only. Capabilities the current user has on this file. Each capability corresponds to a fine-grained action that a user may take.", "properties": {"canAcceptOwnership": {"description": "Output only. Whether the current user is the pending owner of the file. Not populated for shared drive files.", "type": "boolean"}, "canAddChildren": {"description": "Output only. Whether the current user can add children to this folder. This is always false when the item is not a folder.", "type": "boolean"}, "canAddFolderFromAnotherDrive": {"description": "Output only. Whether the current user can add a folder from another drive (different shared drive or My Drive) to this folder. This is false when the item is not a folder. Only populated for items in shared drives.", "type": "boolean"}, "canAddMyDriveParent": {"description": "Output only. Whether the current user can add a parent for the item without removing an existing parent in the same request. Not populated for shared drive files.", "type": "boolean"}, "canChangeCopyRequiresWriterPermission": {"description": "Output only. Whether the current user can change the `copyRequiresWriterPermission` restriction of this file.", "type": "boolean"}, "canChangeRestrictedDownload": {"deprecated": true, "description": "Output only. Deprecated.", "type": "boolean"}, "canChangeSecurityUpdateEnabled": {"description": "Output only. Whether the current user can change the securityUpdateEnabled field on link share metadata.", "type": "boolean"}, "canComment": {"description": "Output only. Whether the current user can comment on this file.", "type": "boolean"}, "canCopy": {"description": "Output only. Whether the current user can copy this file. For an item in a shared drive, whether the current user can copy non-folder descendants of this item, or this item itself if it is not a folder.", "type": "boolean"}, "canDelete": {"description": "Output only. Whether the current user can delete this file.", "type": "boolean"}, "canDeleteChildren": {"description": "Output only. Whether the current user can delete children of this folder. This is false when the item is not a folder. Only populated for items in shared drives.", "type": "boolean"}, "canDisableInheritedPermissions": {"description": "Output only. Whether a user can disable inherited permissions.", "readOnly": true, "type": "boolean"}, "canDownload": {"description": "Output only. Whether the current user can download this file.", "type": "boolean"}, "canEdit": {"description": "Output only. Whether the current user can edit this file. Other factors may limit the type of changes a user can make to a file. For example, see `canChangeCopyRequiresWriterPermission` or `canModifyContent`.", "type": "boolean"}, "canEnableInheritedPermissions": {"description": "Output only. Whether a user can re-enable inherited permissions.", "readOnly": true, "type": "boolean"}, "canListChildren": {"description": "Output only. Whether the current user can list the children of this folder. This is always false when the item is not a folder.", "type": "boolean"}, "canModifyContent": {"description": "Output only. Whether the current user can modify the content of this file.", "type": "boolean"}, "canModifyContentRestriction": {"deprecated": true, "description": "Deprecated: Output only. Use one of `canModifyEditorContentRestriction`, `canModifyOwnerContentRestriction` or `canRemoveContentRestriction`.", "type": "boolean"}, "canModifyEditorContentRestriction": {"description": "Output only. Whether the current user can add or modify content restrictions on the file which are editor restricted.", "type": "boolean"}, "canModifyLabels": {"description": "Output only. Whether the current user can modify the labels on the file.", "type": "boolean"}, "canModifyOwnerContentRestriction": {"description": "Output only. Whether the current user can add or modify content restrictions which are owner restricted.", "type": "boolean"}, "canMoveChildrenOutOfDrive": {"description": "Output only. Whether the current user can move children of this folder outside of the shared drive. This is false when the item is not a folder. Only populated for items in shared drives.", "type": "boolean"}, "canMoveChildrenOutOfTeamDrive": {"deprecated": true, "description": "Output only. Deprecated: Use `canMoveChildrenOutOfDrive` instead.", "type": "boolean"}, "canMoveChildrenWithinDrive": {"description": "Output only. Whether the current user can move children of this folder within this drive. This is false when the item is not a folder. Note that a request to move the child may still fail depending on the current user's access to the child and to the destination folder.", "type": "boolean"}, "canMoveChildrenWithinTeamDrive": {"deprecated": true, "description": "Output only. Deprecated: Use `canMoveChildrenWithinDrive` instead.", "type": "boolean"}, "canMoveItemIntoTeamDrive": {"deprecated": true, "description": "Output only. Deprecated: Use `canMoveItemOutOfDrive` instead.", "type": "boolean"}, "canMoveItemOutOfDrive": {"description": "Output only. Whether the current user can move this item outside of this drive by changing its parent. Note that a request to change the parent of the item may still fail depending on the new parent that is being added.", "type": "boolean"}, "canMoveItemOutOfTeamDrive": {"deprecated": true, "description": "Output only. Deprecated: Use `canMoveItemOutOfDrive` instead.", "type": "boolean"}, "canMoveItemWithinDrive": {"description": "Output only. Whether the current user can move this item within this drive. Note that a request to change the parent of the item may still fail depending on the new parent that is being added and the parent that is being removed.", "type": "boolean"}, "canMoveItemWithinTeamDrive": {"deprecated": true, "description": "Output only. Deprecated: Use `canMoveItemWithinDrive` instead.", "type": "boolean"}, "canMoveTeamDriveItem": {"deprecated": true, "description": "Output only. Deprecated: Use `canMoveItemWithinDrive` or `canMoveItemOutOfDrive` instead.", "type": "boolean"}, "canReadDrive": {"description": "Output only. Whether the current user can read the shared drive to which this file belongs. Only populated for items in shared drives.", "type": "boolean"}, "canReadLabels": {"description": "Output only. Whether the current user can read the labels on the file.", "type": "boolean"}, "canReadRevisions": {"description": "Output only. Whether the current user can read the revisions resource of this file. For a shared drive item, whether revisions of non-folder descendants of this item, or this item itself if it is not a folder, can be read.", "type": "boolean"}, "canReadTeamDrive": {"deprecated": true, "description": "Output only. Deprecated: Use `canReadDrive` instead.", "type": "boolean"}, "canRemoveChildren": {"description": "Output only. Whether the current user can remove children from this folder. This is always false when the item is not a folder. For a folder in a shared drive, use `canDeleteChildren` or `canTrashChildren` instead.", "type": "boolean"}, "canRemoveContentRestriction": {"description": "Output only. Whether there is a content restriction on the file that can be removed by the current user.", "type": "boolean"}, "canRemoveMyDriveParent": {"description": "Output only. Whether the current user can remove a parent from the item without adding another parent in the same request. Not populated for shared drive files.", "type": "boolean"}, "canRename": {"description": "Output only. Whether the current user can rename this file.", "type": "boolean"}, "canShare": {"description": "Output only. Whether the current user can modify the sharing settings for this file.", "type": "boolean"}, "canTrash": {"description": "Output only. Whether the current user can move this file to trash.", "type": "boolean"}, "canTrashChildren": {"description": "Output only. Whether the current user can trash children of this folder. This is false when the item is not a folder. Only populated for items in shared drives.", "type": "boolean"}, "canUntrash": {"description": "Output only. Whether the current user can restore this file from trash.", "type": "boolean"}}, "type": "object"}, "contentRestrictions": {"description": "Restrictions for accessing the content of the file. Only populated if such a restriction exists.", "items": {"$ref": "ContentRestriction"}, "type": "array"}, "copyRequiresWriterPermission": {"description": "Whether the options to copy, print, or download this file, should be disabled for readers and commenters.", "type": "boolean"}, "copyable": {"deprecated": true, "description": "Output only. Deprecated: Use `capabilities/canCopy` instead.", "type": "boolean"}, "createdDate": {"description": "Create time for this file (formatted RFC 3339 timestamp).", "format": "date-time", "type": "string"}, "defaultOpenWithLink": {"description": "Output only. A link to open this file with the user's default app for this file. Only populated when the drive.apps.readonly scope is used.", "type": "string"}, "description": {"description": "A short description of the file.", "type": "string"}, "downloadUrl": {"description": "Output only. Short lived download URL for the file. This field is only populated for files with content stored in Google Drive; it is not populated for Google Docs or shortcut files.", "type": "string"}, "driveId": {"description": "Output only. ID of the shared drive the file resides in. Only populated for items in shared drives.", "type": "string"}, "editable": {"deprecated": true, "description": "Output only. Deprecated: Use `capabilities/canEdit` instead.", "type": "boolean"}, "embedLink": {"description": "Output only. A link for embedding the file.", "type": "string"}, "etag": {"description": "Output only. ETag of the file.", "type": "string"}, "explicitlyTrashed": {"description": "Output only. Whether this file has been explicitly trashed, as opposed to recursively trashed.", "type": "boolean"}, "exportLinks": {"additionalProperties": {"type": "string"}, "description": "Output only. Links for exporting Docs Editors files to specific formats.", "readOnly": true, "type": "object"}, "fileExtension": {"description": "Output only. The final component of `fullFileExtension` with trailing text that does not appear to be part of the extension removed. This field is only populated for files with content stored in Google Drive; it is not populated for Docs Editors or shortcut files.", "type": "string"}, "fileSize": {"description": "Output only. Size in bytes of blobs and first party editor files. Won't be populated for files that have no size, like shortcuts and folders.", "format": "int64", "type": "string"}, "folderColorRgb": {"description": "Folder color as an RGB hex string if the file is a folder or a shortcut to a folder. The list of supported colors is available in the folderColorPalette field of the About resource. If an unsupported color is specified, it will be changed to the closest color in the palette.", "type": "string"}, "fullFileExtension": {"description": "Output only. The full file extension; extracted from the title. May contain multiple concatenated extensions, such as \"tar.gz\". Removing an extension from the title does not clear this field; however, changing the extension on the title does update this field. This field is only populated for files with content stored in Google Drive; it is not populated for Docs Editors or shortcut files.", "type": "string"}, "hasAugmentedPermissions": {"description": "Output only. Whether there are permissions directly on this file. This field is only populated for items in shared drives.", "type": "boolean"}, "hasThumbnail": {"description": "Output only. Whether this file has a thumbnail. This does not indicate whether the requesting app has access to the thumbnail. To check access, look for the presence of the thumbnailLink field.", "type": "boolean"}, "headRevisionId": {"description": "Output only. The ID of the file's head revision. This field is only populated for files with content stored in Google Drive; it is not populated for Docs Editors or shortcut files.", "type": "string"}, "iconLink": {"description": "Output only. A link to the file's icon.", "type": "string"}, "id": {"description": "The ID of the file.", "type": "string"}, "imageMediaMetadata": {"description": "Output only. Metadata about image media. This will only be present for image types, and its contents will depend on what can be parsed from the image content.", "properties": {"aperture": {"description": "Output only. The aperture used to create the photo (f-number).", "format": "float", "type": "number"}, "cameraMake": {"description": "Output only. The make of the camera used to create the photo.", "type": "string"}, "cameraModel": {"description": "Output only. The model of the camera used to create the photo.", "type": "string"}, "colorSpace": {"description": "Output only. The color space of the photo.", "type": "string"}, "date": {"description": "Output only. The date and time the photo was taken (EXIF format timestamp).", "type": "string"}, "exposureBias": {"description": "Output only. The exposure bias of the photo (APEX value).", "format": "float", "type": "number"}, "exposureMode": {"description": "Output only. The exposure mode used to create the photo.", "type": "string"}, "exposureTime": {"description": "Output only. The length of the exposure, in seconds.", "format": "float", "type": "number"}, "flashUsed": {"description": "Output only. Whether a flash was used to create the photo.", "type": "boolean"}, "focalLength": {"description": "Output only. The focal length used to create the photo, in millimeters.", "format": "float", "type": "number"}, "height": {"description": "Output only. The height of the image in pixels.", "format": "int32", "type": "integer"}, "isoSpeed": {"description": "Output only. The ISO speed used to create the photo.", "format": "int32", "type": "integer"}, "lens": {"description": "Output only. The lens used to create the photo.", "type": "string"}, "location": {"description": "Output only. Geographic location information stored in the image.", "properties": {"altitude": {"description": "Output only. The altitude stored in the image.", "format": "double", "type": "number"}, "latitude": {"description": "Output only. The latitude stored in the image.", "format": "double", "type": "number"}, "longitude": {"description": "Output only. The longitude stored in the image.", "format": "double", "type": "number"}}, "type": "object"}, "maxApertureValue": {"description": "Output only. The smallest f-number of the lens at the focal length used to create the photo (APEX value).", "format": "float", "type": "number"}, "meteringMode": {"description": "Output only. The metering mode used to create the photo.", "type": "string"}, "rotation": {"description": "Output only. The number of clockwise 90 degree rotations applied from the image's original orientation.", "format": "int32", "type": "integer"}, "sensor": {"description": "Output only. The type of sensor used to create the photo.", "type": "string"}, "subjectDistance": {"description": "Output only. The distance to the subject of the photo, in meters.", "format": "int32", "type": "integer"}, "whiteBalance": {"description": "Output only. The white balance mode used to create the photo.", "type": "string"}, "width": {"description": "Output only. The width of the image in pixels.", "format": "int32", "type": "integer"}}, "type": "object"}, "indexableText": {"description": "Indexable text attributes for the file (can only be written)", "properties": {"text": {"description": "The text to be indexed for this file.", "type": "string"}}, "type": "object"}, "inheritedPermissionsDisabled": {"description": "Whether this file has inherited permissions disabled. Inherited permissions are enabled by default.", "type": "boolean"}, "isAppAuthorized": {"description": "Output only. Whether the file was created or opened by the requesting app.", "type": "boolean"}, "kind": {"default": "drive#file", "description": "Output only. The type of file. This is always `drive#file`.", "type": "string"}, "labelInfo": {"description": "Output only. An overview of the labels on the file.", "properties": {"labels": {"description": "Output only. The set of labels on the file as requested by the label IDs in the `include<PERSON><PERSON>ls` parameter. By default, no labels are returned.", "items": {"$ref": "Label"}, "type": "array"}}, "type": "object"}, "labels": {"description": "A group of labels for the file.", "properties": {"hidden": {"deprecated": true, "description": "Output only. Deprecated.", "type": "boolean"}, "modified": {"description": "Output only. Whether the file has been modified by this user.", "type": "boolean"}, "restricted": {"deprecated": true, "description": "Output only. Deprecated: Use `copyRequiresWriterPermission` instead.", "type": "boolean"}, "starred": {"description": "Whether this file is starred by the user.", "type": "boolean"}, "trashed": {"description": "Whether this file has been trashed. This label applies to all users accessing the file; however, only owners are allowed to see and untrash files.", "type": "boolean"}, "viewed": {"description": "Whether this file has been viewed by this user.", "type": "boolean"}}, "type": "object"}, "lastModifyingUser": {"$ref": "User", "description": "Output only. The last user to modify this file. This field is only populated when the last modification was performed by a signed-in user."}, "lastModifyingUserName": {"description": "Output only. Name of the last user to modify this file.", "type": "string"}, "lastViewedByMeDate": {"description": "Last time this file was viewed by the user (formatted RFC 3339 timestamp).", "format": "date-time", "type": "string"}, "linkShareMetadata": {"description": "Contains details about the link URLs that clients are using to refer to this item.", "properties": {"securityUpdateEligible": {"description": "Output only. Whether the file is eligible for security update.", "type": "boolean"}, "securityUpdateEnabled": {"description": "Output only. Whether the security update is enabled for this file.", "type": "boolean"}}, "type": "object"}, "markedViewedByMeDate": {"description": "Deprecated.", "format": "date-time", "type": "string"}, "md5Checksum": {"description": "Output only. An MD5 checksum for the content of this file. This field is only populated for files with content stored in Google Drive; it is not populated for Docs Editors or shortcut files.", "type": "string"}, "mimeType": {"description": "The MIME type of the file. This is only mutable on update when uploading new content. This field can be left blank, and the mimetype will be determined from the uploaded content's MIME type.", "type": "string"}, "modifiedByMeDate": {"description": "Last time this file was modified by the user (formatted RFC 3339 timestamp). Note that setting modifiedDate will also update the modifiedByMe date for the user which set the date.", "format": "date-time", "type": "string"}, "modifiedDate": {"description": "Last time this file was modified by anyone (formatted RFC 3339 timestamp). This is only mutable on update when the setModifiedDate parameter is set.", "format": "date-time", "type": "string"}, "openWithLinks": {"additionalProperties": {"type": "string"}, "description": "Output only. A map of the id of each of the user's apps to a link to open this file with that app. Only populated when the drive.apps.readonly scope is used.", "type": "object"}, "originalFilename": {"description": "The original filename of the uploaded content if available, or else the original value of the `title` field. This is only available for files with binary content in Google Drive.", "type": "string"}, "ownedByMe": {"description": "Output only. Whether the file is owned by the current user. Not populated for items in shared drives.", "type": "boolean"}, "ownerNames": {"description": "Output only. Name(s) of the owner(s) of this file. Not populated for items in shared drives.", "items": {"type": "string"}, "type": "array"}, "owners": {"description": "Output only. The owner of this file. Only certain legacy files may have more than one owner. This field isn't populated for items in shared drives.", "items": {"$ref": "User"}, "type": "array"}, "parents": {"description": "The ID of the parent folder containing the file. A file can only have one parent folder; specifying multiple parents isn't supported. If not specified as part of an insert request, the file is placed directly in the user's My Drive folder. If not specified as part of a copy request, the file inherits any discoverable parent of the source file. Update requests must use the `addParents` and `removeParents` parameters to modify the parents list.", "items": {"$ref": "ParentReference"}, "type": "array"}, "permissionIds": {"description": "Output only. List of permission IDs for users with access to this file.", "items": {"type": "string"}, "type": "array"}, "permissions": {"description": "Output only. The list of permissions for users with access to this file. Not populated for items in shared drives.", "items": {"$ref": "Permission"}, "type": "array"}, "properties": {"description": "The list of properties.", "items": {"$ref": "Property"}, "type": "array"}, "quotaBytesUsed": {"description": "Output only. The number of quota bytes used by this file.", "format": "int64", "type": "string"}, "resourceKey": {"description": "Output only. A key needed to access the item via a shared link.", "type": "string"}, "selfLink": {"description": "Output only. A link back to this file.", "type": "string"}, "sha1Checksum": {"description": "Output only. The SHA1 checksum associated with this file, if available. This field is only populated for files with content stored in Google Drive; it is not populated for Docs Editors or shortcut files.", "type": "string"}, "sha256Checksum": {"description": "Output only. The SHA256 checksum associated with this file, if available. This field is only populated for files with content stored in Google Drive; it is not populated for Docs Editors or shortcut files.", "type": "string"}, "shareable": {"deprecated": true, "description": "Output only. Deprecated: Use `capabilities/canShare` instead.", "type": "boolean"}, "shared": {"description": "Output only. Whether the file has been shared. Not populated for items in shared drives.", "type": "boolean"}, "sharedWithMeDate": {"description": "Time at which this file was shared with the user (formatted RFC 3339 timestamp).", "format": "date-time", "type": "string"}, "sharingUser": {"$ref": "User", "description": "Output only. User that shared the item with the current user, if available."}, "shortcutDetails": {"description": "Shortcut file details. Only populated for shortcut files, which have the mimeType field set to `application/vnd.google-apps.shortcut`. Can only be set on `files.insert` requests.", "properties": {"targetId": {"description": "The ID of the file that this shortcut points to. Can only be set on `files.insert` requests.", "type": "string"}, "targetMimeType": {"description": "Output only. The MIME type of the file that this shortcut points to. The value of this field is a snapshot of the target's MIME type, captured when the shortcut is created.", "type": "string"}, "targetResourceKey": {"description": "Output only. The ResourceKey for the target file.", "type": "string"}}, "type": "object"}, "spaces": {"description": "Output only. The list of spaces which contain the file. Supported values are `drive`, `appDataFolder` and `photos`.", "items": {"type": "string"}, "type": "array"}, "teamDriveId": {"deprecated": true, "description": "Output only. Deprecated: Use `driveId` instead.", "type": "string"}, "thumbnail": {"description": "A thumbnail for the file. This will only be used if a standard thumbnail cannot be generated.", "properties": {"image": {"description": "The URL-safe Base64 encoded bytes of the thumbnail image. It should conform to RFC 4648 section 5.", "format": "byte", "type": "string"}, "mimeType": {"description": "The MIME type of the thumbnail.", "type": "string"}}, "type": "object"}, "thumbnailLink": {"description": "Output only. A short-lived link to the file's thumbnail, if available. Typically lasts on the order of hours. Not intended for direct usage on web applications due to [Cross-Origin Resource Sharing (CORS)](https://developer.mozilla.org/en-US/docs/Web/HTTP/CORS), consider using a proxy server. Only populated when the requesting app can access the file's content. If the file isn't shared publicly, the URL returned in `Files.thumbnailLink` must be fetched using a credentialed request.", "type": "string"}, "thumbnailVersion": {"description": "Output only. The thumbnail version for use in thumbnail cache invalidation.", "format": "int64", "type": "string"}, "title": {"description": "The title of this file. Note that for immutable items such as the top level folders of shared drives, My Drive root folder, and Application Data folder the title is constant.", "type": "string"}, "trashedDate": {"description": "The time that the item was trashed (formatted RFC 3339 timestamp). Only populated for items in shared drives.", "format": "date-time", "type": "string"}, "trashingUser": {"$ref": "User", "description": "Output only. If the file has been explicitly trashed, the user who trashed it. Only populated for items in shared drives."}, "userPermission": {"$ref": "Permission", "description": "Output only. The permissions for the authenticated user on this file."}, "version": {"description": "Output only. A monotonically increasing version number for the file. This reflects every change made to the file on the server, even those not visible to the requesting user.", "format": "int64", "type": "string"}, "videoMediaMetadata": {"description": "Output only. Metadata about video media. This will only be present for video types.", "properties": {"durationMillis": {"description": "Output only. The duration of the video in milliseconds.", "format": "int64", "type": "string"}, "height": {"description": "Output only. The height of the video in pixels.", "format": "int32", "type": "integer"}, "width": {"description": "Output only. The width of the video in pixels.", "format": "int32", "type": "integer"}}, "type": "object"}, "webContentLink": {"description": "Output only. A link for downloading the content of the file in a browser using cookie based authentication. In cases where the content is shared publicly, the content can be downloaded without any credentials.", "type": "string"}, "webViewLink": {"description": "Output only. A link only available on public folders for viewing their static web assets (HTML, CSS, JS, etc) via Google Drive's Website Hosting.", "type": "string"}, "writersCanShare": {"description": "Whether writers can share the document with other users. Not populated for items in shared drives.", "type": "boolean"}}, "type": "object"}, "FileList": {"description": "A list of files.", "id": "FileList", "properties": {"etag": {"description": "The ETag of the list.", "type": "string"}, "incompleteSearch": {"description": "Whether the search process was incomplete. If true, then some search results may be missing, since all documents were not searched. This may occur when searching multiple drives with the \"allDrives\" corpora, but all corpora could not be searched. When this happens, it is suggested that clients narrow their query by choosing a different corpus such as \"default\" or \"drive\".", "type": "boolean"}, "items": {"description": "The list of files. If nextPageToken is populated, then this list may be incomplete and an additional page of results should be fetched.", "items": {"$ref": "File"}, "type": "array"}, "kind": {"default": "drive#fileList", "description": "This is always `drive#fileList`.", "type": "string"}, "nextLink": {"description": "A link to the next page of files.", "type": "string"}, "nextPageToken": {"description": "The page token for the next page of files. This will be absent if the end of the files list has been reached. If the token is rejected for any reason, it should be discarded, and pagination should be restarted from the first page of results.", "type": "string"}, "selfLink": {"description": "A link back to this list.", "type": "string"}}, "type": "object"}, "GeneratedIds": {"description": "A list of generated IDs which can be provided in insert requests", "id": "GeneratedIds", "properties": {"ids": {"description": "The IDs generated for the requesting user in the specified space.", "items": {"type": "string"}, "type": "array"}, "kind": {"default": "drive#generatedIds", "description": "This is always `drive#generatedIds`", "type": "string"}, "space": {"description": "The type of file that can be created with these IDs.", "type": "string"}}, "type": "object"}, "Label": {"description": "Representation of a label and label fields.", "id": "Label", "properties": {"fields": {"additionalProperties": {"$ref": "LabelField"}, "description": "A map of the fields on the label, keyed by the field's ID.", "type": "object"}, "id": {"description": "The ID of the label.", "type": "string"}, "kind": {"default": "drive#label", "description": "This is always `drive#label`", "type": "string"}, "revisionId": {"description": "The revision ID of the label.", "type": "string"}}, "type": "object"}, "LabelField": {"description": "Representation of field, which is a typed key-value pair.", "id": "LabelField", "properties": {"dateString": {"description": "Only present if valueType is dateString. RFC 3339 formatted date: YYYY-MM-DD.", "items": {"format": "date", "type": "string"}, "type": "array"}, "id": {"description": "The identifier of this label field.", "type": "string"}, "integer": {"description": "Only present if `valueType` is `integer`.", "items": {"format": "int64", "type": "string"}, "type": "array"}, "kind": {"default": "drive#labelField", "description": "This is always `drive#labelField`.", "type": "string"}, "selection": {"description": "Only present if `valueType` is `selection`", "items": {"type": "string"}, "type": "array"}, "text": {"description": "Only present if `valueType` is `text`.", "items": {"type": "string"}, "type": "array"}, "user": {"description": "Only present if `valueType` is `user`.", "items": {"$ref": "User"}, "type": "array"}, "valueType": {"description": "The field type. While new values may be supported in the future, the following are currently allowed: * `dateString` * `integer` * `selection` * `text` * `user`", "type": "string"}}, "type": "object"}, "LabelFieldModification": {"description": "A modification to a label's field.", "id": "LabelFieldModification", "properties": {"fieldId": {"description": "The ID of the field to be modified.", "type": "string"}, "kind": {"default": "drive#labelFieldModification", "description": "This is always `drive#labelFieldModification`.", "type": "string"}, "setDateValues": {"description": "Replaces the value of a dateString Field with these new values. The string must be in the RFC 3339 full-date format: YYYY-MM-DD.", "items": {"format": "date", "type": "string"}, "type": "array"}, "setIntegerValues": {"description": "Replaces the value of an `integer` field with these new values.", "items": {"format": "int64", "type": "string"}, "type": "array"}, "setSelectionValues": {"description": "Replaces a `selection` field with these new values.", "items": {"type": "string"}, "type": "array"}, "setTextValues": {"description": "Sets the value of a `text` field.", "items": {"type": "string"}, "type": "array"}, "setUserValues": {"description": "Replaces a `user` field with these new values. The values must be valid email addresses.", "items": {"type": "string"}, "type": "array"}, "unsetValues": {"description": "Unsets the values for this field.", "type": "boolean"}}, "type": "object"}, "LabelList": {"description": "A list of labels applied to a file.", "id": "LabelList", "properties": {"items": {"description": "The list of labels.", "items": {"$ref": "Label"}, "type": "array"}, "kind": {"default": "drive#labelList", "description": "This is always `drive#labelList`", "type": "string"}, "nextPageToken": {"description": "The page token for the next page of labels. This field will be absent if the end of the list has been reached. If the token is rejected for any reason, it should be discarded, and pagination should be restarted from the first page of results.", "type": "string"}}, "type": "object"}, "LabelModification": {"description": "A modification to a label on a file. A LabelModification can be used to apply a label to a file, update an existing label on a file, or remove a label from a file.", "id": "LabelModification", "properties": {"fieldModifications": {"description": "The list of modifications to this label's fields.", "items": {"$ref": "LabelFieldModification"}, "type": "array"}, "kind": {"default": "drive#labelModification", "description": "This is always `drive#labelModification`.", "type": "string"}, "labelId": {"annotations": {"required": ["drive.files.modifyLabels"]}, "description": "The ID of the label to modify.", "type": "string"}, "removeLabel": {"description": "If true, the label will be removed from the file.", "type": "boolean"}}, "type": "object"}, "ModifyLabelsRequest": {"description": "A request to modify the set of labels on a file. This request may contain many modifications that will either all succeed or all fail atomically.", "id": "ModifyLabelsRequest", "properties": {"kind": {"default": "drive#modifyLabelsRequest", "description": "This is always `drive#modifyLabelsRequest`.", "type": "string"}, "labelModifications": {"description": "The list of modifications to apply to the labels on the file.", "items": {"$ref": "LabelModification"}, "type": "array"}}, "type": "object"}, "ModifyLabelsResponse": {"description": "Response to a ModifyLabels request. This contains only those labels which were added or updated by the request.", "id": "ModifyLabelsResponse", "properties": {"kind": {"default": "drive#modifyLabelsResponse", "description": "This is always `drive#modifyLabelsResponse`", "type": "string"}, "modifiedLabels": {"description": "The list of labels which were added or updated by the request.", "items": {"$ref": "Label"}, "type": "array"}}, "type": "object"}, "ParentList": {"description": "A list of a file's parents.", "id": "ParentList", "properties": {"etag": {"description": "The ETag of the list.", "type": "string"}, "items": {"description": "The list of parents.", "items": {"$ref": "ParentReference"}, "type": "array"}, "kind": {"default": "drive#parentList", "description": "This is always `drive#parentList`.", "type": "string"}, "selfLink": {"description": "A link back to this list.", "type": "string"}}, "type": "object"}, "ParentReference": {"description": "A reference to a file's parent. A file can only have one parent folder; specifying multiple parents isn't supported. Some resource methods (such as `parents.get`) require a `parentId`. Use the `parents.list` method to retrieve the ID for a parent.", "id": "ParentReference", "properties": {"id": {"annotations": {"required": ["drive.parents.insert"]}, "description": "The ID of the parent.", "type": "string"}, "isRoot": {"description": "Output only. Whether or not the parent is the root folder.", "type": "boolean"}, "kind": {"default": "drive#parentReference", "description": "Output only. This is always `drive#parentReference`.", "type": "string"}, "parentLink": {"description": "Output only. A link to the parent.", "type": "string"}, "selfLink": {"description": "Output only. A link back to this reference.", "type": "string"}}, "type": "object"}, "Permission": {"description": "A permission for a file. A permission grants a user, group, domain, or the world access to a file or a folder hierarchy. Some resource methods (such as `permissions.update`) require a `permissionId`. Use the `permissions.list` method to retrieve the ID for a file, folder, or shared drive.", "id": "Permission", "properties": {"additionalRoles": {"description": "Additional roles for this user. Only `commenter` is currently allowed, though more may be supported in the future.", "items": {"type": "string"}, "type": "array"}, "authKey": {"deprecated": true, "description": "Output only. Deprecated.", "type": "string"}, "deleted": {"description": "Output only. Whether the account associated with this permission has been deleted. This field only pertains to user and group permissions.", "type": "boolean"}, "domain": {"description": "Output only. The domain name of the entity this permission refers to. This is an output-only field which is present when the permission type is `user`, `group` or `domain`.", "type": "string"}, "emailAddress": {"description": "Output only. The email address of the user or group this permission refers to. This is an output-only field which is present when the permission type is `user` or `group`.", "type": "string"}, "etag": {"description": "Output only. The ETag of the permission.", "type": "string"}, "expirationDate": {"description": "The time at which this permission will expire (RFC 3339 date-time). Expiration dates have the following restrictions: - They can only be set on user and group permissions - The date must be in the future - The date cannot be more than a year in the future - The date can only be set on drive.permissions.update or drive.permissions.patch requests", "format": "date-time", "type": "string"}, "id": {"description": "The ID of the user this permission refers to, and identical to the `permissionId` in the About and Files resources. When making a `drive.permissions.insert` request, exactly one of the `id` or `value` fields must be specified unless the permission type is `anyone`, in which case both `id` and `value` are ignored.", "type": "string"}, "inheritedPermissionsDisabled": {"description": "When true, only organizers, owners, and users with permissions added directly on the item can access it.", "type": "boolean"}, "kind": {"default": "drive#permission", "description": "Output only. This is always `drive#permission`.", "type": "string"}, "name": {"description": "Output only. The name for this permission.", "type": "string"}, "pendingOwner": {"description": "Whether the account associated with this permission is a pending owner. Only populated for `user` type permissions for files that are not in a shared drive.", "type": "boolean"}, "permissionDetails": {"description": "Output only. Details of whether the permissions on this item are inherited or directly on this item.", "items": {"properties": {"additionalRoles": {"description": "Output only. Additional roles for this user. Only `commenter` is currently possible, though more may be supported in the future.", "items": {"type": "string"}, "type": "array"}, "inherited": {"description": "Output only. Whether this permission is inherited. This field is always populated.", "type": "boolean"}, "inheritedFrom": {"description": "Output only. The ID of the item from which this permission is inherited. This is only populated for items in shared drives.", "type": "string"}, "permissionType": {"description": "Output only. The permission type for this user. While new values may be added in future, the following are currently possible: * `file` * `member`", "type": "string"}, "role": {"description": "Output only. The primary role for this user. While new values may be added in the future, the following are currently possible: * `organizer` * `fileOrganizer` * `writer` * `reader`", "type": "string"}}, "type": "object"}, "readOnly": true, "type": "array"}, "photoLink": {"description": "Output only. A link to the profile photo, if available.", "type": "string"}, "role": {"annotations": {"required": ["drive.permissions.insert"]}, "description": "The primary role for this user. While new values may be supported in the future, the following are currently allowed: * `owner` * `organizer` * `fileOrganizer` * `writer` * `reader`", "type": "string"}, "selfLink": {"description": "Output only. A link back to this permission.", "type": "string"}, "teamDrivePermissionDetails": {"deprecated": true, "description": "Output only. Deprecated: Use `permissionDetails` instead.", "items": {"properties": {"additionalRoles": {"deprecated": true, "description": "Output only. Deprecated: Use `permissionDetails/additionalRoles` instead.", "items": {"type": "string"}, "type": "array"}, "inherited": {"deprecated": true, "description": "Output only. Deprecated: Use `permissionDetails/inherited` instead.", "type": "boolean"}, "inheritedFrom": {"deprecated": true, "description": "Output only. Deprecated: Use `permissionDetails/inheritedFrom` instead.", "type": "string"}, "role": {"deprecated": true, "description": "Output only. Deprecated: Use `permissionDetails/role` instead.", "type": "string"}, "teamDrivePermissionType": {"deprecated": true, "description": "Output only. Deprecated: Use `permissionDetails/permissionType` instead.", "type": "string"}}, "type": "object"}, "readOnly": true, "type": "array"}, "type": {"annotations": {"required": ["drive.permissions.insert"]}, "description": "The account type. Allowed values are: * `user` * `group` * `domain` * `anyone`", "type": "string"}, "value": {"description": "The email address or domain name for the entity. This is used during inserts and is not populated in responses. When making a `drive.permissions.insert` request, exactly one of the `id` or `value` fields must be specified unless the permission type is `anyone`, in which case both `id` and `value` are ignored.", "type": "string"}, "view": {"description": "Indicates the view for this permission. Only populated for permissions that belong to a view. published and metadata are the only supported values. - published: The permission's role is published_reader. - metadata: The item is only visible to the metadata view because the item has limited access and the scope has at least read access to the parent. Note: The metadata view is currently only supported on folders. ", "type": "string"}, "withLink": {"description": "Whether the link is required for this permission.", "type": "boolean"}}, "type": "object"}, "PermissionId": {"description": "An ID for a user or group as seen in Permission items.", "id": "PermissionId", "properties": {"id": {"description": "The permission ID.", "type": "string"}, "kind": {"default": "drive#permissionId", "description": "This is always `drive#permissionId`.", "type": "string"}}, "type": "object"}, "PermissionList": {"description": "A list of permissions associated with a file.", "id": "PermissionList", "properties": {"etag": {"description": "The ETag of the list.", "type": "string"}, "items": {"description": "The list of permissions.", "items": {"$ref": "Permission"}, "type": "array"}, "kind": {"default": "drive#permissionList", "description": "This is always `drive#permissionList`.", "type": "string"}, "nextPageToken": {"description": "The page token for the next page of permissions. This field will be absent if the end of the permissions list has been reached. If the token is rejected for any reason, it should be discarded, and pagination should be restarted from the first page of results.", "type": "string"}, "selfLink": {"description": "A link back to this list.", "type": "string"}}, "type": "object"}, "Property": {"description": "A key-value pair attached to a file that is either public or private to an application. The following limits apply to file properties: * Maximum of 100 properties total per file * Maximum of 30 private properties per app * Maximum of 30 public properties * Maximum of 124 bytes size limit on (key + value) string in UTF-8 encoding for a single property Some resource methods (such as `properties.update`) require a `propertyKey`. Use the `properties.list` method to retrieve the key for a property.", "id": "Property", "properties": {"etag": {"description": "Output only. ETag of the property.", "type": "string"}, "key": {"annotations": {"required": ["drive.properties.insert"]}, "description": "The key of this property.", "type": "string"}, "kind": {"default": "drive#property", "description": "Output only. This is always `drive#property`.", "type": "string"}, "selfLink": {"description": "Output only. The link back to this property.", "type": "string"}, "value": {"description": "The value of this property.", "type": "string"}, "visibility": {"description": "The visibility of this property. Allowed values are PRIVATE (default) and PUBLIC. Private properties can only be retrieved using an authenticated request. An authenticated request uses an access token obtained with a OAuth 2 client ID. You cannot use an API key to retrieve private properties.", "type": "string"}}, "type": "object"}, "PropertyList": {"description": "A collection of properties, key-value pairs that are either public or private to an application.", "id": "PropertyList", "properties": {"etag": {"description": "The ETag of the list.", "type": "string"}, "items": {"description": "The list of properties.", "items": {"$ref": "Property"}, "type": "array"}, "kind": {"default": "drive#propertyList", "description": "This is always `drive#propertyList`.", "type": "string"}, "selfLink": {"description": "The link back to this list.", "type": "string"}}, "type": "object"}, "Revision": {"description": "A revision of a file. Some resource methods (such as `revisions.update`) require a `revisionId`. Use the `revisions.list` method to retrieve the ID for a revision.", "id": "Revision", "properties": {"downloadUrl": {"description": "Output only. Short term download URL for the file. This will only be populated on files with content stored in Drive.", "type": "string"}, "etag": {"description": "Output only. The ETag of the revision.", "type": "string"}, "exportLinks": {"additionalProperties": {"type": "string"}, "description": "Output only. Links for exporting Docs Editors files to specific formats.", "type": "object"}, "fileSize": {"description": "Output only. The size of the revision in bytes. This will only be populated on files with content stored in Drive.", "format": "int64", "type": "string"}, "id": {"description": "Output only. The ID of the revision.", "type": "string"}, "kind": {"default": "drive#revision", "description": "Output only. This is always `drive#revision`.", "type": "string"}, "lastModifyingUser": {"$ref": "User", "description": "Output only. The last user to modify this revision. This field is only populated when the last modification was performed by a signed-in user."}, "lastModifyingUserName": {"description": "Output only. Name of the last user to modify this revision.", "type": "string"}, "md5Checksum": {"description": "Output only. An MD5 checksum for the content of this revision. This will only be populated on files with content stored in Drive.", "type": "string"}, "mimeType": {"description": "Output only. The MIME type of the revision.", "type": "string"}, "modifiedDate": {"description": "Last time this revision was modified (formatted RFC 3339 timestamp).", "format": "date-time", "type": "string"}, "originalFilename": {"description": "Output only. The original filename when this revision was created. This will only be populated on files with content stored in Drive.", "type": "string"}, "pinned": {"description": "Whether this revision is pinned to prevent automatic purging. If not set, the revision is automatically purged 30 days after newer content is uploaded. This field can only be modified on files with content stored in Drive, excluding Docs Editors files. Revisions can also be pinned when they are created through the drive.files.insert/update/copy by using the pinned query parameter. Pinned revisions are stored indefinitely using additional storage quota, up to a maximum of 200 revisions.", "type": "boolean"}, "publishAuto": {"description": "Whether subsequent revisions will be automatically republished. This is only populated and can only be modified for Docs Editors files.", "type": "boolean"}, "published": {"description": "Whether this revision is published. This is only populated and can only be modified for Docs Editors files.", "type": "boolean"}, "publishedLink": {"description": "Output only. A link to the published revision. This is only populated for Docs Editors files.", "type": "string"}, "publishedOutsideDomain": {"description": "Whether this revision is published outside the domain. This is only populated and can only be modified for Docs Editors files.", "type": "boolean"}, "selfLink": {"description": "Output only. A link back to this revision.", "type": "string"}}, "type": "object"}, "RevisionList": {"description": "A list of revisions of a file.", "id": "RevisionList", "properties": {"etag": {"description": "The ETag of the list.", "type": "string"}, "items": {"description": "The list of revisions. If nextPageToken is populated, then this list may be incomplete and an additional page of results should be fetched.", "items": {"$ref": "Revision"}, "type": "array"}, "kind": {"default": "drive#revisionList", "description": "This is always `drive#revisionList`.", "type": "string"}, "nextPageToken": {"description": "The page token for the next page of revisions. This field will be absent if the end of the revisions list has been reached. If the token is rejected for any reason, it should be discarded and pagination should be restarted from the first page of results.", "type": "string"}, "selfLink": {"description": "A link back to this list.", "type": "string"}}, "type": "object"}, "StartPageToken": {"id": "StartPageToken", "properties": {"kind": {"default": "drive#startPageToken", "description": "Identifies what kind of resource this is. Value: the fixed string `\"drive#startPageToken\"`.", "type": "string"}, "startPageToken": {"description": "The starting page token for listing changes.", "type": "string"}}, "type": "object"}, "TeamDrive": {"description": "Deprecated: Use the `drive` collection instead.", "id": "TeamDrive", "properties": {"backgroundImageFile": {"description": "An image file and cropping parameters from which a background image for this Team Drive is set. This is a write only field; it can only be set on `drive.teamdrives.update` requests that don't set `themeId`. When specified, all fields of the `backgroundImageFile` must be set.", "properties": {"id": {"description": "The ID of an image file in Drive to use for the background image.", "type": "string"}, "width": {"description": "The width of the cropped image in the closed range of 0 to 1. This value represents the width of the cropped image divided by the width of the entire image. The height is computed by applying a width to height aspect ratio of 80 to 9. The resulting image must be at least 1280 pixels wide and 144 pixels high.", "format": "float", "type": "number"}, "xCoordinate": {"description": "The X coordinate of the upper left corner of the cropping area in the background image. This is a value in the closed range of 0 to 1. This value represents the horizontal distance from the left side of the entire image to the left side of the cropping area divided by the width of the entire image.", "format": "float", "type": "number"}, "yCoordinate": {"description": "The Y coordinate of the upper left corner of the cropping area in the background image. This is a value in the closed range of 0 to 1. This value represents the vertical distance from the top side of the entire image to the top side of the cropping area divided by the height of the entire image.", "format": "float", "type": "number"}}, "type": "object"}, "backgroundImageLink": {"description": "A short-lived link to this Team Drive's background image.", "type": "string"}, "capabilities": {"description": "Capabilities the current user has on this Team Drive.", "properties": {"canAddChildren": {"description": "Whether the current user can add children to folders in this Team Drive.", "type": "boolean"}, "canChangeCopyRequiresWriterPermissionRestriction": {"description": "Whether the current user can change the `copyRequiresWriterPermission` restriction of this Team Drive.", "type": "boolean"}, "canChangeDomainUsersOnlyRestriction": {"description": "Whether the current user can change the `domainUsersOnly` restriction of this Team Drive.", "type": "boolean"}, "canChangeSharingFoldersRequiresOrganizerPermissionRestriction": {"description": "Whether the current user can change the `sharingFoldersRequiresOrganizerPermission` restriction of this Team Drive.", "type": "boolean"}, "canChangeTeamDriveBackground": {"description": "Whether the current user can change the background of this Team Drive.", "type": "boolean"}, "canChangeTeamMembersOnlyRestriction": {"description": "Whether the current user can change the `teamMembersOnly` restriction of this Team Drive.", "type": "boolean"}, "canComment": {"description": "Whether the current user can comment on files in this Team Drive.", "type": "boolean"}, "canCopy": {"description": "Whether the current user can copy files in this Team Drive.", "type": "boolean"}, "canDeleteChildren": {"description": "Whether the current user can delete children from folders in this Team Drive.", "type": "boolean"}, "canDeleteTeamDrive": {"description": "Whether the current user can delete this Team Drive. Attempting to delete the Team Drive may still fail if there are untrashed items inside the Team Drive.", "type": "boolean"}, "canDownload": {"description": "Whether the current user can download files in this Team Drive.", "type": "boolean"}, "canEdit": {"description": "Whether the current user can edit files in this Team Drive", "type": "boolean"}, "canListChildren": {"description": "Whether the current user can list the children of folders in this Team Drive.", "type": "boolean"}, "canManageMembers": {"description": "Whether the current user can add members to this Team Drive or remove them or change their role.", "type": "boolean"}, "canReadRevisions": {"description": "Whether the current user can read the revisions resource of files in this Team Drive.", "type": "boolean"}, "canRemoveChildren": {"deprecated": true, "description": "Deprecated: Use `canDeleteChildren` or `canTrashChildren` instead.", "type": "boolean"}, "canRename": {"description": "Whether the current user can rename files or folders in this Team Drive.", "type": "boolean"}, "canRenameTeamDrive": {"description": "Whether the current user can rename this Team Drive.", "type": "boolean"}, "canResetTeamDriveRestrictions": {"description": "Whether the current user can reset the Team Drive restrictions to defaults.", "type": "boolean"}, "canShare": {"description": "Whether the current user can share files or folders in this Team Drive.", "type": "boolean"}, "canTrashChildren": {"description": "Whether the current user can trash children from folders in this Team Drive.", "type": "boolean"}}, "type": "object"}, "colorRgb": {"description": "The color of this Team Drive as an RGB hex string. It can only be set on a `drive.teamdrives.update` request that does not set `themeId`.", "type": "string"}, "createdDate": {"description": "The time at which the Team Drive was created (RFC 3339 date-time).", "format": "date-time", "type": "string"}, "id": {"description": "The ID of this Team Drive which is also the ID of the top level folder of this Team Drive.", "type": "string"}, "kind": {"default": "drive#teamDrive", "description": "This is always `drive#teamDrive`", "type": "string"}, "name": {"annotations": {"required": ["drive.teamdrives.insert"]}, "description": "The name of this Team Drive.", "type": "string"}, "orgUnitId": {"description": "The organizational unit of this shared drive. This field is only populated on `drives.list` responses when the `useDomainAdminAccess` parameter is set to `true`.", "type": "string"}, "restrictions": {"description": "A set of restrictions that apply to this Team Drive or items inside this Team Drive.", "properties": {"adminManagedRestrictions": {"description": "Whether administrative privileges on this Team Drive are required to modify restrictions.", "type": "boolean"}, "copyRequiresWriterPermission": {"description": "Whether the options to copy, print, or download files inside this Team Drive, should be disabled for readers and commenters. When this restriction is set to `true`, it will override the similarly named field to `true` for any file inside this Team Drive.", "type": "boolean"}, "domainUsersOnly": {"description": "Whether access to this Team Drive and items inside this Team Drive is restricted to users of the domain to which this Team Drive belongs. This restriction may be overridden by other sharing policies controlled outside of this Team Drive.", "type": "boolean"}, "sharingFoldersRequiresOrganizerPermission": {"description": "If true, only users with the organizer role can share folders. If false, users with either the organizer role or the file organizer role can share folders.", "type": "boolean"}, "teamMembersOnly": {"description": "Whether access to items inside this Team Drive is restricted to members of this Team Drive.", "type": "boolean"}}, "type": "object"}, "themeId": {"description": "The ID of the theme from which the background image and color will be set. The set of possible `teamDriveThemes` can be retrieved from a `drive.about.get` response. When not specified on a `drive.teamdrives.insert` request, a random theme is chosen from which the background image and color are set. This is a write-only field; it can only be set on requests that don't set `colorRgb` or `backgroundImageFile`.", "type": "string"}}, "type": "object"}, "TeamDriveList": {"description": "A list of Team Drives.", "id": "TeamDriveList", "properties": {"items": {"description": "The list of Team Drives.", "items": {"$ref": "TeamDrive"}, "type": "array"}, "kind": {"default": "drive#teamDriveList", "description": "This is always `drive#teamDriveList`", "type": "string"}, "nextPageToken": {"description": "The page token for the next page of Team Drives.", "type": "string"}}, "type": "object"}, "User": {"description": "Information about a Drive user.", "id": "User", "properties": {"displayName": {"description": "Output only. A plain text displayable name for this user.", "readOnly": true, "type": "string"}, "emailAddress": {"description": "Output only. The email address of the user. This may not be present in certain contexts if the user has not made their email address visible to the requester.", "readOnly": true, "type": "string"}, "isAuthenticatedUser": {"description": "Output only. Whether this user is the same as the authenticated user for whom the request was made.", "readOnly": true, "type": "boolean"}, "kind": {"default": "drive#user", "description": "Output only. Identifies what kind of resource this is. Value: the fixed string `drive#user`.", "readOnly": true, "type": "string"}, "permissionId": {"description": "Output only. The user's ID as visible in Permission resources.", "readOnly": true, "type": "string"}, "picture": {"description": "Output only. The user's profile picture.", "properties": {"url": {"description": "Output only. A URL that points to a profile picture of this user.", "readOnly": true, "type": "string"}}, "readOnly": true, "type": "object"}}, "type": "object"}}, "servicePath": "drive/v2/", "title": "Google Drive API", "version": "v2"}