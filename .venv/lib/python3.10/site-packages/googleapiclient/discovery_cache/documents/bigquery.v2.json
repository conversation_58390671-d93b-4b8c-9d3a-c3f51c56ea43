{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/bigquery": {"description": "View and manage your data in Google BigQuery and see the email address for your Google Account"}, "https://www.googleapis.com/auth/bigquery.insertdata": {"description": "Insert data into Google BigQuery"}, "https://www.googleapis.com/auth/cloud-platform": {"description": "See, edit, configure, and delete your Google Cloud data and see the email address for your Google Account."}, "https://www.googleapis.com/auth/cloud-platform.read-only": {"description": "View your data across Google Cloud services and see the email address of your Google Account"}, "https://www.googleapis.com/auth/devstorage.full_control": {"description": "Manage your data and permissions in Cloud Storage and see the email address for your Google Account"}, "https://www.googleapis.com/auth/devstorage.read_only": {"description": "View your data in Google Cloud Storage"}, "https://www.googleapis.com/auth/devstorage.read_write": {"description": "Manage your data in Cloud Storage and see the email address of your Google Account"}}}}, "basePath": "/bigquery/v2/", "baseUrl": "https://bigquery.googleapis.com/bigquery/v2/", "batchPath": "batch/bigquery/v2", "canonicalName": "Bigquery", "description": "A data platform for customers to create, manage, share and query data.", "discoveryVersion": "v1", "documentationLink": "https://cloud.google.com/bigquery/", "endpoints": [{"description": "Regional Endpoint", "endpointUrl": "https://bigquery.europe-west3.rep.googleapis.com/", "location": "europe-west3"}, {"description": "Regional Endpoint", "endpointUrl": "https://bigquery.europe-west8.rep.googleapis.com/", "location": "europe-west8"}, {"description": "Regional Endpoint", "endpointUrl": "https://bigquery.europe-west9.rep.googleapis.com/", "location": "europe-west9"}, {"description": "Regional Endpoint", "endpointUrl": "https://bigquery.me-central2.rep.googleapis.com/", "location": "me-central2"}, {"description": "Regional Endpoint", "endpointUrl": "https://bigquery.us-central1.rep.googleapis.com/", "location": "us-central1"}, {"description": "Regional Endpoint", "endpointUrl": "https://bigquery.us-central2.rep.googleapis.com/", "location": "us-central2"}, {"description": "Regional Endpoint", "endpointUrl": "https://bigquery.us-east1.rep.googleapis.com/", "location": "us-east1"}, {"description": "Regional Endpoint", "endpointUrl": "https://bigquery.us-east4.rep.googleapis.com/", "location": "us-east4"}, {"description": "Regional Endpoint", "endpointUrl": "https://bigquery.us-east5.rep.googleapis.com/", "location": "us-east5"}, {"description": "Regional Endpoint", "endpointUrl": "https://bigquery.us-east7.rep.googleapis.com/", "location": "us-east7"}, {"description": "Regional Endpoint", "endpointUrl": "https://bigquery.us-south1.rep.googleapis.com/", "location": "us-south1"}, {"description": "Regional Endpoint", "endpointUrl": "https://bigquery.us-west1.rep.googleapis.com/", "location": "us-west1"}, {"description": "Regional Endpoint", "endpointUrl": "https://bigquery.us-west2.rep.googleapis.com/", "location": "us-west2"}, {"description": "Regional Endpoint", "endpointUrl": "https://bigquery.us-west3.rep.googleapis.com/", "location": "us-west3"}, {"description": "Regional Endpoint", "endpointUrl": "https://bigquery.us-west4.rep.googleapis.com/", "location": "us-west4"}, {"description": "Regional Endpoint", "endpointUrl": "https://bigquery.us-west8.rep.googleapis.com/", "location": "us-west8"}], "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "bigquery:v2", "kind": "discovery#restDescription", "mtlsRootUrl": "https://bigquery.mtls.googleapis.com/", "name": "big<PERSON>y", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"datasets": {"methods": {"delete": {"description": "Deletes the dataset specified by the datasetId value. Before you can delete a dataset, you must delete all its tables, either manually or by specifying deleteContents. Immediately after deletion, you can create another dataset with the same name.", "flatPath": "projects/{projectsId}/datasets/{datasetsId}", "httpMethod": "DELETE", "id": "bigquery.datasets.delete", "parameterOrder": ["projectId", "datasetId"], "parameters": {"datasetId": {"description": "Required. Dataset ID of dataset being deleted", "location": "path", "pattern": "^[^/]+$", "required": true, "type": "string"}, "deleteContents": {"description": "If True, delete all the tables in the dataset. If False and the dataset contains tables, the request will fail. Default is False", "location": "query", "type": "boolean"}, "projectId": {"description": "Required. Project ID of the dataset being deleted", "location": "path", "pattern": "^[^/]+$", "required": true, "type": "string"}}, "path": "projects/{+projectId}/datasets/{+datasetId}", "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Returns the dataset specified by datasetID.", "flatPath": "projects/{projectsId}/datasets/{datasetsId}", "httpMethod": "GET", "id": "bigquery.datasets.get", "parameterOrder": ["projectId", "datasetId"], "parameters": {"accessPolicyVersion": {"description": "Optional. The version of the access policy schema to fetch. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for conditional access policy binding in datasets must specify version 3. Dataset with no conditional role bindings in access policy may specify any valid value or leave the field unset. This field will be mapped to [IAM Policy version] (https://cloud.google.com/iam/docs/policies#versions) and will be used to fetch policy from IAM. If unset or if 0 or 1 value is used for dataset with conditional bindings, access entry with condition will have role string appended by 'withcond' string followed by a hash value. For example : { \"access\": [ { \"role\": \"roles/bigquery.dataViewer_with_conditionalbinding_7a34awqsda\", \"userByEmail\": \"<EMAIL>\", } ] } Please refer https://cloud.google.com/iam/docs/troubleshooting-withcond for more details.", "format": "int32", "location": "query", "type": "integer"}, "datasetId": {"description": "Required. Dataset ID of the requested dataset", "location": "path", "pattern": "^[^/]+$", "required": true, "type": "string"}, "datasetView": {"description": "Optional. Specifies the view that determines which dataset information is returned. By default, metadata and ACL information are returned.", "enum": ["DATASET_VIEW_UNSPECIFIED", "METADATA", "ACL", "FULL"], "enumDescriptions": ["The default value. Default to the FULL view.", "Updates metadata information for the dataset, such as friendlyName, description, labels, etc.", "Updates ACL information for the dataset, which defines dataset access for one or more entities.", "Updates both dataset metadata and ACL information."], "location": "query", "type": "string"}, "projectId": {"description": "Required. Project ID of the requested dataset", "location": "path", "pattern": "^[^/]+$", "required": true, "type": "string"}}, "path": "projects/{+projectId}/datasets/{+datasetId}", "response": {"$ref": "Dataset"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"]}, "insert": {"description": "Creates a new empty dataset.", "flatPath": "projects/{projectsId}/datasets", "httpMethod": "POST", "id": "bigquery.datasets.insert", "parameterOrder": ["projectId"], "parameters": {"accessPolicyVersion": {"description": "Optional. The version of the provided access policy schema. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. This version refers to the schema version of the access policy and not the version of access policy. This field's value can be equal or more than the access policy schema provided in the request. For example, * Requests with conditional access policy binding in datasets must specify version 3. * But dataset with no conditional role bindings in access policy may specify any valid value or leave the field unset. If unset or if 0 or 1 value is used for dataset with conditional bindings, request will be rejected. This field will be mapped to IAM Policy version (https://cloud.google.com/iam/docs/policies#versions) and will be used to set policy in IAM.", "format": "int32", "location": "query", "type": "integer"}, "projectId": {"description": "Required. Project ID of the new dataset", "location": "path", "pattern": "^[^/]+$", "required": true, "type": "string"}}, "path": "projects/{+projectId}/datasets", "request": {"$ref": "Dataset"}, "response": {"$ref": "Dataset"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists all datasets in the specified project to which the user has been granted the READER dataset role.", "flatPath": "projects/{projectsId}/datasets", "httpMethod": "GET", "id": "bigquery.datasets.list", "parameterOrder": ["projectId"], "parameters": {"all": {"description": "Whether to list all datasets, including hidden ones", "location": "query", "type": "boolean"}, "filter": {"description": "An expression for filtering the results of the request by label. The syntax is `labels.[:]`. Multiple filters can be AND-ed together by connecting with a space. Example: `labels.department:receiving labels.active`. See [Filtering datasets using labels](https://cloud.google.com/bigquery/docs/filtering-labels#filtering_datasets_using_labels) for details.", "location": "query", "type": "string"}, "maxResults": {"description": "The maximum number of results to return in a single response page. Leverage the page tokens to iterate through the entire collection.", "format": "uint32", "location": "query", "type": "integer"}, "pageToken": {"description": "Page token, returned by a previous call, to request the next page of results", "location": "query", "type": "string"}, "projectId": {"description": "Required. Project ID of the datasets to be listed", "location": "path", "pattern": "^[^/]+$", "required": true, "type": "string"}}, "path": "projects/{+projectId}/datasets", "response": {"$ref": "DatasetList"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"]}, "patch": {"description": "Updates information in an existing dataset. The update method replaces the entire dataset resource, whereas the patch method only replaces fields that are provided in the submitted dataset resource. This method supports RFC5789 patch semantics.", "flatPath": "projects/{projectsId}/datasets/{datasetsId}", "httpMethod": "PATCH", "id": "bigquery.datasets.patch", "parameterOrder": ["projectId", "datasetId"], "parameters": {"accessPolicyVersion": {"description": "Optional. The version of the provided access policy schema. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. This version refers to the schema version of the access policy and not the version of access policy. This field's value can be equal or more than the access policy schema provided in the request. For example, * Operations updating conditional access policy binding in datasets must specify version 3. Some of the operations are : - Adding a new access policy entry with condition. - Removing an access policy entry with condition. - Updating an access policy entry with condition. * But dataset with no conditional role bindings in access policy may specify any valid value or leave the field unset. If unset or if 0 or 1 value is used for dataset with conditional bindings, request will be rejected. This field will be mapped to IAM Policy version (https://cloud.google.com/iam/docs/policies#versions) and will be used to set policy in IAM.", "format": "int32", "location": "query", "type": "integer"}, "datasetId": {"description": "Required. Dataset ID of the dataset being updated", "location": "path", "pattern": "^[^/]+$", "required": true, "type": "string"}, "projectId": {"description": "Required. Project ID of the dataset being updated", "location": "path", "pattern": "^[^/]+$", "required": true, "type": "string"}, "updateMode": {"description": "Optional. Specifies the fields of dataset that update/patch operation is targeting By default, both metadata and ACL fields are updated.", "enum": ["UPDATE_MODE_UNSPECIFIED", "UPDATE_METADATA", "UPDATE_ACL", "UPDATE_FULL"], "enumDescriptions": ["The default value. Default to the UPDATE_FULL.", "Includes metadata information for the dataset, such as friendlyName, description, labels, etc.", "Includes ACL information for the dataset, which defines dataset access for one or more entities.", "Includes both dataset metadata and ACL information."], "location": "query", "type": "string"}}, "path": "projects/{+projectId}/datasets/{+datasetId}", "request": {"$ref": "Dataset"}, "response": {"$ref": "Dataset"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"]}, "undelete": {"description": "Undeletes a dataset which is within time travel window based on datasetId. If a time is specified, the dataset version deleted at that time is undeleted, else the last live version is undeleted.", "flatPath": "projects/{projectsId}/datasets/{datasetsId}:undelete", "httpMethod": "POST", "id": "bigquery.datasets.undelete", "parameterOrder": ["projectId", "datasetId"], "parameters": {"datasetId": {"description": "Required. Dataset ID of dataset being deleted", "location": "path", "pattern": "^[^/]+$", "required": true, "type": "string"}, "projectId": {"description": "Required. Project ID of the dataset to be undeleted", "location": "path", "pattern": "^[^/]+$", "required": true, "type": "string"}}, "path": "projects/{+projectId}/datasets/{+datasetId}:undelete", "request": {"$ref": "UndeleteDatasetRequest"}, "response": {"$ref": "Dataset"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"]}, "update": {"description": "Updates information in an existing dataset. The update method replaces the entire dataset resource, whereas the patch method only replaces fields that are provided in the submitted dataset resource.", "flatPath": "projects/{projectsId}/datasets/{datasetsId}", "httpMethod": "PUT", "id": "bigquery.datasets.update", "parameterOrder": ["projectId", "datasetId"], "parameters": {"accessPolicyVersion": {"description": "Optional. The version of the provided access policy schema. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. This version refers to the schema version of the access policy and not the version of access policy. This field's value can be equal or more than the access policy schema provided in the request. For example, * Operations updating conditional access policy binding in datasets must specify version 3. Some of the operations are : - Adding a new access policy entry with condition. - Removing an access policy entry with condition. - Updating an access policy entry with condition. * But dataset with no conditional role bindings in access policy may specify any valid value or leave the field unset. If unset or if 0 or 1 value is used for dataset with conditional bindings, request will be rejected. This field will be mapped to IAM Policy version (https://cloud.google.com/iam/docs/policies#versions) and will be used to set policy in IAM.", "format": "int32", "location": "query", "type": "integer"}, "datasetId": {"description": "Required. Dataset ID of the dataset being updated", "location": "path", "pattern": "^[^/]+$", "required": true, "type": "string"}, "projectId": {"description": "Required. Project ID of the dataset being updated", "location": "path", "pattern": "^[^/]+$", "required": true, "type": "string"}, "updateMode": {"description": "Optional. Specifies the fields of dataset that update/patch operation is targeting By default, both metadata and ACL fields are updated.", "enum": ["UPDATE_MODE_UNSPECIFIED", "UPDATE_METADATA", "UPDATE_ACL", "UPDATE_FULL"], "enumDescriptions": ["The default value. Default to the UPDATE_FULL.", "Includes metadata information for the dataset, such as friendlyName, description, labels, etc.", "Includes ACL information for the dataset, which defines dataset access for one or more entities.", "Includes both dataset metadata and ACL information."], "location": "query", "type": "string"}}, "path": "projects/{+projectId}/datasets/{+datasetId}", "request": {"$ref": "Dataset"}, "response": {"$ref": "Dataset"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"]}}}, "jobs": {"methods": {"cancel": {"description": "Requests that a job be cancelled. This call will return immediately, and the client will need to poll for the job status to see if the cancel completed successfully. Cancelled jobs may still incur costs.", "flatPath": "projects/{projectsId}/jobs/{jobsId}/cancel", "httpMethod": "POST", "id": "bigquery.jobs.cancel", "parameterOrder": ["projectId", "jobId"], "parameters": {"jobId": {"description": "Required. Job ID of the job to cancel", "location": "path", "pattern": "^[^/]+$", "required": true, "type": "string"}, "location": {"description": "The geographic location of the job. You must [specify the location](https://cloud.google.com/bigquery/docs/locations#specify_locations) to run the job for the following scenarios: * If the location to run a job is not in the `us` or the `eu` multi-regional location * If the job's location is in a single region (for example, `us-central1`)", "location": "query", "type": "string"}, "projectId": {"description": "Required. Project ID of the job to cancel", "location": "path", "pattern": "^[^/]+$", "required": true, "type": "string"}}, "path": "projects/{+projectId}/jobs/{+jobId}/cancel", "response": {"$ref": "JobCancelResponse"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Requests the deletion of the metadata of a job. This call returns when the job's metadata is deleted.", "flatPath": "projects/{projectsId}/jobs/{jobsId}/delete", "httpMethod": "DELETE", "id": "bigquery.jobs.delete", "parameterOrder": ["projectId", "jobId"], "parameters": {"jobId": {"description": "Required. Job ID of the job for which metadata is to be deleted. If this is a parent job which has child jobs, the metadata from all child jobs will be deleted as well. Direct deletion of the metadata of child jobs is not allowed.", "location": "path", "pattern": "^[^/]+$", "required": true, "type": "string"}, "location": {"description": "The geographic location of the job. Required. For more information, see how to [specify locations](https://cloud.google.com/bigquery/docs/locations#specify_locations).", "location": "query", "type": "string"}, "projectId": {"description": "Required. Project ID of the job for which metadata is to be deleted.", "location": "path", "pattern": "^[^/]+$", "required": true, "type": "string"}}, "path": "projects/{+projectId}/jobs/{+jobId}/delete", "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Returns information about a specific job. Job information is available for a six month period after creation. Requires that you're the person who ran the job, or have the Is Owner project role.", "flatPath": "projects/{projectsId}/jobs/{jobsId}", "httpMethod": "GET", "id": "bigquery.jobs.get", "parameterOrder": ["projectId", "jobId"], "parameters": {"jobId": {"description": "Required. Job ID of the requested job.", "location": "path", "pattern": "^[^/]+$", "required": true, "type": "string"}, "location": {"description": "The geographic location of the job. You must specify the location to run the job for the following scenarios: * If the location to run a job is not in the `us` or the `eu` multi-regional location * If the job's location is in a single region (for example, `us-central1`) For more information, see how to [specify locations](https://cloud.google.com/bigquery/docs/locations#specify_locations).", "location": "query", "type": "string"}, "projectId": {"description": "Required. Project ID of the requested job.", "location": "path", "pattern": "^[^/]+$", "required": true, "type": "string"}}, "path": "projects/{+projectId}/jobs/{+jobId}", "response": {"$ref": "Job"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"]}, "getQueryResults": {"description": "RPC to get the results of a query job.", "flatPath": "projects/{projectsId}/queries/{queriesId}", "httpMethod": "GET", "id": "bigquery.jobs.getQueryResults", "parameterOrder": ["projectId", "jobId"], "parameters": {"formatOptions.useInt64Timestamp": {"description": "Optional. Output timestamp as usec int64. Default is false.", "location": "query", "type": "boolean"}, "jobId": {"description": "Required. Job ID of the query job.", "location": "path", "pattern": "^[^/]+$", "required": true, "type": "string"}, "location": {"description": "The geographic location of the job. You must specify the location to run the job for the following scenarios: * If the location to run a job is not in the `us` or the `eu` multi-regional location * If the job's location is in a single region (for example, `us-central1`) For more information, see how to [specify locations](https://cloud.google.com/bigquery/docs/locations#specify_locations).", "location": "query", "type": "string"}, "maxResults": {"description": "Maximum number of results to read.", "format": "uint32", "location": "query", "type": "integer"}, "pageToken": {"description": "Page token, returned by a previous call, to request the next page of results.", "location": "query", "type": "string"}, "projectId": {"description": "Required. Project ID of the query job.", "location": "path", "pattern": "^[^/]+$", "required": true, "type": "string"}, "startIndex": {"description": "Zero-based index of the starting row.", "format": "uint64", "location": "query", "type": "string"}, "timeoutMs": {"description": "Optional: Specifies the maximum amount of time, in milliseconds, that the client is willing to wait for the query to complete. By default, this limit is 10 seconds (10,000 milliseconds). If the query is complete, the jobComplete field in the response is true. If the query has not yet completed, jobComplete is false. You can request a longer timeout period in the timeoutMs field. However, the call is not guaranteed to wait for the specified timeout; it typically returns after around 200 seconds (200,000 milliseconds), even if the query is not complete. If jobComplete is false, you can continue to wait for the query to complete by calling the getQueryResults method until the jobComplete field in the getQueryResults response is true.", "format": "uint32", "location": "query", "type": "integer"}}, "path": "projects/{+projectId}/queries/{+jobId}", "response": {"$ref": "GetQueryResultsResponse"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"]}, "insert": {"description": "Starts a new asynchronous job. This API has two different kinds of endpoint URIs, as this method supports a variety of use cases. * The *Metadata* URI is used for most interactions, as it accepts the job configuration directly. * The *Upload* URI is ONLY for the case when you're sending both a load job configuration and a data stream together. In this case, the Upload URI accepts the job configuration and the data as two distinct multipart MIME parts.", "flatPath": "projects/{projectsId}/jobs", "httpMethod": "POST", "id": "bigquery.jobs.insert", "mediaUpload": {"accept": ["*/*"], "protocols": {"resumable": {"multipart": true, "path": "/resumable/upload/bigquery/v2/projects/{+projectId}/jobs"}, "simple": {"multipart": true, "path": "/upload/bigquery/v2/projects/{+projectId}/jobs"}}}, "parameterOrder": ["projectId"], "parameters": {"projectId": {"description": "Project ID of project that will be billed for the job.", "location": "path", "pattern": "^[^/]+$", "required": true, "type": "string"}}, "path": "projects/{+projectId}/jobs", "request": {"$ref": "Job"}, "response": {"$ref": "Job"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/devstorage.full_control", "https://www.googleapis.com/auth/devstorage.read_only", "https://www.googleapis.com/auth/devstorage.read_write"], "supportsMediaUpload": true}, "list": {"description": "Lists all jobs that you started in the specified project. Job information is available for a six month period after creation. The job list is sorted in reverse chronological order, by job creation time. Requires the Can View project role, or the Is Owner project role if you set the allUsers property.", "flatPath": "projects/{projectsId}/jobs", "httpMethod": "GET", "id": "bigquery.jobs.list", "parameterOrder": ["projectId"], "parameters": {"allUsers": {"description": "Whether to display jobs owned by all users in the project. Default False.", "location": "query", "type": "boolean"}, "maxCreationTime": {"description": "Max value for job creation time, in milliseconds since the POSIX epoch. If set, only jobs created before or at this timestamp are returned.", "format": "uint64", "location": "query", "type": "string"}, "maxResults": {"description": "The maximum number of results to return in a single response page. Leverage the page tokens to iterate through the entire collection.", "format": "uint32", "location": "query", "type": "integer"}, "minCreationTime": {"description": "Min value for job creation time, in milliseconds since the POSIX epoch. If set, only jobs created after or at this timestamp are returned.", "format": "uint64", "location": "query", "type": "string"}, "pageToken": {"description": "Page token, returned by a previous call, to request the next page of results.", "location": "query", "type": "string"}, "parentJobId": {"description": "If set, show only child jobs of the specified parent. Otherwise, show all top-level jobs.", "location": "query", "type": "string"}, "projectId": {"description": "Project ID of the jobs to list.", "location": "path", "pattern": "^[^/]+$", "required": true, "type": "string"}, "projection": {"description": "Restrict information returned to a set of selected fields", "enum": ["full", "minimal"], "enumDescriptions": ["Includes all job data", "Does not include the job configuration"], "location": "query", "type": "string"}, "stateFilter": {"description": "Filter for job state", "enum": ["done", "pending", "running"], "enumDescriptions": ["Finished jobs", "Pending jobs", "Running jobs"], "location": "query", "repeated": true, "type": "string"}}, "path": "projects/{+projectId}/jobs", "response": {"$ref": "JobList"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"]}, "query": {"description": "Runs a BigQuery SQL query synchronously and returns query results if the query completes within a specified timeout.", "flatPath": "projects/{projectsId}/queries", "httpMethod": "POST", "id": "bigquery.jobs.query", "parameterOrder": ["projectId"], "parameters": {"projectId": {"description": "Required. Project ID of the query request.", "location": "path", "pattern": "^[^/]+$", "required": true, "type": "string"}}, "path": "projects/{+projectId}/queries", "request": {"$ref": "QueryRequest"}, "response": {"$ref": "QueryResponse"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"]}}}, "models": {"methods": {"delete": {"description": "Deletes the model specified by modelId from the dataset.", "flatPath": "projects/{projectsId}/datasets/{datasetsId}/models/{modelsId}", "httpMethod": "DELETE", "id": "bigquery.models.delete", "parameterOrder": ["projectId", "datasetId", "modelId"], "parameters": {"datasetId": {"description": "Required. Dataset ID of the model to delete.", "location": "path", "pattern": "^[^/]+$", "required": true, "type": "string"}, "modelId": {"description": "Required. Model ID of the model to delete.", "location": "path", "pattern": "^[^/]+$", "required": true, "type": "string"}, "projectId": {"description": "Required. Project ID of the model to delete.", "location": "path", "pattern": "^[^/]+$", "required": true, "type": "string"}}, "path": "projects/{+projectId}/datasets/{+datasetId}/models/{+modelId}", "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets the specified model resource by model ID.", "flatPath": "projects/{projectsId}/datasets/{datasetsId}/models/{modelsId}", "httpMethod": "GET", "id": "bigquery.models.get", "parameterOrder": ["projectId", "datasetId", "modelId"], "parameters": {"datasetId": {"description": "Required. Dataset ID of the requested model.", "location": "path", "pattern": "^[^/]+$", "required": true, "type": "string"}, "modelId": {"description": "Required. Model ID of the requested model.", "location": "path", "pattern": "^[^/]+$", "required": true, "type": "string"}, "projectId": {"description": "Required. Project ID of the requested model.", "location": "path", "pattern": "^[^/]+$", "required": true, "type": "string"}}, "path": "projects/{+projectId}/datasets/{+datasetId}/models/{+modelId}", "response": {"$ref": "Model"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"]}, "list": {"description": "Lists all models in the specified dataset. Requires the READER dataset role. After retrieving the list of models, you can get information about a particular model by calling the models.get method.", "flatPath": "projects/{projectsId}/datasets/{datasetsId}/models", "httpMethod": "GET", "id": "bigquery.models.list", "parameterOrder": ["projectId", "datasetId"], "parameters": {"datasetId": {"description": "Required. Dataset ID of the models to list.", "location": "path", "pattern": "^[^/]+$", "required": true, "type": "string"}, "maxResults": {"description": "The maximum number of results to return in a single response page. Leverage the page tokens to iterate through the entire collection.", "format": "uint32", "location": "query", "type": "integer"}, "pageToken": {"description": "Page token, returned by a previous call to request the next page of results", "location": "query", "type": "string"}, "projectId": {"description": "Required. Project ID of the models to list.", "location": "path", "pattern": "^[^/]+$", "required": true, "type": "string"}}, "path": "projects/{+projectId}/datasets/{+datasetId}/models", "response": {"$ref": "ListModelsResponse"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"]}, "patch": {"description": "Patch specific fields in the specified model.", "flatPath": "projects/{projectsId}/datasets/{datasetsId}/models/{modelsId}", "httpMethod": "PATCH", "id": "bigquery.models.patch", "parameterOrder": ["projectId", "datasetId", "modelId"], "parameters": {"datasetId": {"description": "Required. Dataset ID of the model to patch.", "location": "path", "pattern": "^[^/]+$", "required": true, "type": "string"}, "modelId": {"description": "Required. Model ID of the model to patch.", "location": "path", "pattern": "^[^/]+$", "required": true, "type": "string"}, "projectId": {"description": "Required. Project ID of the model to patch.", "location": "path", "pattern": "^[^/]+$", "required": true, "type": "string"}}, "path": "projects/{+projectId}/datasets/{+datasetId}/models/{+modelId}", "request": {"$ref": "Model"}, "response": {"$ref": "Model"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"]}}}, "projects": {"methods": {"getServiceAccount": {"description": "RPC to get the service account for a project used for interactions with Google Cloud KMS", "flatPath": "projects/{projectsId}/serviceAccount", "httpMethod": "GET", "id": "bigquery.projects.getServiceAccount", "parameterOrder": ["projectId"], "parameters": {"projectId": {"description": "Required. ID of the project.", "location": "path", "pattern": "^[^/]+$", "required": true, "type": "string"}}, "path": "projects/{+projectId}/serviceAccount", "response": {"$ref": "GetServiceAccountResponse"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"]}, "list": {"description": "RPC to list projects to which the user has been granted any project role. Users of this method are encouraged to consider the [Resource Manager](https://cloud.google.com/resource-manager/docs/) API, which provides the underlying data for this method and has more capabilities.", "flatPath": "projects", "httpMethod": "GET", "id": "bigquery.projects.list", "parameterOrder": [], "parameters": {"maxResults": {"description": "`maxResults` unset returns all results, up to 50 per page. Additionally, the number of projects in a page may be fewer than `maxResults` because projects are retrieved and then filtered to only projects with the BigQuery API enabled.", "format": "uint32", "location": "query", "type": "integer"}, "pageToken": {"description": "Page token, returned by a previous call, to request the next page of results. If not present, no further pages are present.", "location": "query", "type": "string"}}, "path": "projects", "response": {"$ref": "ProjectList"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"]}}}, "routines": {"methods": {"delete": {"description": "Deletes the routine specified by routineId from the dataset.", "flatPath": "projects/{projectsId}/datasets/{datasetsId}/routines/{routinesId}", "httpMethod": "DELETE", "id": "bigquery.routines.delete", "parameterOrder": ["projectId", "datasetId", "routineId"], "parameters": {"datasetId": {"description": "Required. Dataset ID of the routine to delete", "location": "path", "pattern": "^[^/]+$", "required": true, "type": "string"}, "projectId": {"description": "Required. Project ID of the routine to delete", "location": "path", "pattern": "^[^/]+$", "required": true, "type": "string"}, "routineId": {"description": "Required. Routine ID of the routine to delete", "location": "path", "pattern": "^[^/]+$", "required": true, "type": "string"}}, "path": "projects/{+projectId}/datasets/{+datasetId}/routines/{+routineId}", "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets the specified routine resource by routine ID.", "flatPath": "projects/{projectsId}/datasets/{datasetsId}/routines/{routinesId}", "httpMethod": "GET", "id": "bigquery.routines.get", "parameterOrder": ["projectId", "datasetId", "routineId"], "parameters": {"datasetId": {"description": "Required. Dataset ID of the requested routine", "location": "path", "pattern": "^[^/]+$", "required": true, "type": "string"}, "projectId": {"description": "Required. Project ID of the requested routine", "location": "path", "pattern": "^[^/]+$", "required": true, "type": "string"}, "readMask": {"description": "If set, only the Routine fields in the field mask are returned in the response. If unset, all Routine fields are returned.", "format": "google-fieldmask", "location": "query", "type": "string"}, "routineId": {"description": "Required. Routine ID of the requested routine", "location": "path", "pattern": "^[^/]+$", "required": true, "type": "string"}}, "path": "projects/{+projectId}/datasets/{+datasetId}/routines/{+routineId}", "response": {"$ref": "Routine"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"]}, "getIamPolicy": {"description": "Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.", "flatPath": "projects/{projectsId}/datasets/{datasetsId}/routines/{routinesId}:getIamPolicy", "httpMethod": "POST", "id": "bigquery.routines.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/datasets/[^/]+/routines/[^/]+$", "required": true, "type": "string"}}, "path": "{+resource}:getIamPolicy", "request": {"$ref": "GetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"]}, "insert": {"description": "Creates a new routine in the dataset.", "flatPath": "projects/{projectsId}/datasets/{datasetsId}/routines", "httpMethod": "POST", "id": "bigquery.routines.insert", "parameterOrder": ["projectId", "datasetId"], "parameters": {"datasetId": {"description": "Required. Dataset ID of the new routine", "location": "path", "pattern": "^[^/]+$", "required": true, "type": "string"}, "projectId": {"description": "Required. Project ID of the new routine", "location": "path", "pattern": "^[^/]+$", "required": true, "type": "string"}}, "path": "projects/{+projectId}/datasets/{+datasetId}/routines", "request": {"$ref": "Routine"}, "response": {"$ref": "Routine"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists all routines in the specified dataset. Requires the READER dataset role.", "flatPath": "projects/{projectsId}/datasets/{datasetsId}/routines", "httpMethod": "GET", "id": "bigquery.routines.list", "parameterOrder": ["projectId", "datasetId"], "parameters": {"datasetId": {"description": "Required. Dataset ID of the routines to list", "location": "path", "pattern": "^[^/]+$", "required": true, "type": "string"}, "filter": {"description": "If set, then only the Routines matching this filter are returned. The supported format is `routineType:{RoutineType}`, where `{RoutineType}` is a RoutineType enum. For example: `routineType:SCALAR_FUNCTION`.", "location": "query", "type": "string"}, "maxResults": {"description": "The maximum number of results to return in a single response page. Leverage the page tokens to iterate through the entire collection.", "format": "uint32", "location": "query", "type": "integer"}, "pageToken": {"description": "Page token, returned by a previous call, to request the next page of results", "location": "query", "type": "string"}, "projectId": {"description": "Required. Project ID of the routines to list", "location": "path", "pattern": "^[^/]+$", "required": true, "type": "string"}, "readMask": {"description": "If set, then only the Routine fields in the field mask, as well as project_id, dataset_id and routine_id, are returned in the response. If unset, then the following Routine fields are returned: etag, project_id, dataset_id, routine_id, routine_type, creation_time, last_modified_time, and language.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "projects/{+projectId}/datasets/{+datasetId}/routines", "response": {"$ref": "ListRoutinesResponse"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"]}, "setIamPolicy": {"description": "Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.", "flatPath": "projects/{projectsId}/datasets/{datasetsId}/routines/{routinesId}:setIamPolicy", "httpMethod": "POST", "id": "bigquery.routines.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/datasets/[^/]+/routines/[^/]+$", "required": true, "type": "string"}}, "path": "{+resource}:setIamPolicy", "request": {"$ref": "SetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"]}, "update": {"description": "Updates information in an existing routine. The update method replaces the entire Routine resource.", "flatPath": "projects/{projectsId}/datasets/{datasetsId}/routines/{routinesId}", "httpMethod": "PUT", "id": "bigquery.routines.update", "parameterOrder": ["projectId", "datasetId", "routineId"], "parameters": {"datasetId": {"description": "Required. Dataset ID of the routine to update", "location": "path", "pattern": "^[^/]+$", "required": true, "type": "string"}, "projectId": {"description": "Required. Project ID of the routine to update", "location": "path", "pattern": "^[^/]+$", "required": true, "type": "string"}, "routineId": {"description": "Required. Routine ID of the routine to update", "location": "path", "pattern": "^[^/]+$", "required": true, "type": "string"}}, "path": "projects/{+projectId}/datasets/{+datasetId}/routines/{+routineId}", "request": {"$ref": "Routine"}, "response": {"$ref": "Routine"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"]}}}, "rowAccessPolicies": {"methods": {"batchDelete": {"description": "Deletes provided row access policies.", "flatPath": "projects/{projectsId}/datasets/{datasetsId}/tables/{tablesId}/rowAccessPolicies:batchDelete", "httpMethod": "POST", "id": "bigquery.rowAccessPolicies.batchDelete", "parameterOrder": ["projectId", "datasetId", "tableId"], "parameters": {"datasetId": {"description": "Required. Dataset ID of the table to delete the row access policies.", "location": "path", "pattern": "^[^/]+$", "required": true, "type": "string"}, "projectId": {"description": "Required. Project ID of the table to delete the row access policies.", "location": "path", "pattern": "^[^/]+$", "required": true, "type": "string"}, "tableId": {"description": "Required. Table ID of the table to delete the row access policies.", "location": "path", "pattern": "^[^/]+$", "required": true, "type": "string"}}, "path": "projects/{+projectId}/datasets/{+datasetId}/tables/{+tableId}/rowAccessPolicies:batchDelete", "request": {"$ref": "BatchDeleteRowAccessPoliciesRequest"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"]}, "delete": {"description": "Deletes a row access policy.", "flatPath": "projects/{projectsId}/datasets/{datasetsId}/tables/{tablesId}/rowAccessPolicies/{rowAccessPoliciesId}", "httpMethod": "DELETE", "id": "bigquery.rowAccessPolicies.delete", "parameterOrder": ["projectId", "datasetId", "tableId", "policyId"], "parameters": {"datasetId": {"description": "Required. Dataset ID of the table to delete the row access policy.", "location": "path", "pattern": "^[^/]+$", "required": true, "type": "string"}, "force": {"description": "If set to true, it deletes the row access policy even if it's the last row access policy on the table and the deletion will widen the access rather narrowing it.", "location": "query", "type": "boolean"}, "policyId": {"description": "Required. Policy ID of the row access policy.", "location": "path", "pattern": "^[^/]+$", "required": true, "type": "string"}, "projectId": {"description": "Required. Project ID of the table to delete the row access policy.", "location": "path", "pattern": "^[^/]+$", "required": true, "type": "string"}, "tableId": {"description": "Required. Table ID of the table to delete the row access policy.", "location": "path", "pattern": "^[^/]+$", "required": true, "type": "string"}}, "path": "projects/{+projectId}/datasets/{+datasetId}/tables/{+tableId}/rowAccessPolicies/{+policyId}", "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"]}, "get": {"description": "Gets the specified row access policy by policy ID.", "flatPath": "projects/{projectsId}/datasets/{datasetsId}/tables/{tablesId}/rowAccessPolicies/{rowAccessPoliciesId}", "httpMethod": "GET", "id": "bigquery.rowAccessPolicies.get", "parameterOrder": ["projectId", "datasetId", "tableId", "policyId"], "parameters": {"datasetId": {"description": "Required. Dataset ID of the table to get the row access policy.", "location": "path", "pattern": "^[^/]+$", "required": true, "type": "string"}, "policyId": {"description": "Required. Policy ID of the row access policy.", "location": "path", "pattern": "^[^/]+$", "required": true, "type": "string"}, "projectId": {"description": "Required. Project ID of the table to get the row access policy.", "location": "path", "pattern": "^[^/]+$", "required": true, "type": "string"}, "tableId": {"description": "Required. Table ID of the table to get the row access policy.", "location": "path", "pattern": "^[^/]+$", "required": true, "type": "string"}}, "path": "projects/{+projectId}/datasets/{+datasetId}/tables/{+tableId}/rowAccessPolicies/{+policyId}", "response": {"$ref": "RowAccessPolicy"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"]}, "getIamPolicy": {"description": "Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.", "flatPath": "projects/{projectsId}/datasets/{datasetsId}/tables/{tablesId}/rowAccessPolicies/{rowAccessPoliciesId}:getIamPolicy", "httpMethod": "POST", "id": "bigquery.rowAccessPolicies.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/datasets/[^/]+/tables/[^/]+/rowAccessPolicies/[^/]+$", "required": true, "type": "string"}}, "path": "{+resource}:getIamPolicy", "request": {"$ref": "GetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"]}, "insert": {"description": "Creates a row access policy.", "flatPath": "projects/{projectsId}/datasets/{datasetsId}/tables/{tablesId}/rowAccessPolicies", "httpMethod": "POST", "id": "bigquery.rowAccessPolicies.insert", "parameterOrder": ["projectId", "datasetId", "tableId"], "parameters": {"datasetId": {"description": "Required. Dataset ID of the table to get the row access policy.", "location": "path", "pattern": "^[^/]+$", "required": true, "type": "string"}, "projectId": {"description": "Required. Project ID of the table to get the row access policy.", "location": "path", "pattern": "^[^/]+$", "required": true, "type": "string"}, "tableId": {"description": "Required. Table ID of the table to get the row access policy.", "location": "path", "pattern": "^[^/]+$", "required": true, "type": "string"}}, "path": "projects/{+projectId}/datasets/{+datasetId}/tables/{+tableId}/rowAccessPolicies", "request": {"$ref": "RowAccessPolicy"}, "response": {"$ref": "RowAccessPolicy"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"]}, "list": {"description": "Lists all row access policies on the specified table.", "flatPath": "projects/{projectsId}/datasets/{datasetsId}/tables/{tablesId}/rowAccessPolicies", "httpMethod": "GET", "id": "bigquery.rowAccessPolicies.list", "parameterOrder": ["projectId", "datasetId", "tableId"], "parameters": {"datasetId": {"description": "Required. Dataset ID of row access policies to list.", "location": "path", "pattern": "^[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "The maximum number of results to return in a single response page. Leverage the page tokens to iterate through the entire collection.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Page token, returned by a previous call, to request the next page of results.", "location": "query", "type": "string"}, "projectId": {"description": "Required. Project ID of the row access policies to list.", "location": "path", "pattern": "^[^/]+$", "required": true, "type": "string"}, "tableId": {"description": "Required. Table ID of the table to list row access policies.", "location": "path", "pattern": "^[^/]+$", "required": true, "type": "string"}}, "path": "projects/{+projectId}/datasets/{+datasetId}/tables/{+tableId}/rowAccessPolicies", "response": {"$ref": "ListRowAccessPoliciesResponse"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"]}, "testIamPermissions": {"description": "Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may \"fail open\" without warning.", "flatPath": "projects/{projectsId}/datasets/{datasetsId}/tables/{tablesId}/rowAccessPolicies/{rowAccessPoliciesId}:testIamPermissions", "httpMethod": "POST", "id": "bigquery.rowAccessPolicies.testIamPermissions", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/datasets/[^/]+/tables/[^/]+/rowAccessPolicies/[^/]+$", "required": true, "type": "string"}}, "path": "{+resource}:testIamPermissions", "request": {"$ref": "TestIamPermissionsRequest"}, "response": {"$ref": "TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"]}, "update": {"description": "Updates a row access policy.", "flatPath": "projects/{projectsId}/datasets/{datasetsId}/tables/{tablesId}/rowAccessPolicies/{rowAccessPoliciesId}", "httpMethod": "PUT", "id": "bigquery.rowAccessPolicies.update", "parameterOrder": ["projectId", "datasetId", "tableId", "policyId"], "parameters": {"datasetId": {"description": "Required. Dataset ID of the table to get the row access policy.", "location": "path", "pattern": "^[^/]+$", "required": true, "type": "string"}, "policyId": {"description": "Required. Policy ID of the row access policy.", "location": "path", "pattern": "^[^/]+$", "required": true, "type": "string"}, "projectId": {"description": "Required. Project ID of the table to get the row access policy.", "location": "path", "pattern": "^[^/]+$", "required": true, "type": "string"}, "tableId": {"description": "Required. Table ID of the table to get the row access policy.", "location": "path", "pattern": "^[^/]+$", "required": true, "type": "string"}}, "path": "projects/{+projectId}/datasets/{+datasetId}/tables/{+tableId}/rowAccessPolicies/{+policyId}", "request": {"$ref": "RowAccessPolicy"}, "response": {"$ref": "RowAccessPolicy"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"]}}}, "tabledata": {"methods": {"insertAll": {"description": "Streams data into BigQuery one record at a time without needing to run a load job.", "flatPath": "projects/{projectsId}/datasets/{datasetsId}/tables/{tablesId}/insertAll", "httpMethod": "POST", "id": "bigquery.tabledata.insertAll", "parameterOrder": ["projectId", "datasetId", "tableId"], "parameters": {"datasetId": {"description": "Required. Dataset ID of the destination.", "location": "path", "pattern": "^[^/]+$", "required": true, "type": "string"}, "projectId": {"description": "Required. Project ID of the destination.", "location": "path", "pattern": "^[^/]+$", "required": true, "type": "string"}, "tableId": {"description": "Required. Table ID of the destination.", "location": "path", "pattern": "^[^/]+$", "required": true, "type": "string"}}, "path": "projects/{+projectId}/datasets/{+datasetId}/tables/{+tableId}/insertAll", "request": {"$ref": "TableDataInsertAllRequest"}, "response": {"$ref": "TableDataInsertAllResponse"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/bigquery.insertdata", "https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "List the content of a table in rows.", "flatPath": "projects/{projectsId}/datasets/{datasetsId}/tables/{tablesId}/data", "httpMethod": "GET", "id": "bigquery.tabledata.list", "parameterOrder": ["projectId", "datasetId", "tableId"], "parameters": {"datasetId": {"description": "Required. Dataset id of the table to list.", "location": "path", "pattern": "^[^/]+$", "required": true, "type": "string"}, "formatOptions.useInt64Timestamp": {"description": "Optional. Output timestamp as usec int64. Default is false.", "location": "query", "type": "boolean"}, "maxResults": {"description": "Row limit of the table.", "format": "uint32", "location": "query", "type": "integer"}, "pageToken": {"description": "To retrieve the next page of table data, set this field to the string provided in the pageToken field of the response body from your previous call to tabledata.list.", "location": "query", "type": "string"}, "projectId": {"description": "Required. Project id of the table to list.", "location": "path", "pattern": "^[^/]+$", "required": true, "type": "string"}, "selectedFields": {"description": "Subset of fields to return, supports select into sub fields. Example: selected_fields = \"a,e.d.f\";", "location": "query", "type": "string"}, "startIndex": {"description": "Start row index of the table.", "format": "uint64", "location": "query", "type": "string"}, "tableId": {"description": "Required. Table id of the table to list.", "location": "path", "pattern": "^[^/]+$", "required": true, "type": "string"}}, "path": "projects/{+projectId}/datasets/{+datasetId}/tables/{+tableId}/data", "response": {"$ref": "TableDataList"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"]}}}, "tables": {"methods": {"delete": {"description": "Deletes the table specified by tableId from the dataset. If the table contains data, all the data will be deleted.", "flatPath": "projects/{projectsId}/datasets/{datasetsId}/tables/{tablesId}", "httpMethod": "DELETE", "id": "bigquery.tables.delete", "parameterOrder": ["projectId", "datasetId", "tableId"], "parameters": {"datasetId": {"description": "Required. Dataset ID of the table to delete", "location": "path", "pattern": "^[^/]+$", "required": true, "type": "string"}, "projectId": {"description": "Required. Project ID of the table to delete", "location": "path", "pattern": "^[^/]+$", "required": true, "type": "string"}, "tableId": {"description": "Required. Table ID of the table to delete", "location": "path", "pattern": "^[^/]+$", "required": true, "type": "string"}}, "path": "projects/{+projectId}/datasets/{+datasetId}/tables/{+tableId}", "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets the specified table resource by table ID. This method does not return the data in the table, it only returns the table resource, which describes the structure of this table.", "flatPath": "projects/{projectsId}/datasets/{datasetsId}/tables/{tablesId}", "httpMethod": "GET", "id": "bigquery.tables.get", "parameterOrder": ["projectId", "datasetId", "tableId"], "parameters": {"datasetId": {"description": "Required. Dataset ID of the requested table", "location": "path", "pattern": "^[^/]+$", "required": true, "type": "string"}, "projectId": {"description": "Required. Project ID of the requested table", "location": "path", "pattern": "^[^/]+$", "required": true, "type": "string"}, "selectedFields": {"description": "List of table schema fields to return (comma-separated). If unspecified, all fields are returned. A fieldMask cannot be used here because the fields will automatically be converted from camelCase to snake_case and the conversion will fail if there are underscores. Since these are fields in BigQuery table schemas, underscores are allowed.", "location": "query", "type": "string"}, "tableId": {"description": "Required. Table ID of the requested table", "location": "path", "pattern": "^[^/]+$", "required": true, "type": "string"}, "view": {"description": "Optional. Specifies the view that determines which table information is returned. By default, basic table information and storage statistics (STORAGE_STATS) are returned.", "enum": ["TABLE_METADATA_VIEW_UNSPECIFIED", "BASIC", "STORAGE_STATS", "FULL"], "enumDescriptions": ["The default value. Default to the STORAGE_STATS view.", "Includes basic table information including schema and partitioning specification. This view does not include storage statistics such as numRows or numBytes. This view is significantly more efficient and should be used to support high query rates.", "Includes all information in the BASIC view as well as storage statistics (numBytes, numLongTermBytes, numRows and lastModifiedTime).", "Includes all table information, including storage statistics. It returns same information as STORAGE_STATS view, but may contain additional information in the future."], "location": "query", "type": "string"}}, "path": "projects/{+projectId}/datasets/{+datasetId}/tables/{+tableId}", "response": {"$ref": "Table"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"]}, "getIamPolicy": {"description": "Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.", "flatPath": "projects/{projectsId}/datasets/{datasetsId}/tables/{tablesId}:getIamPolicy", "httpMethod": "POST", "id": "bigquery.tables.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/datasets/[^/]+/tables/[^/]+$", "required": true, "type": "string"}}, "path": "{+resource}:getIamPolicy", "request": {"$ref": "GetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"]}, "insert": {"description": "Creates a new, empty table in the dataset.", "flatPath": "projects/{projectsId}/datasets/{datasetsId}/tables", "httpMethod": "POST", "id": "bigquery.tables.insert", "parameterOrder": ["projectId", "datasetId"], "parameters": {"datasetId": {"description": "Required. Dataset ID of the new table", "location": "path", "pattern": "^[^/]+$", "required": true, "type": "string"}, "projectId": {"description": "Required. Project ID of the new table", "location": "path", "pattern": "^[^/]+$", "required": true, "type": "string"}}, "path": "projects/{+projectId}/datasets/{+datasetId}/tables", "request": {"$ref": "Table"}, "response": {"$ref": "Table"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists all tables in the specified dataset. Requires the READER dataset role.", "flatPath": "projects/{projectsId}/datasets/{datasetsId}/tables", "httpMethod": "GET", "id": "bigquery.tables.list", "parameterOrder": ["projectId", "datasetId"], "parameters": {"datasetId": {"description": "Required. Dataset ID of the tables to list", "location": "path", "pattern": "^[^/]+$", "required": true, "type": "string"}, "maxResults": {"description": "The maximum number of results to return in a single response page. Leverage the page tokens to iterate through the entire collection.", "format": "uint32", "location": "query", "type": "integer"}, "pageToken": {"description": "Page token, returned by a previous call, to request the next page of results", "location": "query", "type": "string"}, "projectId": {"description": "Required. Project ID of the tables to list", "location": "path", "pattern": "^[^/]+$", "required": true, "type": "string"}}, "path": "projects/{+projectId}/datasets/{+datasetId}/tables", "response": {"$ref": "TableList"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"]}, "patch": {"description": "Updates information in an existing table. The update method replaces the entire table resource, whereas the patch method only replaces fields that are provided in the submitted table resource. This method supports RFC5789 patch semantics.", "flatPath": "projects/{projectsId}/datasets/{datasetsId}/tables/{tablesId}", "httpMethod": "PATCH", "id": "bigquery.tables.patch", "parameterOrder": ["projectId", "datasetId", "tableId"], "parameters": {"autodetect_schema": {"description": "Optional.  When true will autodetect schema, else will keep original schema", "location": "query", "type": "boolean"}, "datasetId": {"description": "Required. Dataset ID of the table to update", "location": "path", "pattern": "^[^/]+$", "required": true, "type": "string"}, "projectId": {"description": "Required. Project ID of the table to update", "location": "path", "pattern": "^[^/]+$", "required": true, "type": "string"}, "tableId": {"description": "Required. Table ID of the table to update", "location": "path", "pattern": "^[^/]+$", "required": true, "type": "string"}}, "path": "projects/{+projectId}/datasets/{+datasetId}/tables/{+tableId}", "request": {"$ref": "Table"}, "response": {"$ref": "Table"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"]}, "setIamPolicy": {"description": "Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.", "flatPath": "projects/{projectsId}/datasets/{datasetsId}/tables/{tablesId}:setIamPolicy", "httpMethod": "POST", "id": "bigquery.tables.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/datasets/[^/]+/tables/[^/]+$", "required": true, "type": "string"}}, "path": "{+resource}:setIamPolicy", "request": {"$ref": "SetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"]}, "testIamPermissions": {"description": "Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may \"fail open\" without warning.", "flatPath": "projects/{projectsId}/datasets/{datasetsId}/tables/{tablesId}:testIamPermissions", "httpMethod": "POST", "id": "bigquery.tables.testIamPermissions", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/datasets/[^/]+/tables/[^/]+$", "required": true, "type": "string"}}, "path": "{+resource}:testIamPermissions", "request": {"$ref": "TestIamPermissionsRequest"}, "response": {"$ref": "TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"]}, "update": {"description": "Updates information in an existing table. The update method replaces the entire Table resource, whereas the patch method only replaces fields that are provided in the submitted Table resource.", "flatPath": "projects/{projectsId}/datasets/{datasetsId}/tables/{tablesId}", "httpMethod": "PUT", "id": "bigquery.tables.update", "parameterOrder": ["projectId", "datasetId", "tableId"], "parameters": {"autodetect_schema": {"description": "Optional.  When true will autodetect schema, else will keep original schema", "location": "query", "type": "boolean"}, "datasetId": {"description": "Required. Dataset ID of the table to update", "location": "path", "pattern": "^[^/]+$", "required": true, "type": "string"}, "projectId": {"description": "Required. Project ID of the table to update", "location": "path", "pattern": "^[^/]+$", "required": true, "type": "string"}, "tableId": {"description": "Required. Table ID of the table to update", "location": "path", "pattern": "^[^/]+$", "required": true, "type": "string"}}, "path": "projects/{+projectId}/datasets/{+datasetId}/tables/{+tableId}", "request": {"$ref": "Table"}, "response": {"$ref": "Table"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"]}}}}, "revision": "20250511", "rootUrl": "https://bigquery.googleapis.com/", "schemas": {"AggregateClassificationMetrics": {"description": "Aggregate metrics for classification/classifier models. For multi-class models, the metrics are either macro-averaged or micro-averaged. When macro-averaged, the metrics are calculated for each label and then an unweighted average is taken of those values. When micro-averaged, the metric is calculated globally by counting the total number of correctly predicted rows.", "id": "AggregateClassificationMetrics", "properties": {"accuracy": {"description": "Accuracy is the fraction of predictions given the correct label. For multiclass this is a micro-averaged metric.", "format": "double", "type": "number"}, "f1Score": {"description": "The F1 score is an average of recall and precision. For multiclass this is a macro-averaged metric.", "format": "double", "type": "number"}, "logLoss": {"description": "Logarithmic Loss. For multiclass this is a macro-averaged metric.", "format": "double", "type": "number"}, "precision": {"description": "Precision is the fraction of actual positive predictions that had positive actual labels. For multiclass this is a macro-averaged metric treating each class as a binary classifier.", "format": "double", "type": "number"}, "recall": {"description": "Recall is the fraction of actual positive labels that were given a positive prediction. For multiclass this is a macro-averaged metric.", "format": "double", "type": "number"}, "rocAuc": {"description": "Area Under a ROC Curve. For multiclass this is a macro-averaged metric.", "format": "double", "type": "number"}, "threshold": {"description": "Threshold at which the metrics are computed. For binary classification models this is the positive class threshold. For multi-class classification models this is the confidence threshold.", "format": "double", "type": "number"}}, "type": "object"}, "AggregationThresholdPolicy": {"description": "Represents privacy policy associated with \"aggregation threshold\" method.", "id": "AggregationThresholdPolicy", "properties": {"privacyUnitColumns": {"description": "Optional. The privacy unit column(s) associated with this policy. For now, only one column per data source object (table, view) is allowed as a privacy unit column. Representing as a repeated field in metadata for extensibility to multiple columns in future. Duplicates and Repeated struct fields are not allowed. For nested fields, use dot notation (\"outer.inner\")", "items": {"type": "string"}, "type": "array"}, "threshold": {"description": "Optional. The threshold for the \"aggregation threshold\" policy.", "format": "int64", "type": "string"}}, "type": "object"}, "Argument": {"description": "Input/output argument of a function or a stored procedure.", "id": "Argument", "properties": {"argumentKind": {"description": "Optional. Defaults to FIXED_TYPE.", "enum": ["ARGUMENT_KIND_UNSPECIFIED", "FIXED_TYPE", "ANY_TYPE"], "enumDescriptions": ["Default value.", "The argument is a variable with fully specified type, which can be a struct or an array, but not a table.", "The argument is any type, including struct or array, but not a table."], "type": "string"}, "dataType": {"$ref": "StandardSqlDataType", "description": "Set if argument_kind == FIXED_TYPE."}, "isAggregate": {"description": "Optional. Whether the argument is an aggregate function parameter. Must be Unset for routine types other than AGGREGATE_FUNCTION. For AGGREGATE_FUNCTION, if set to false, it is equivalent to adding \"NOT AGGREGATE\" clause in DDL; Otherwise, it is equivalent to omitting \"NOT AGGREGATE\" clause in DDL.", "type": "boolean"}, "mode": {"description": "Optional. Specifies whether the argument is input or output. Can be set for procedures only.", "enum": ["MODE_UNSPECIFIED", "IN", "OUT", "INOUT"], "enumDescriptions": ["Default value.", "The argument is input-only.", "The argument is output-only.", "The argument is both an input and an output."], "type": "string"}, "name": {"description": "Optional. The name of this argument. Can be absent for function return argument.", "type": "string"}}, "type": "object"}, "ArimaCoefficients": {"description": "Arima coefficients.", "id": "ArimaCoefficients", "properties": {"autoRegressiveCoefficients": {"description": "Auto-regressive coefficients, an array of double.", "items": {"format": "double", "type": "number"}, "type": "array"}, "interceptCoefficient": {"description": "Intercept coefficient, just a double not an array.", "format": "double", "type": "number"}, "movingAverageCoefficients": {"description": "Moving-average coefficients, an array of double.", "items": {"format": "double", "type": "number"}, "type": "array"}}, "type": "object"}, "ArimaFittingMetrics": {"description": "ARIMA model fitting metrics.", "id": "ArimaFittingMetrics", "properties": {"aic": {"description": "AIC.", "format": "double", "type": "number"}, "logLikelihood": {"description": "Log-likelihood.", "format": "double", "type": "number"}, "variance": {"description": "Varian<PERSON>.", "format": "double", "type": "number"}}, "type": "object"}, "ArimaForecastingMetrics": {"description": "Model evaluation metrics for ARIMA forecasting models.", "id": "ArimaForecastingMetrics", "properties": {"arimaFittingMetrics": {"deprecated": true, "description": "Arima model fitting metrics.", "items": {"$ref": "ArimaFittingMetrics"}, "type": "array"}, "arimaSingleModelForecastingMetrics": {"description": "Repeated as there can be many metric sets (one for each model) in auto-arima and the large-scale case.", "items": {"$ref": "ArimaSingleModelForecastingMetrics"}, "type": "array"}, "hasDrift": {"deprecated": true, "description": "Whether Arima model fitted with drift or not. It is always false when d is not 1.", "items": {"type": "boolean"}, "type": "array"}, "nonSeasonalOrder": {"deprecated": true, "description": "Non-seasonal order.", "items": {"$ref": "ArimaOrder"}, "type": "array"}, "seasonalPeriods": {"deprecated": true, "description": "Seasonal periods. Repeated because multiple periods are supported for one time series.", "items": {"enum": ["SEASONAL_PERIOD_TYPE_UNSPECIFIED", "NO_SEASONALITY", "DAILY", "WEEKLY", "MONTHLY", "QUARTERLY", "YEARLY"], "enumDescriptions": ["Unspecified seasonal period.", "No seasonality", "Daily period, 24 hours.", "Weekly period, 7 days.", "Monthly period, 30 days or irregular.", "Quarterly period, 90 days or irregular.", "Yearly period, 365 days or irregular."], "type": "string"}, "type": "array"}, "timeSeriesId": {"deprecated": true, "description": "Id to differentiate different time series for the large-scale case.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ArimaModelInfo": {"description": "Arima model information.", "id": "ArimaModelInfo", "properties": {"arimaCoefficients": {"$ref": "ArimaCoefficients", "description": "Arima coefficients."}, "arimaFittingMetrics": {"$ref": "ArimaFittingMetrics", "description": "Arima fitting metrics."}, "hasDrift": {"description": "Whether Arima model fitted with drift or not. It is always false when d is not 1.", "type": "boolean"}, "hasHolidayEffect": {"description": "If true, holiday_effect is a part of time series decomposition result.", "type": "boolean"}, "hasSpikesAndDips": {"description": "If true, spikes_and_dips is a part of time series decomposition result.", "type": "boolean"}, "hasStepChanges": {"description": "If true, step_changes is a part of time series decomposition result.", "type": "boolean"}, "nonSeasonalOrder": {"$ref": "ArimaOrder", "description": "Non-seasonal order."}, "seasonalPeriods": {"description": "Seasonal periods. Repeated because multiple periods are supported for one time series.", "items": {"enum": ["SEASONAL_PERIOD_TYPE_UNSPECIFIED", "NO_SEASONALITY", "DAILY", "WEEKLY", "MONTHLY", "QUARTERLY", "YEARLY"], "enumDescriptions": ["Unspecified seasonal period.", "No seasonality", "Daily period, 24 hours.", "Weekly period, 7 days.", "Monthly period, 30 days or irregular.", "Quarterly period, 90 days or irregular.", "Yearly period, 365 days or irregular."], "type": "string"}, "type": "array"}, "timeSeriesId": {"description": "The time_series_id value for this time series. It will be one of the unique values from the time_series_id_column specified during ARIMA model training. Only present when time_series_id_column training option was used.", "type": "string"}, "timeSeriesIds": {"description": "The tuple of time_series_ids identifying this time series. It will be one of the unique tuples of values present in the time_series_id_columns specified during ARIMA model training. Only present when time_series_id_columns training option was used and the order of values here are same as the order of time_series_id_columns.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ArimaOrder": {"description": "Arima order, can be used for both non-seasonal and seasonal parts.", "id": "ArimaOrder", "properties": {"d": {"description": "Order of the differencing part.", "format": "int64", "type": "string"}, "p": {"description": "Order of the autoregressive part.", "format": "int64", "type": "string"}, "q": {"description": "Order of the moving-average part.", "format": "int64", "type": "string"}}, "type": "object"}, "ArimaResult": {"description": "(Auto-)arima fitting result. Wrap everything in ArimaResult for easier refactoring if we want to use model-specific iteration results.", "id": "ArimaResult", "properties": {"arimaModelInfo": {"description": "This message is repeated because there are multiple arima models fitted in auto-arima. For non-auto-arima model, its size is one.", "items": {"$ref": "ArimaModelInfo"}, "type": "array"}, "seasonalPeriods": {"description": "Seasonal periods. Repeated because multiple periods are supported for one time series.", "items": {"enum": ["SEASONAL_PERIOD_TYPE_UNSPECIFIED", "NO_SEASONALITY", "DAILY", "WEEKLY", "MONTHLY", "QUARTERLY", "YEARLY"], "enumDescriptions": ["Unspecified seasonal period.", "No seasonality", "Daily period, 24 hours.", "Weekly period, 7 days.", "Monthly period, 30 days or irregular.", "Quarterly period, 90 days or irregular.", "Yearly period, 365 days or irregular."], "type": "string"}, "type": "array"}}, "type": "object"}, "ArimaSingleModelForecastingMetrics": {"description": "Model evaluation metrics for a single ARIMA forecasting model.", "id": "ArimaSingleModelForecastingMetrics", "properties": {"arimaFittingMetrics": {"$ref": "ArimaFittingMetrics", "description": "Arima fitting metrics."}, "hasDrift": {"description": "Is arima model fitted with drift or not. It is always false when d is not 1.", "type": "boolean"}, "hasHolidayEffect": {"description": "If true, holiday_effect is a part of time series decomposition result.", "type": "boolean"}, "hasSpikesAndDips": {"description": "If true, spikes_and_dips is a part of time series decomposition result.", "type": "boolean"}, "hasStepChanges": {"description": "If true, step_changes is a part of time series decomposition result.", "type": "boolean"}, "nonSeasonalOrder": {"$ref": "ArimaOrder", "description": "Non-seasonal order."}, "seasonalPeriods": {"description": "Seasonal periods. Repeated because multiple periods are supported for one time series.", "items": {"enum": ["SEASONAL_PERIOD_TYPE_UNSPECIFIED", "NO_SEASONALITY", "DAILY", "WEEKLY", "MONTHLY", "QUARTERLY", "YEARLY"], "enumDescriptions": ["Unspecified seasonal period.", "No seasonality", "Daily period, 24 hours.", "Weekly period, 7 days.", "Monthly period, 30 days or irregular.", "Quarterly period, 90 days or irregular.", "Yearly period, 365 days or irregular."], "type": "string"}, "type": "array"}, "timeSeriesId": {"description": "The time_series_id value for this time series. It will be one of the unique values from the time_series_id_column specified during ARIMA model training. Only present when time_series_id_column training option was used.", "type": "string"}, "timeSeriesIds": {"description": "The tuple of time_series_ids identifying this time series. It will be one of the unique tuples of values present in the time_series_id_columns specified during ARIMA model training. Only present when time_series_id_columns training option was used and the order of values here are same as the order of time_series_id_columns.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "AuditConfig": {"description": "Specifies the audit configuration for a service. The configuration determines which permission types are logged, and what identities, if any, are exempted from logging. An AuditConfig must have one or more AuditLogConfigs. If there are AuditConfigs for both `allServices` and a specific service, the union of the two AuditConfigs is used for that service: the log_types specified in each AuditConfig are enabled, and the exempted_members in each AuditLogConfig are exempted. Example Policy with multiple AuditConfigs: { \"audit_configs\": [ { \"service\": \"allServices\", \"audit_log_configs\": [ { \"log_type\": \"DATA_READ\", \"exempted_members\": [ \"user:<EMAIL>\" ] }, { \"log_type\": \"DATA_WRITE\" }, { \"log_type\": \"ADMIN_READ\" } ] }, { \"service\": \"sampleservice.googleapis.com\", \"audit_log_configs\": [ { \"log_type\": \"DATA_READ\" }, { \"log_type\": \"DATA_WRITE\", \"exempted_members\": [ \"user:<EMAIL>\" ] } ] } ] } For sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ logging. It also exempts `<EMAIL>` from DATA_READ logging, and `<EMAIL>` from DATA_WRITE logging.", "id": "AuditConfig", "properties": {"auditLogConfigs": {"description": "The configuration for logging of each type of permission.", "items": {"$ref": "AuditLogConfig"}, "type": "array"}, "service": {"description": "Specifies a service that will be enabled for audit logging. For example, `storage.googleapis.com`, `cloudsql.googleapis.com`. `allServices` is a special value that covers all services.", "type": "string"}}, "type": "object"}, "AuditLogConfig": {"description": "Provides the configuration for logging a type of permissions. Example: { \"audit_log_configs\": [ { \"log_type\": \"DATA_READ\", \"exempted_members\": [ \"user:<EMAIL>\" ] }, { \"log_type\": \"DATA_WRITE\" } ] } This enables 'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from DATA_READ logging.", "id": "AuditLogConfig", "properties": {"exemptedMembers": {"description": "Specifies the identities that do not cause logging for this type of permission. Follows the same format of Binding.members.", "items": {"type": "string"}, "type": "array"}, "logType": {"description": "The log type that this config enables.", "enum": ["LOG_TYPE_UNSPECIFIED", "ADMIN_READ", "DATA_WRITE", "DATA_READ"], "enumDescriptions": ["Default case. Should never be this.", "Admin reads. Example: CloudIAM getIamPolicy", "Data writes. Example: CloudSQL Users create", "Data reads. Example: CloudSQL Users list"], "type": "string"}}, "type": "object"}, "AvroOptions": {"description": "Options for external data sources.", "id": "AvroOptions", "properties": {"useAvroLogicalTypes": {"description": "Optional. If sourceFormat is set to \"AVRO\", indicates whether to interpret logical types as the corresponding BigQuery data type (for example, TIMESTAMP), instead of using the raw type (for example, INTEGER).", "type": "boolean"}}, "type": "object"}, "BatchDeleteRowAccessPoliciesRequest": {"description": "Request message for the BatchDeleteRowAccessPoliciesRequest method.", "id": "BatchDeleteRowAccessPoliciesRequest", "properties": {"force": {"description": "If set to true, it deletes the row access policy even if it's the last row access policy on the table and the deletion will widen the access rather narrowing it.", "type": "boolean"}, "policyIds": {"description": "Required. Policy IDs of the row access policies.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "BiEngineReason": {"description": "Reason why BI Engine didn't accelerate the query (or sub-query).", "id": "BiEngineReason", "properties": {"code": {"description": "Output only. High-level BI Engine reason for partial or disabled acceleration", "enum": ["CODE_UNSPECIFIED", "NO_RESERVATION", "INSUFFICIENT_RESERVATION", "UNSUPPORTED_SQL_TEXT", "INPUT_TOO_LARGE", "OTHER_REASON", "TABLE_EXCLUDED"], "enumDescriptions": ["BiEngineReason not specified.", "No reservation available for BI Engine acceleration.", "Not enough memory available for BI Engine acceleration.", "This particular SQL text is not supported for acceleration by BI Engine.", "Input too large for acceleration by BI Engine.", "Catch-all code for all other cases for partial or disabled acceleration.", "One or more tables were not eligible for BI Engine acceleration."], "readOnly": true, "type": "string"}, "message": {"description": "Output only. Free form human-readable reason for partial or disabled acceleration.", "readOnly": true, "type": "string"}}, "type": "object"}, "BiEngineStatistics": {"description": "Statistics for a BI Engine specific query. Populated as part of JobStatistics2", "id": "BiEngineStatistics", "properties": {"accelerationMode": {"description": "Output only. Specifies which mode of BI Engine acceleration was performed (if any).", "enum": ["BI_ENGINE_ACCELERATION_MODE_UNSPECIFIED", "BI_ENGINE_DISABLED", "PARTIAL_INPUT", "FULL_INPUT", "FULL_QUERY"], "enumDescriptions": ["BiEngineMode type not specified.", "BI Engine acceleration was attempted but disabled. bi_engine_reasons specifies a more detailed reason.", "Some inputs were accelerated using BI Engine. See bi_engine_reasons for why parts of the query were not accelerated.", "All of the query inputs were accelerated using BI Engine.", "All of the query was accelerated using BI Engine."], "readOnly": true, "type": "string"}, "biEngineMode": {"description": "Output only. Specifies which mode of BI Engine acceleration was performed (if any).", "enum": ["ACCELERATION_MODE_UNSPECIFIED", "DISABLED", "PARTIAL", "FULL"], "enumDescriptions": ["BiEngineMode type not specified.", "BI Engine disabled the acceleration. bi_engine_reasons specifies a more detailed reason.", "Part of the query was accelerated using BI Engine. See bi_engine_reasons for why parts of the query were not accelerated.", "All of the query was accelerated using BI Engine."], "readOnly": true, "type": "string"}, "biEngineReasons": {"description": "In case of DISABLED or PARTIAL bi_engine_mode, these contain the explanatory reasons as to why BI Engine could not accelerate. In case the full query was accelerated, this field is not populated.", "items": {"$ref": "BiEngineReason"}, "type": "array"}}, "type": "object"}, "BigLakeConfiguration": {"description": "Configuration for BigQuery tables for Apache Iceberg (formerly BigLake managed tables.)", "id": "BigLakeConfiguration", "properties": {"connectionId": {"description": "Optional. The connection specifying the credentials to be used to read and write to external storage, such as Cloud Storage. The connection_id can have the form `{project}.{location}.{connection_id}` or `projects/{project}/locations/{location}/connections/{connection_id}\".", "type": "string"}, "fileFormat": {"description": "Optional. The file format the table data is stored in.", "enum": ["FILE_FORMAT_UNSPECIFIED", "PARQUET"], "enumDescriptions": ["Default Value.", "Apache Parquet format."], "type": "string"}, "storageUri": {"description": "Optional. The fully qualified location prefix of the external folder where table data is stored. The '*' wildcard character is not allowed. The URI should be in the format `gs://bucket/path_to_table/`", "type": "string"}, "tableFormat": {"description": "Optional. The table format the metadata only snapshots are stored in.", "enum": ["TABLE_FORMAT_UNSPECIFIED", "ICEBERG"], "enumDescriptions": ["Default Value.", "Apache Iceberg format."], "type": "string"}}, "type": "object"}, "BigQueryModelTraining": {"id": "BigQueryModelTraining", "properties": {"currentIteration": {"description": "Deprecated.", "format": "int32", "type": "integer"}, "expectedTotalIterations": {"description": "Deprecated.", "format": "int64", "type": "string"}}, "type": "object"}, "BigtableColumn": {"description": "Information related to a Bigtable column.", "id": "BigtableColumn", "properties": {"encoding": {"description": "Optional. The encoding of the values when the type is not STRING. Acceptable encoding values are: TEXT - indicates values are alphanumeric text strings. BINARY - indicates values are encoded using HBase Bytes.toBytes family of functions. 'encoding' can also be set at the column family level. However, the setting at this level takes precedence if 'encoding' is set at both levels.", "type": "string"}, "fieldName": {"description": "Optional. If the qualifier is not a valid BigQuery field identifier i.e. does not match a-zA-Z*, a valid identifier must be provided as the column field name and is used as field name in queries.", "type": "string"}, "onlyReadLatest": {"description": "Optional. If this is set, only the latest version of value in this column are exposed. 'onlyReadLatest' can also be set at the column family level. However, the setting at this level takes precedence if 'onlyReadLatest' is set at both levels.", "type": "boolean"}, "qualifierEncoded": {"description": "[Required] Qualifier of the column. Columns in the parent column family that has this exact qualifier are exposed as `.` field. If the qualifier is valid UTF-8 string, it can be specified in the qualifier_string field. Otherwise, a base-64 encoded value must be set to qualifier_encoded. The column field name is the same as the column qualifier. However, if the qualifier is not a valid BigQuery field identifier i.e. does not match a-zA-Z*, a valid identifier must be provided as field_name.", "format": "byte", "type": "string"}, "qualifierString": {"description": "Qualifier string.", "type": "string"}, "type": {"description": "Optional. The type to convert the value in cells of this column. The values are expected to be encoded using HBase Bytes.toBytes function when using the BINARY encoding value. Following BigQuery types are allowed (case-sensitive): * BYTES * STRING * INTEGER * FLOAT * BOOLEAN * JSO<PERSON> Default type is BYTES. 'type' can also be set at the column family level. However, the setting at this level takes precedence if 'type' is set at both levels.", "type": "string"}}, "type": "object"}, "BigtableColumnFamily": {"description": "Information related to a Bigtable column family.", "id": "BigtableColumnFamily", "properties": {"columns": {"description": "Optional. Lists of columns that should be exposed as individual fields as opposed to a list of (column name, value) pairs. All columns whose qualifier matches a qualifier in this list can be accessed as `.`. Other columns can be accessed as a list through the `.Column` field.", "items": {"$ref": "BigtableColumn"}, "type": "array"}, "encoding": {"description": "Optional. The encoding of the values when the type is not STRING. Acceptable encoding values are: TEXT - indicates values are alphanumeric text strings. BINARY - indicates values are encoded using HBase Bytes.toBytes family of functions. This can be overridden for a specific column by listing that column in 'columns' and specifying an encoding for it.", "type": "string"}, "familyId": {"description": "Identifier of the column family.", "type": "string"}, "onlyReadLatest": {"description": "Optional. If this is set only the latest version of value are exposed for all columns in this column family. This can be overridden for a specific column by listing that column in 'columns' and specifying a different setting for that column.", "type": "boolean"}, "type": {"description": "Optional. The type to convert the value in cells of this column family. The values are expected to be encoded using HBase Bytes.toBytes function when using the BINARY encoding value. Following BigQuery types are allowed (case-sensitive): * BYTES * STRING * INTEGER * FLOAT * BOOLEAN * JSO<PERSON> Default type is BYTES. This can be overridden for a specific column by listing that column in 'columns' and specifying a type for it.", "type": "string"}}, "type": "object"}, "BigtableOptions": {"description": "Options specific to Google Cloud Bigtable data sources.", "id": "BigtableOptions", "properties": {"columnFamilies": {"description": "Optional. List of column families to expose in the table schema along with their types. This list restricts the column families that can be referenced in queries and specifies their value types. You can use this list to do type conversions - see the 'type' field for more details. If you leave this list empty, all column families are present in the table schema and their values are read as BYTES. During a query only the column families referenced in that query are read from Bigtable.", "items": {"$ref": "BigtableColumnFamily"}, "type": "array"}, "ignoreUnspecifiedColumnFamilies": {"description": "Optional. If field is true, then the column families that are not specified in columnFamilies list are not exposed in the table schema. Otherwise, they are read with BYTES type values. The default value is false.", "type": "boolean"}, "outputColumnFamiliesAsJson": {"description": "Optional. If field is true, then each column family will be read as a single JSON column. Otherwise they are read as a repeated cell structure containing timestamp/value tuples. The default value is false.", "type": "boolean"}, "readRowkeyAsString": {"description": "Optional. If field is true, then the rowkey column families will be read and converted to string. Otherwise they are read with BYTES type values and users need to manually cast them with CAST if necessary. The default value is false.", "type": "boolean"}}, "type": "object"}, "BinaryClassificationMetrics": {"description": "Evaluation metrics for binary classification/classifier models.", "id": "BinaryClassificationMetrics", "properties": {"aggregateClassificationMetrics": {"$ref": "AggregateClassificationMetrics", "description": "Aggregate classification metrics."}, "binaryConfusionMatrixList": {"description": "Binary confusion matrix at multiple thresholds.", "items": {"$ref": "BinaryConfusionMatrix"}, "type": "array"}, "negativeLabel": {"description": "Label representing the negative class.", "type": "string"}, "positiveLabel": {"description": "Label representing the positive class.", "type": "string"}}, "type": "object"}, "BinaryConfusionMatrix": {"description": "Confusion matrix for binary classification models.", "id": "BinaryConfusionMatrix", "properties": {"accuracy": {"description": "The fraction of predictions given the correct label.", "format": "double", "type": "number"}, "f1Score": {"description": "The equally weighted average of recall and precision.", "format": "double", "type": "number"}, "falseNegatives": {"description": "Number of false samples predicted as false.", "format": "int64", "type": "string"}, "falsePositives": {"description": "Number of false samples predicted as true.", "format": "int64", "type": "string"}, "positiveClassThreshold": {"description": "Threshold value used when computing each of the following metric.", "format": "double", "type": "number"}, "precision": {"description": "The fraction of actual positive predictions that had positive actual labels.", "format": "double", "type": "number"}, "recall": {"description": "The fraction of actual positive labels that were given a positive prediction.", "format": "double", "type": "number"}, "trueNegatives": {"description": "Number of true samples predicted as false.", "format": "int64", "type": "string"}, "truePositives": {"description": "Number of true samples predicted as true.", "format": "int64", "type": "string"}}, "type": "object"}, "Binding": {"description": "Associates `members`, or principals, with a `role`.", "id": "Binding", "properties": {"condition": {"$ref": "Expr", "description": "The condition that is associated with this binding. If the condition evaluates to `true`, then this binding applies to the current request. If the condition evaluates to `false`, then this binding does not apply to the current request. However, a different role binding might grant the same role to one or more of the principals in this binding. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies)."}, "members": {"description": "Specifies the principals requesting access for a Google Cloud resource. `members` can have the following values: * `allUsers`: A special identifier that represents anyone who is on the internet; with or without a Google account. * `allAuthenticatedUsers`: A special identifier that represents anyone who is authenticated with a Google account or a service account. Does not include identities that come from external identity providers (IdPs) through identity federation. * `user:{emailid}`: An email address that represents a specific Google account. For example, `<EMAIL>` . * `serviceAccount:{emailid}`: An email address that represents a Google service account. For example, `<EMAIL>`. * `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`: An identifier for a [Kubernetes service account](https://cloud.google.com/kubernetes-engine/docs/how-to/kubernetes-service-accounts). For example, `my-project.svc.id.goog[my-namespace/my-kubernetes-sa]`. * `group:{emailid}`: An email address that represents a Google group. For example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain (primary) that represents all the users of that domain. For example, `google.com` or `example.com`. * `principal://iam.googleapis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: A single identity in a workforce identity pool. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/group/{group_id}`: All workforce identities in a group. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/attribute.{attribute_name}/{attribute_value}`: All workforce identities with a specific attribute value. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/*`: All identities in a workforce identity pool. * `principal://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/subject/{subject_attribute_value}`: A single identity in a workload identity pool. * `principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/group/{group_id}`: A workload identity pool group. * `principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/attribute.{attribute_name}/{attribute_value}`: All identities in a workload identity pool with a certain attribute. * `principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/*`: All identities in a workload identity pool. * `deleted:user:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a user that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the user is recovered, this value reverts to `user:{emailid}` and the recovered user retains the role in the binding. * `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a service account that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the service account is undeleted, this value reverts to `serviceAccount:{emailid}` and the undeleted service account retains the role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a Google group that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the group is recovered, this value reverts to `group:{emailid}` and the recovered group retains the role in the binding. * `deleted:principal://iam.googleapis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: Deleted single identity in a workforce identity pool. For example, `deleted:principal://iam.googleapis.com/locations/global/workforcePools/my-pool-id/subject/my-subject-attribute-value`.", "items": {"type": "string"}, "type": "array"}, "role": {"description": "Role that is assigned to the list of `members`, or principals. For example, `roles/viewer`, `roles/editor`, or `roles/owner`. For an overview of the IAM roles and permissions, see the [IAM documentation](https://cloud.google.com/iam/docs/roles-overview). For a list of the available pre-defined roles, see [here](https://cloud.google.com/iam/docs/understanding-roles).", "type": "string"}}, "type": "object"}, "BqmlIterationResult": {"id": "BqmlIterationResult", "properties": {"durationMs": {"description": "Deprecated.", "format": "int64", "type": "string"}, "evalLoss": {"description": "Deprecated.", "format": "double", "type": "number"}, "index": {"description": "Deprecated.", "format": "int32", "type": "integer"}, "learnRate": {"description": "Deprecated.", "format": "double", "type": "number"}, "trainingLoss": {"description": "Deprecated.", "format": "double", "type": "number"}}, "type": "object"}, "BqmlTrainingRun": {"id": "BqmlTrainingRun", "properties": {"iterationResults": {"description": "Deprecated.", "items": {"$ref": "BqmlIterationResult"}, "type": "array"}, "startTime": {"description": "Deprecated.", "format": "date-time", "type": "string"}, "state": {"description": "Deprecated.", "type": "string"}, "trainingOptions": {"description": "Deprecated.", "properties": {"earlyStop": {"type": "boolean"}, "l1Reg": {"format": "double", "type": "number"}, "l2Reg": {"format": "double", "type": "number"}, "learnRate": {"format": "double", "type": "number"}, "learnRateStrategy": {"type": "string"}, "lineSearchInitLearnRate": {"format": "double", "type": "number"}, "maxIteration": {"format": "int64", "type": "string"}, "minRelProgress": {"format": "double", "type": "number"}, "warmStart": {"type": "boolean"}}, "type": "object"}}, "type": "object"}, "CategoricalValue": {"description": "Representative value of a categorical feature.", "id": "CategoricalValue", "properties": {"categoryCounts": {"description": "Counts of all categories for the categorical feature. If there are more than ten categories, we return top ten (by count) and return one more CategoryCount with category \"_OTHER_\" and count as aggregate counts of remaining categories.", "items": {"$ref": "CategoryCount"}, "type": "array"}}, "type": "object"}, "CategoryCount": {"description": "Represents the count of a single category within the cluster.", "id": "CategoryCount", "properties": {"category": {"description": "The name of category.", "type": "string"}, "count": {"description": "The count of training samples matching the category within the cluster.", "format": "int64", "type": "string"}}, "type": "object"}, "CloneDefinition": {"description": "Information about base table and clone time of a table clone.", "id": "CloneDefinition", "properties": {"baseTableReference": {"$ref": "TableReference", "description": "Required. Reference describing the ID of the table that was cloned."}, "cloneTime": {"description": "Required. The time at which the base table was cloned. This value is reported in the JSON response using RFC3339 format.", "format": "date-time", "type": "string"}}, "type": "object"}, "Cluster": {"description": "Message containing the information about one cluster.", "id": "Cluster", "properties": {"centroidId": {"description": "Centroid id.", "format": "int64", "type": "string"}, "count": {"description": "Count of training data rows that were assigned to this cluster.", "format": "int64", "type": "string"}, "featureValues": {"description": "Values of highly variant features for this cluster.", "items": {"$ref": "FeatureValue"}, "type": "array"}}, "type": "object"}, "ClusterInfo": {"description": "Information about a single cluster for clustering model.", "id": "ClusterInfo", "properties": {"centroidId": {"description": "Centroid id.", "format": "int64", "type": "string"}, "clusterRadius": {"description": "Cluster radius, the average distance from centroid to each point assigned to the cluster.", "format": "double", "type": "number"}, "clusterSize": {"description": "Cluster size, the total number of points assigned to the cluster.", "format": "int64", "type": "string"}}, "type": "object"}, "Clustering": {"description": "Configures table clustering.", "id": "Clustering", "properties": {"fields": {"description": "One or more fields on which data should be clustered. Only top-level, non-repeated, simple-type fields are supported. The ordering of the clustering fields should be prioritized from most to least important for filtering purposes. For additional information, see [Introduction to clustered tables](https://cloud.google.com/bigquery/docs/clustered-tables#limitations).", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ClusteringMetrics": {"description": "Evaluation metrics for clustering models.", "id": "ClusteringMetrics", "properties": {"clusters": {"description": "Information for all clusters.", "items": {"$ref": "Cluster"}, "type": "array"}, "daviesBouldinIndex": {"description": "Davies-Bouldin index.", "format": "double", "type": "number"}, "meanSquaredDistance": {"description": "Mean of squared distances between each sample to its cluster centroid.", "format": "double", "type": "number"}}, "type": "object"}, "ConfusionMatrix": {"description": "Confusion matrix for multi-class classification models.", "id": "ConfusionMatrix", "properties": {"confidenceThreshold": {"description": "Confidence threshold used when computing the entries of the confusion matrix.", "format": "double", "type": "number"}, "rows": {"description": "One row per actual label.", "items": {"$ref": "Row"}, "type": "array"}}, "type": "object"}, "ConnectionProperty": {"description": "A connection-level property to customize query behavior. Under JDBC, these correspond directly to connection properties passed to the DriverManager. Under ODBC, these correspond to properties in the connection string. Currently supported connection properties: * **dataset_project_id**: represents the default project for datasets that are used in the query. Setting the system variable `@@dataset_project_id` achieves the same behavior. For more information about system variables, see: https://cloud.google.com/bigquery/docs/reference/system-variables * **time_zone**: represents the default timezone used to run the query. * **session_id**: associates the query with a given session. * **query_label**: associates the query with a given job label. If set, all subsequent queries in a script or session will have this label. For the format in which a you can specify a query label, see labels in the JobConfiguration resource type: https://cloud.google.com/bigquery/docs/reference/rest/v2/Job#jobconfiguration * **service_account**: indicates the service account to use to run a continuous query. If set, the query job uses the service account to access Google Cloud resources. Service account access is bounded by the IAM permissions that you have granted to the service account. Additional properties are allowed, but ignored. Specifying multiple connection properties with the same key returns an error.", "id": "ConnectionProperty", "properties": {"key": {"description": "The key of the property to set.", "type": "string"}, "value": {"description": "The value of the property to set.", "type": "string"}}, "type": "object"}, "CsvOptions": {"description": "Information related to a CSV data source.", "id": "CsvOptions", "properties": {"allowJaggedRows": {"description": "Optional. Indicates if BigQuery should accept rows that are missing trailing optional columns. If true, BigQuery treats missing trailing columns as null values. If false, records with missing trailing columns are treated as bad records, and if there are too many bad records, an invalid error is returned in the job result. The default value is false.", "type": "boolean"}, "allowQuotedNewlines": {"description": "Optional. Indicates if BigQuery should allow quoted data sections that contain newline characters in a CSV file. The default value is false.", "type": "boolean"}, "encoding": {"description": "Optional. The character encoding of the data. The supported values are UTF-8, ISO-8859-1, UTF-16BE, UTF-16LE, UTF-32BE, and UTF-32LE. The default value is UTF-8. BigQuery decodes the data after the raw, binary data has been split using the values of the quote and fieldDelimiter properties.", "type": "string"}, "fieldDelimiter": {"description": "Optional. The separator character for fields in a CSV file. The separator is interpreted as a single byte. For files encoded in ISO-8859-1, any single character can be used as a separator. For files encoded in UTF-8, characters represented in decimal range 1-127 (U+0001-U+007F) can be used without any modification. UTF-8 characters encoded with multiple bytes (i.e. U+0080 and above) will have only the first byte used for separating fields. The remaining bytes will be treated as a part of the field. BigQuery also supports the escape sequence \"\\t\" (U+0009) to specify a tab separator. The default value is comma (\",\", U+002C).", "type": "string"}, "nullMarker": {"description": "Optional. Specifies a string that represents a null value in a CSV file. For example, if you specify \"\\N\", BigQuery interprets \"\\N\" as a null value when querying a CSV file. The default value is the empty string. If you set this property to a custom value, BigQuery throws an error if an empty string is present for all data types except for STRING and BYTE. For STRING and BYTE columns, BigQuery interprets the empty string as an empty value.", "type": "string"}, "nullMarkers": {"description": "Optional. A list of strings represented as SQL NULL value in a CSV file. null_marker and null_markers can't be set at the same time. If null_marker is set, null_markers has to be not set. If null_markers is set, null_marker has to be not set. If both null_marker and null_markers are set at the same time, a user error would be thrown. Any strings listed in null_markers, including empty string would be interpreted as SQL NULL. This applies to all column types.", "items": {"type": "string"}, "type": "array"}, "preserveAsciiControlCharacters": {"description": "Optional. Indicates if the embedded ASCII control characters (the first 32 characters in the ASCII-table, from '\\x00' to '\\x1F') are preserved.", "type": "boolean"}, "quote": {"default": "\"", "description": "Optional. The value that is used to quote data sections in a CSV file. BigQuery converts the string to ISO-8859-1 encoding, and then uses the first byte of the encoded string to split the data in its raw, binary state. The default value is a double-quote (\"). If your data does not contain quoted sections, set the property value to an empty string. If your data contains quoted newline characters, you must also set the allowQuotedNewlines property to true. To include the specific quote character within a quoted value, precede it with an additional matching quote character. For example, if you want to escape the default character ' \" ', use ' \"\" '.", "pattern": ".?", "type": "string"}, "skipLeadingRows": {"description": "Optional. The number of rows at the top of a CSV file that BigQuery will skip when reading the data. The default value is 0. This property is useful if you have header rows in the file that should be skipped. When autodetect is on, the behavior is the following: * skipLeadingRows unspecified - Autodetect tries to detect headers in the first row. If they are not detected, the row is read as data. Otherwise data is read starting from the second row. * skipLeadingRows is 0 - Instructs autodetect that there are no headers and data should be read starting from the first row. * skipLeadingRows = N > 0 - Autodetect skips N-1 rows and tries to detect headers in row N. If headers are not detected, row N is just skipped. Otherwise row N is used to extract column names for the detected schema.", "format": "int64", "type": "string"}, "sourceColumnMatch": {"description": "Optional. Controls the strategy used to match loaded columns to the schema. If not set, a sensible default is chosen based on how the schema is provided. If autodetect is used, then columns are matched by name. Otherwise, columns are matched by position. This is done to keep the behavior backward-compatible. Acceptable values are: POSITION - matches by position. This assumes that the columns are ordered the same way as the schema. NAME - matches by name. This reads the header row as column names and reorders columns to match the field names in the schema.", "type": "string"}}, "type": "object"}, "DataFormatOptions": {"description": "Options for data format adjustments.", "id": "DataFormatOptions", "properties": {"useInt64Timestamp": {"description": "Optional. Output timestamp as usec int64. Default is false.", "type": "boolean"}}, "type": "object"}, "DataMaskingStatistics": {"description": "Statistics for data-masking.", "id": "DataMaskingStatistics", "properties": {"dataMaskingApplied": {"description": "Whether any accessed data was protected by the data masking.", "type": "boolean"}}, "type": "object"}, "DataPolicyOption": {"description": "Data policy option proto, it currently supports name only, will support precedence later.", "id": "DataPolicyOption", "properties": {"name": {"description": "Data policy resource name in the form of projects/project_id/locations/location_id/dataPolicies/data_policy_id.", "type": "string"}}, "type": "object"}, "DataSplitResult": {"description": "Data split result. This contains references to the training and evaluation data tables that were used to train the model.", "id": "DataSplitResult", "properties": {"evaluationTable": {"$ref": "TableReference", "description": "Table reference of the evaluation data after split."}, "testTable": {"$ref": "TableReference", "description": "Table reference of the test data after split."}, "trainingTable": {"$ref": "TableReference", "description": "Table reference of the training data after split."}}, "type": "object"}, "Dataset": {"description": "Represents a BigQuery dataset.", "id": "Dataset", "properties": {"access": {"description": "Optional. An array of objects that define dataset access for one or more entities. You can set this property when inserting or updating a dataset in order to control who is allowed to access the data. If unspecified at dataset creation time, BigQuery adds default dataset access for the following entities: access.specialGroup: projectReaders; access.role: READER; access.specialGroup: projectWriters; access.role: WRITER; access.specialGroup: projectOwners; access.role: OWNER; access.userByEmail: [dataset creator email]; access.role: OWNER; If you patch a dataset, then this field is overwritten by the patched dataset's access field. To add entities, you must supply the entire existing access array in addition to any new entities that you want to add.", "items": {"description": "An object that defines dataset access for an entity.", "properties": {"condition": {"$ref": "Expr", "description": "Optional. condition for the binding. If CEL expression in this field is true, this access binding will be considered"}, "dataset": {"$ref": "DatasetAccessEntry", "description": "[Pick one] A grant authorizing all resources of a particular type in a particular dataset access to this dataset. Only views are supported for now. The role field is not required when this field is set. If that dataset is deleted and re-created, its access needs to be granted again via an update operation."}, "domain": {"description": "[Pick one] A domain to grant access to. Any users signed in with the domain specified will be granted the specified access. Example: \"example.com\". Maps to IAM policy member \"domain:DOMAIN\".", "type": "string"}, "groupByEmail": {"description": "[Pick one] An email address of a Google Group to grant access to. Maps to IAM policy member \"group:GROUP\".", "type": "string"}, "iamMember": {"description": "[Pick one] Some other type of member that appears in the IAM Policy but isn't a user, group, domain, or special group.", "type": "string"}, "role": {"description": "An IAM role ID that should be granted to the user, group, or domain specified in this access entry. The following legacy mappings will be applied: * `OWNER`: `roles/bigquery.dataOwner` * `WRITER`: `roles/bigquery.dataEditor` * `READER`: `roles/bigquery.dataViewer` This field will accept any of the above formats, but will return only the legacy format. For example, if you set this field to \"roles/bigquery.dataOwner\", it will be returned back as \"OWNER\".", "type": "string"}, "routine": {"$ref": "RoutineReference", "description": "[Pick one] A routine from a different dataset to grant access to. Queries executed against that routine will have read access to views/tables/routines in this dataset. Only UDF is supported for now. The role field is not required when this field is set. If that routine is updated by any user, access to the routine needs to be granted again via an update operation."}, "specialGroup": {"description": "[Pick one] A special group to grant access to. Possible values include: * projectOwners: Owners of the enclosing project. * projectReaders: Readers of the enclosing project. * projectWriters: Writers of the enclosing project. * allAuthenticatedUsers: All authenticated BigQuery users. Maps to similarly-named IAM members.", "type": "string"}, "userByEmail": {"description": "[Pick one] An email address of a user to grant access to. For example: <EMAIL>. Maps to IAM policy member \"user:EMAIL\" or \"serviceAccount:EMAIL\".", "type": "string"}, "view": {"$ref": "TableReference", "description": "[Pick one] A view from a different dataset to grant access to. Queries executed against that view will have read access to views/tables/routines in this dataset. The role field is not required when this field is set. If that view is updated by any user, access to the view needs to be granted again via an update operation."}}, "type": "object"}, "type": "array"}, "creationTime": {"description": "Output only. The time when this dataset was created, in milliseconds since the epoch.", "format": "int64", "readOnly": true, "type": "string"}, "datasetReference": {"$ref": "DatasetReference", "description": "Required. A reference that identifies the dataset."}, "defaultCollation": {"description": "Optional. Defines the default collation specification of future tables created in the dataset. If a table is created in this dataset without table-level default collation, then the table inherits the dataset default collation, which is applied to the string fields that do not have explicit collation specified. A change to this field affects only tables created afterwards, and does not alter the existing tables. The following values are supported: * 'und:ci': undetermined locale, case insensitive. * '': empty string. Default to case-sensitive behavior.", "type": "string"}, "defaultEncryptionConfiguration": {"$ref": "EncryptionConfiguration", "description": "The default encryption key for all tables in the dataset. After this property is set, the encryption key of all newly-created tables in the dataset is set to this value unless the table creation request or query explicitly overrides the key."}, "defaultPartitionExpirationMs": {"description": "This default partition expiration, expressed in milliseconds. When new time-partitioned tables are created in a dataset where this property is set, the table will inherit this value, propagated as the `TimePartitioning.expirationMs` property on the new table. If you set `TimePartitioning.expirationMs` explicitly when creating a table, the `defaultPartitionExpirationMs` of the containing dataset is ignored. When creating a partitioned table, if `defaultPartitionExpirationMs` is set, the `defaultTableExpirationMs` value is ignored and the table will not be inherit a table expiration deadline.", "format": "int64", "type": "string"}, "defaultRoundingMode": {"description": "Optional. Defines the default rounding mode specification of new tables created within this dataset. During table creation, if this field is specified, the table within this dataset will inherit the default rounding mode of the dataset. Setting the default rounding mode on a table overrides this option. Existing tables in the dataset are unaffected. If columns are defined during that table creation, they will immediately inherit the table's default rounding mode, unless otherwise specified.", "enum": ["ROUNDING_MODE_UNSPECIFIED", "ROUND_HALF_AWAY_FROM_ZERO", "ROUND_HALF_EVEN"], "enumDescriptions": ["Unspecified will default to using ROUND_HALF_AWAY_FROM_ZERO.", "ROUND_HALF_AWAY_FROM_ZERO rounds half values away from zero when applying precision and scale upon writing of NUMERIC and BIGNUMERIC values. For Scale: 0 1.1, 1.2, 1.3, 1.4 => 1 1.5, 1.6, 1.7, 1.8, 1.9 => 2", "ROUND_HALF_EVEN rounds half values to the nearest even value when applying precision and scale upon writing of NUMERIC and BIGNUMERIC values. For Scale: 0 1.1, 1.2, 1.3, 1.4 => 1 1.5 => 2 1.6, 1.7, 1.8, 1.9 => 2 2.5 => 2"], "type": "string"}, "defaultTableExpirationMs": {"description": "Optional. The default lifetime of all tables in the dataset, in milliseconds. The minimum lifetime value is 3600000 milliseconds (one hour). To clear an existing default expiration with a PATCH request, set to 0. Once this property is set, all newly-created tables in the dataset will have an expirationTime property set to the creation time plus the value in this property, and changing the value will only affect new tables, not existing ones. When the expirationTime for a given table is reached, that table will be deleted automatically. If a table's expirationTime is modified or removed before the table expires, or if you provide an explicit expirationTime when creating a table, that value takes precedence over the default expiration time indicated by this property.", "format": "int64", "type": "string"}, "description": {"description": "Optional. A user-friendly description of the dataset.", "type": "string"}, "etag": {"description": "Output only. A hash of the resource.", "readOnly": true, "type": "string"}, "externalCatalogDatasetOptions": {"$ref": "ExternalCatalogDatasetOptions", "description": "Optional. Options defining open source compatible datasets living in the BigQuery catalog. Contains metadata of open source database, schema or namespace represented by the current dataset."}, "externalDatasetReference": {"$ref": "ExternalDatasetReference", "description": "Optional. Reference to a read-only external dataset defined in data catalogs outside of BigQuery. Filled out when the dataset type is EXTERNAL."}, "friendlyName": {"description": "Optional. A descriptive name for the dataset.", "type": "string"}, "id": {"description": "Output only. The fully-qualified unique name of the dataset in the format projectId:datasetId. The dataset name without the project name is given in the datasetId field. When creating a new dataset, leave this field blank, and instead specify the datasetId field.", "readOnly": true, "type": "string"}, "isCaseInsensitive": {"description": "Optional. TRUE if the dataset and its table names are case-insensitive, otherwise FALSE. By default, this is FALSE, which means the dataset and its table names are case-sensitive. This field does not affect routine references.", "type": "boolean"}, "kind": {"default": "bigquery#dataset", "description": "Output only. The resource type.", "readOnly": true, "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "The labels associated with this dataset. You can use these to organize and group your datasets. You can set this property when inserting or updating a dataset. See [Creating and Updating Dataset Labels](https://cloud.google.com/bigquery/docs/creating-managing-labels#creating_and_updating_dataset_labels) for more information.", "type": "object"}, "lastModifiedTime": {"description": "Output only. The date when this dataset was last modified, in milliseconds since the epoch.", "format": "int64", "readOnly": true, "type": "string"}, "linkedDatasetMetadata": {"$ref": "LinkedDatasetMetadata", "description": "Output only. Metadata about the LinkedDataset. Filled out when the dataset type is LINKED.", "readOnly": true}, "linkedDatasetSource": {"$ref": "LinkedDatasetSource", "description": "Optional. The source dataset reference when the dataset is of type LINKED. For all other dataset types it is not set. This field cannot be updated once it is set. Any attempt to update this field using Update and Patch API Operations will be ignored."}, "location": {"description": "The geographic location where the dataset should reside. See https://cloud.google.com/bigquery/docs/locations for supported locations.", "type": "string"}, "maxTimeTravelHours": {"description": "Optional. Defines the time travel window in hours. The value can be from 48 to 168 hours (2 to 7 days). The default value is 168 hours if this is not set.", "format": "int64", "type": "string"}, "resourceTags": {"additionalProperties": {"type": "string"}, "description": "Optional. The [tags](https://cloud.google.com/bigquery/docs/tags) attached to this dataset. Tag keys are globally unique. Tag key is expected to be in the namespaced format, for example \"123456789012/environment\" where 123456789012 is the ID of the parent organization or project resource for this tag key. Tag value is expected to be the short name, for example \"Production\". See [Tag definitions](https://cloud.google.com/iam/docs/tags-access-control#definitions) for more details.", "type": "object"}, "restrictions": {"$ref": "RestrictionConfig", "description": "Optional. Output only. Restriction config for all tables and dataset. If set, restrict certain accesses on the dataset and all its tables based on the config. See [Data egress](https://cloud.google.com/bigquery/docs/analytics-hub-introduction#data_egress) for more details.", "readOnly": true}, "satisfiesPzi": {"description": "Output only. Reserved for future use.", "readOnly": true, "type": "boolean"}, "satisfiesPzs": {"description": "Output only. Reserved for future use.", "readOnly": true, "type": "boolean"}, "selfLink": {"description": "Output only. A URL that can be used to access the resource again. You can use this URL in Get or Update requests to the resource.", "readOnly": true, "type": "string"}, "storageBillingModel": {"description": "Optional. Updates storage_billing_model for the dataset.", "enum": ["STORAGE_BILLING_MODEL_UNSPECIFIED", "LOGICAL", "PHYSICAL"], "enumDescriptions": ["Value not set.", "Billing for logical bytes.", "Billing for physical bytes."], "type": "string"}, "tags": {"deprecated": true, "description": "Output only. Tags for the dataset. To provide tags as inputs, use the `resourceTags` field.", "items": {"description": "A global tag managed by Resource Manager. https://cloud.google.com/iam/docs/tags-access-control#definitions", "properties": {"tagKey": {"description": "Required. The namespaced friendly name of the tag key, e.g. \"12345/environment\" where 12345 is org id.", "type": "string"}, "tagValue": {"description": "Required. The friendly short name of the tag value, e.g. \"production\".", "type": "string"}}, "type": "object"}, "readOnly": true, "type": "array"}, "type": {"description": "Output only. Same as `type` in `ListFormatDataset`. The type of the dataset, one of: * DEFAULT - only accessible by owner and authorized accounts, * PUBLIC - accessible by everyone, * LINKED - linked dataset, * EXTERNAL - dataset with definition in external metadata catalog.", "readOnly": true, "type": "string"}}, "type": "object"}, "DatasetAccessEntry": {"description": "Grants all resources of particular types in a particular dataset read access to the current dataset. Similar to how individually authorized views work, updates to any resource granted through its dataset (including creation of new resources) requires read permission to referenced resources, plus write permission to the authorizing dataset.", "id": "DatasetAccessEntry", "properties": {"dataset": {"$ref": "DatasetReference", "description": "The dataset this entry applies to"}, "targetTypes": {"description": "Which resources in the dataset this entry applies to. Currently, only views are supported, but additional target types may be added in the future.", "items": {"enum": ["TARGET_TYPE_UNSPECIFIED", "VIEWS", "ROUTINES"], "enumDescriptions": ["Do not use. You must set a target type explicitly.", "This entry applies to views in the dataset.", "This entry applies to routines in the dataset."], "type": "string"}, "type": "array"}}, "type": "object"}, "DatasetList": {"description": "Response format for a page of results when listing datasets.", "id": "DatasetList", "properties": {"datasets": {"description": "An array of the dataset resources in the project. Each resource contains basic information. For full information about a particular dataset resource, use the Datasets: get method. This property is omitted when there are no datasets in the project.", "items": {"description": "A dataset resource with only a subset of fields, to be returned in a list of datasets.", "properties": {"datasetReference": {"$ref": "DatasetReference", "description": "The dataset reference. Use this property to access specific parts of the dataset's ID, such as project ID or dataset ID."}, "externalDatasetReference": {"$ref": "ExternalDatasetReference", "description": "Output only. Reference to a read-only external dataset defined in data catalogs outside of BigQuery. Filled out when the dataset type is EXTERNAL.", "readOnly": true}, "friendlyName": {"description": "An alternate name for the dataset. The friendly name is purely decorative in nature.", "type": "string"}, "id": {"description": "The fully-qualified, unique, opaque ID of the dataset.", "type": "string"}, "kind": {"description": "The resource type. This property always returns the value \"bigquery#dataset\"", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "The labels associated with this dataset. You can use these to organize and group your datasets.", "type": "object"}, "location": {"description": "The geographic location where the dataset resides.", "type": "string"}}, "type": "object"}, "type": "array"}, "etag": {"description": "Output only. A hash value of the results page. You can use this property to determine if the page has changed since the last request.", "readOnly": true, "type": "string"}, "kind": {"default": "bigquery#datasetList", "description": "Output only. The resource type. This property always returns the value \"bigquery#datasetList\"", "readOnly": true, "type": "string"}, "nextPageToken": {"description": "A token that can be used to request the next results page. This property is omitted on the final results page.", "type": "string"}, "unreachable": {"description": "A list of skipped locations that were unreachable. For more information about BigQuery locations, see: https://cloud.google.com/bigquery/docs/locations. Example: \"europe-west5\"", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "DatasetReference": {"description": "Identifier for a dataset.", "id": "DatasetReference", "properties": {"datasetId": {"description": "Required. A unique ID for this dataset, without the project name. The ID must contain only letters (a-z, A-Z), numbers (0-9), or underscores (_). The maximum length is 1,024 characters.", "type": "string"}, "projectId": {"description": "Optional. The ID of the project containing this dataset.", "type": "string"}}, "type": "object"}, "DestinationTableProperties": {"description": "Properties for the destination table.", "id": "DestinationTableProperties", "properties": {"description": {"description": "Optional. The description for the destination table. This will only be used if the destination table is newly created. If the table already exists and a value different than the current description is provided, the job will fail.", "type": "string"}, "expirationTime": {"description": "Internal use only.", "format": "date-time", "type": "string"}, "friendlyName": {"description": "Optional. Friendly name for the destination table. If the table already exists, it should be same as the existing friendly name.", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. The labels associated with this table. You can use these to organize and group your tables. This will only be used if the destination table is newly created. If the table already exists and labels are different than the current labels are provided, the job will fail.", "type": "object"}}, "type": "object"}, "DifferentialPrivacyPolicy": {"description": "Represents privacy policy associated with \"differential privacy\" method.", "id": "DifferentialPrivacyPolicy", "properties": {"deltaBudget": {"description": "Optional. The total delta budget for all queries against the privacy-protected view. Each subscriber query against this view charges the amount of delta that is pre-defined by the contributor through the privacy policy delta_per_query field. If there is sufficient budget, then the subscriber query attempts to complete. It might still fail due to other reasons, in which case the charge is refunded. If there is insufficient budget the query is rejected. There might be multiple charge attempts if a single query references multiple views. In this case there must be sufficient budget for all charges or the query is rejected and charges are refunded in best effort. The budget does not have a refresh policy and can only be updated via ALTER VIEW or circumvented by creating a new view that can be queried with a fresh budget.", "format": "double", "type": "number"}, "deltaBudgetRemaining": {"description": "Output only. The delta budget remaining. If budget is exhausted, no more queries are allowed. Note that the budget for queries that are in progress is deducted before the query executes. If the query fails or is cancelled then the budget is refunded. In this case the amount of budget remaining can increase.", "format": "double", "readOnly": true, "type": "number"}, "deltaPerQuery": {"description": "Optional. The delta value that is used per query. Delta represents the probability that any row will fail to be epsilon differentially private. Indicates the risk associated with exposing aggregate rows in the result of a query.", "format": "double", "type": "number"}, "epsilonBudget": {"description": "Optional. The total epsilon budget for all queries against the privacy-protected view. Each subscriber query against this view charges the amount of epsilon they request in their query. If there is sufficient budget, then the subscriber query attempts to complete. It might still fail due to other reasons, in which case the charge is refunded. If there is insufficient budget the query is rejected. There might be multiple charge attempts if a single query references multiple views. In this case there must be sufficient budget for all charges or the query is rejected and charges are refunded in best effort. The budget does not have a refresh policy and can only be updated via ALTER VIEW or circumvented by creating a new view that can be queried with a fresh budget.", "format": "double", "type": "number"}, "epsilonBudgetRemaining": {"description": "Output only. The epsilon budget remaining. If budget is exhausted, no more queries are allowed. Note that the budget for queries that are in progress is deducted before the query executes. If the query fails or is cancelled then the budget is refunded. In this case the amount of budget remaining can increase.", "format": "double", "readOnly": true, "type": "number"}, "maxEpsilonPerQuery": {"description": "Optional. The maximum epsilon value that a query can consume. If the subscriber specifies epsilon as a parameter in a SELECT query, it must be less than or equal to this value. The epsilon parameter controls the amount of noise that is added to the groups — a higher epsilon means less noise.", "format": "double", "type": "number"}, "maxGroupsContributed": {"description": "Optional. The maximum groups contributed value that is used per query. Represents the maximum number of groups to which each protected entity can contribute. Changing this value does not improve or worsen privacy. The best value for accuracy and utility depends on the query and data.", "format": "int64", "type": "string"}, "privacyUnitColumn": {"description": "Optional. The privacy unit column associated with this policy. Differential privacy policies can only have one privacy unit column per data source object (table, view).", "type": "string"}}, "type": "object"}, "DimensionalityReductionMetrics": {"description": "Model evaluation metrics for dimensionality reduction models.", "id": "DimensionalityReductionMetrics", "properties": {"totalExplainedVarianceRatio": {"description": "Total percentage of variance explained by the selected principal components.", "format": "double", "type": "number"}}, "type": "object"}, "DmlStatistics": {"description": "Detailed statistics for DML statements", "id": "DmlStatistics", "properties": {"deletedRowCount": {"description": "Output only. Number of deleted Rows. populated by DML DELETE, MERGE and TRUNCATE statements.", "format": "int64", "readOnly": true, "type": "string"}, "insertedRowCount": {"description": "Output only. Number of inserted Rows. Populated by DML INSERT and MERGE statements", "format": "int64", "readOnly": true, "type": "string"}, "updatedRowCount": {"description": "Output only. Number of updated Rows. Populated by DML UPDATE and MERGE statements.", "format": "int64", "readOnly": true, "type": "string"}}, "type": "object"}, "DoubleCandidates": {"description": "Discrete candidates of a double hyperparameter.", "id": "DoubleCandidates", "properties": {"candidates": {"description": "Candidates for the double parameter in increasing order.", "items": {"format": "double", "type": "number"}, "type": "array"}}, "type": "object"}, "DoubleHparamSearchSpace": {"description": "Search space for a double hyperparameter.", "id": "DoubleHparamSearchSpace", "properties": {"candidates": {"$ref": "DoubleCandidates", "description": "Candidates of the double hyperparameter."}, "range": {"$ref": "DoubleRange", "description": "Range of the double hyperparameter."}}, "type": "object"}, "DoubleRange": {"description": "Range of a double hyperparameter.", "id": "DoubleRange", "properties": {"max": {"description": "Max value of the double parameter.", "format": "double", "type": "number"}, "min": {"description": "Min value of the double parameter.", "format": "double", "type": "number"}}, "type": "object"}, "EncryptionConfiguration": {"description": "Configuration for Cloud KMS encryption settings.", "id": "EncryptionConfiguration", "properties": {"kmsKeyName": {"description": "Optional. Describes the Cloud KMS encryption key that will be used to protect destination BigQuery table. The BigQuery Service Account associated with your project requires access to this encryption key.", "type": "string"}}, "type": "object"}, "Entry": {"description": "A single entry in the confusion matrix.", "id": "Entry", "properties": {"itemCount": {"description": "Number of items being predicted as this label.", "format": "int64", "type": "string"}, "predictedLabel": {"description": "The predicted label. For confidence_threshold > 0, we will also add an entry indicating the number of items under the confidence threshold.", "type": "string"}}, "type": "object"}, "ErrorProto": {"description": "Error details.", "id": "<PERSON><PERSON>r<PERSON><PERSON><PERSON>", "properties": {"debugInfo": {"description": "Debugging information. This property is internal to Google and should not be used.", "type": "string"}, "location": {"description": "Specifies where the error occurred, if present.", "type": "string"}, "message": {"description": "A human-readable description of the error.", "type": "string"}, "reason": {"description": "A short error code that summarizes the error.", "type": "string"}}, "type": "object"}, "EvaluationMetrics": {"description": "Evaluation metrics of a model. These are either computed on all training data or just the eval data based on whether eval data was used during training. These are not present for imported models.", "id": "EvaluationMetrics", "properties": {"arimaForecastingMetrics": {"$ref": "ArimaForecastingMetrics", "description": "Populated for ARIMA models."}, "binaryClassificationMetrics": {"$ref": "BinaryClassificationMetrics", "description": "Populated for binary classification/classifier models."}, "clusteringMetrics": {"$ref": "ClusteringMetrics", "description": "Populated for clustering models."}, "dimensionalityReductionMetrics": {"$ref": "DimensionalityReductionMetrics", "description": "Evaluation metrics when the model is a dimensionality reduction model, which currently includes PCA."}, "multiClassClassificationMetrics": {"$ref": "MultiClassClassificationMetrics", "description": "Populated for multi-class classification/classifier models."}, "rankingMetrics": {"$ref": "RankingMetrics", "description": "Populated for implicit feedback type matrix factorization models."}, "regressionMetrics": {"$ref": "RegressionMetrics", "description": "Populated for regression models and explicit feedback type matrix factorization models."}}, "type": "object"}, "ExplainQueryStage": {"description": "A single stage of query execution.", "id": "ExplainQueryStage", "properties": {"completedParallelInputs": {"description": "Number of parallel input segments completed.", "format": "int64", "type": "string"}, "computeMode": {"description": "Output only. Compute mode for this stage.", "enum": ["COMPUTE_MODE_UNSPECIFIED", "BIGQUERY", "BI_ENGINE"], "enumDescriptions": ["ComputeMode type not specified.", "This stage was processed using BigQuery slots.", "This stage was processed using BI Engine compute."], "readOnly": true, "type": "string"}, "computeMsAvg": {"description": "Milliseconds the average shard spent on CPU-bound tasks.", "format": "int64", "type": "string"}, "computeMsMax": {"description": "Milliseconds the slowest shard spent on CPU-bound tasks.", "format": "int64", "type": "string"}, "computeRatioAvg": {"description": "Relative amount of time the average shard spent on CPU-bound tasks.", "format": "double", "type": "number"}, "computeRatioMax": {"description": "Relative amount of time the slowest shard spent on CPU-bound tasks.", "format": "double", "type": "number"}, "endMs": {"description": "Stage end time represented as milliseconds since the epoch.", "format": "int64", "type": "string"}, "id": {"description": "Unique ID for the stage within the plan.", "format": "int64", "type": "string"}, "inputStages": {"description": "IDs for stages that are inputs to this stage.", "items": {"format": "int64", "type": "string"}, "type": "array"}, "name": {"description": "Human-readable name for the stage.", "type": "string"}, "parallelInputs": {"description": "Number of parallel input segments to be processed", "format": "int64", "type": "string"}, "readMsAvg": {"description": "Milliseconds the average shard spent reading input.", "format": "int64", "type": "string"}, "readMsMax": {"description": "Milliseconds the slowest shard spent reading input.", "format": "int64", "type": "string"}, "readRatioAvg": {"description": "Relative amount of time the average shard spent reading input.", "format": "double", "type": "number"}, "readRatioMax": {"description": "Relative amount of time the slowest shard spent reading input.", "format": "double", "type": "number"}, "recordsRead": {"description": "Number of records read into the stage.", "format": "int64", "type": "string"}, "recordsWritten": {"description": "Number of records written by the stage.", "format": "int64", "type": "string"}, "shuffleOutputBytes": {"description": "Total number of bytes written to shuffle.", "format": "int64", "type": "string"}, "shuffleOutputBytesSpilled": {"description": "Total number of bytes written to shuffle and spilled to disk.", "format": "int64", "type": "string"}, "slotMs": {"description": "Slot-milliseconds used by the stage.", "format": "int64", "type": "string"}, "startMs": {"description": "Stage start time represented as milliseconds since the epoch.", "format": "int64", "type": "string"}, "status": {"description": "Current status for this stage.", "type": "string"}, "steps": {"description": "List of operations within the stage in dependency order (approximately chronological).", "items": {"$ref": "ExplainQueryStep"}, "type": "array"}, "waitMsAvg": {"description": "Milliseconds the average shard spent waiting to be scheduled.", "format": "int64", "type": "string"}, "waitMsMax": {"description": "Milliseconds the slowest shard spent waiting to be scheduled.", "format": "int64", "type": "string"}, "waitRatioAvg": {"description": "Relative amount of time the average shard spent waiting to be scheduled.", "format": "double", "type": "number"}, "waitRatioMax": {"description": "Relative amount of time the slowest shard spent waiting to be scheduled.", "format": "double", "type": "number"}, "writeMsAvg": {"description": "Milliseconds the average shard spent on writing output.", "format": "int64", "type": "string"}, "writeMsMax": {"description": "Milliseconds the slowest shard spent on writing output.", "format": "int64", "type": "string"}, "writeRatioAvg": {"description": "Relative amount of time the average shard spent on writing output.", "format": "double", "type": "number"}, "writeRatioMax": {"description": "Relative amount of time the slowest shard spent on writing output.", "format": "double", "type": "number"}}, "type": "object"}, "ExplainQueryStep": {"description": "An operation within a stage.", "id": "ExplainQueryStep", "properties": {"kind": {"description": "Machine-readable operation type.", "type": "string"}, "substeps": {"description": "Human-readable description of the step(s).", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "Explanation": {"description": "Explanation for a single feature.", "id": "Explanation", "properties": {"attribution": {"description": "Attribution of feature.", "format": "double", "type": "number"}, "featureName": {"description": "The full feature name. For non-numerical features, will be formatted like `.`. Overall size of feature name will always be truncated to first 120 characters.", "type": "string"}}, "type": "object"}, "ExportDataStatistics": {"description": "Statistics for the EXPORT DATA statement as part of Query Job. EXTRACT JOB statistics are populated in JobStatistics4.", "id": "ExportDataStatistics", "properties": {"fileCount": {"description": "Number of destination files generated in case of EXPORT DATA statement only.", "format": "int64", "type": "string"}, "rowCount": {"description": "[Alpha] Number of destination rows generated in case of EXPORT DATA statement only.", "format": "int64", "type": "string"}}, "type": "object"}, "Expr": {"description": "Represents a textual expression in the Common Expression Language (CEL) syntax. CEL is a C-like expression language. The syntax and semantics of CEL are documented at https://github.com/google/cel-spec. Example (Comparison): title: \"Summary size limit\" description: \"Determines if a summary is less than 100 chars\" expression: \"document.summary.size() < 100\" Example (Equality): title: \"Requestor is owner\" description: \"Determines if requestor is the document owner\" expression: \"document.owner == request.auth.claims.email\" Example (Logic): title: \"Public documents\" description: \"Determine whether the document should be publicly visible\" expression: \"document.type != 'private' && document.type != 'internal'\" Example (Data Manipulation): title: \"Notification string\" description: \"Create a notification string with a timestamp.\" expression: \"'New message received at ' + string(document.create_time)\" The exact variables and functions that may be referenced within an expression are determined by the service that evaluates it. See the service documentation for additional information.", "id": "Expr", "properties": {"description": {"description": "Optional. Description of the expression. This is a longer text which describes the expression, e.g. when hovered over it in a UI.", "type": "string"}, "expression": {"description": "Textual representation of an expression in Common Expression Language syntax.", "type": "string"}, "location": {"description": "Optional. String indicating the location of the expression for error reporting, e.g. a file name and a position in the file.", "type": "string"}, "title": {"description": "Optional. Title for the expression, i.e. a short string describing its purpose. This can be used e.g. in UIs which allow to enter the expression.", "type": "string"}}, "type": "object"}, "ExternalCatalogDatasetOptions": {"description": "Options defining open source compatible datasets living in the BigQuery catalog. Contains metadata of open source database, schema, or namespace represented by the current dataset.", "id": "ExternalCatalogDatasetOptions", "properties": {"defaultStorageLocationUri": {"description": "Optional. The storage location URI for all tables in the dataset. Equivalent to hive metastore's database locationUri. Maximum length of 1024 characters.", "type": "string"}, "parameters": {"additionalProperties": {"type": "string"}, "description": "Optional. A map of key value pairs defining the parameters and properties of the open source schema. Maximum size of 2MiB.", "type": "object"}}, "type": "object"}, "ExternalCatalogTableOptions": {"description": "Metadata about open source compatible table. The fields contained in these options correspond to Hive metastore's table-level properties.", "id": "ExternalCatalogTableOptions", "properties": {"connectionId": {"description": "Optional. A connection ID that specifies the credentials to be used to read external storage, such as Azure Blob, Cloud Storage, or Amazon S3. This connection is needed to read the open source table from BigQuery. The connection_id format must be either `..` or `projects//locations//connections/`.", "type": "string"}, "parameters": {"additionalProperties": {"type": "string"}, "description": "Optional. A map of the key-value pairs defining the parameters and properties of the open source table. Corresponds with Hive metastore table parameters. Maximum size of 4MiB.", "type": "object"}, "storageDescriptor": {"$ref": "StorageDescriptor", "description": "Optional. A storage descriptor containing information about the physical storage of this table."}}, "type": "object"}, "ExternalDataConfiguration": {"id": "ExternalDataConfiguration", "properties": {"autodetect": {"description": "Try to detect schema and format options automatically. Any option specified explicitly will be honored.", "type": "boolean"}, "avroOptions": {"$ref": "AvroOptions", "description": "Optional. Additional properties to set if sourceFormat is set to AVRO."}, "bigtableOptions": {"$ref": "BigtableOptions", "description": "Optional. Additional options if sourceFormat is set to BIGTABLE."}, "compression": {"description": "Optional. The compression type of the data source. Possible values include GZIP and NONE. The default value is NONE. This setting is ignored for Google Cloud Bigtable, Google Cloud Datastore backups, Avro, ORC and Parquet formats. An empty string is an invalid value.", "type": "string"}, "connectionId": {"description": "Optional. The connection specifying the credentials to be used to read external storage, such as Azure Blob, Cloud Storage, or S3. The connection_id can have the form `{project_id}.{location_id};{connection_id}` or `projects/{project_id}/locations/{location_id}/connections/{connection_id}`.", "type": "string"}, "csvOptions": {"$ref": "CsvOptions", "description": "Optional. Additional properties to set if sourceFormat is set to CSV."}, "dateFormat": {"description": "Optional. Format used to parse DATE values. Supports C-style and SQL-style values.", "type": "string"}, "datetimeFormat": {"description": "Optional. Format used to parse DATETIME values. Supports C-style and SQL-style values.", "type": "string"}, "decimalTargetTypes": {"description": "Defines the list of possible SQL data types to which the source decimal values are converted. This list and the precision and the scale parameters of the decimal field determine the target type. In the order of NUMERIC, BIGNUMERIC, and STRING, a type is picked if it is in the specified list and if it supports the precision and the scale. STRING supports all precision and scale values. If none of the listed types supports the precision and the scale, the type supporting the widest range in the specified list is picked, and if a value exceeds the supported range when reading the data, an error will be thrown. Example: Suppose the value of this field is [\"NUMER<PERSON>\", \"BIGNUME<PERSON><PERSON>\"]. If (precision,scale) is: * (38,9) -> NUMERIC; * (39,9) -> BIGNUMERIC (NUMERIC cannot hold 30 integer digits); * (38,10) -> BIGNUMERIC (NUMERIC cannot hold 10 fractional digits); * (76,38) -> BIGNUMERIC; * (77,38) -> B<PERSON><PERSON><PERSON>RI<PERSON> (error if value exceeds supported range). This field cannot contain duplicate types. The order of the types in this field is ignored. For example, [\"<PERSON>IG<PERSON><PERSON>RI<PERSON>\", \"NUMER<PERSON>\"] is the same as [\"<PERSON><PERSON>ER<PERSON>\", \"<PERSON>IGNUME<PERSON><PERSON>\"] and NUMERIC always takes precedence over <PERSON><PERSON>NUMERI<PERSON>. Defaults to [\"<PERSON><PERSON>ER<PERSON>\", \"STRING\"] for ORC and [\"NUMERIC\"] for the other file formats.", "items": {"enum": ["DECIMAL_TARGET_TYPE_UNSPECIFIED", "NUMERIC", "BIGNUMERIC", "STRING"], "enumDescriptions": ["Invalid type.", "Decimal values could be converted to NUMERIC type.", "Decimal values could be converted to BIGNUMERIC type.", "Decimal values could be converted to STRING type."], "type": "string"}, "type": "array"}, "fileSetSpecType": {"description": "Optional. Specifies how source URIs are interpreted for constructing the file set to load. By default source URIs are expanded against the underlying storage. Other options include specifying manifest files. Only applicable to object storage systems.", "enum": ["FILE_SET_SPEC_TYPE_FILE_SYSTEM_MATCH", "FILE_SET_SPEC_TYPE_NEW_LINE_DELIMITED_MANIFEST"], "enumDescriptions": ["This option expands source URIs by listing files from the object store. It is the default behavior if FileSetSpecType is not set.", "This option indicates that the provided URIs are newline-delimited manifest files, with one URI per line. Wildcard URIs are not supported."], "type": "string"}, "googleSheetsOptions": {"$ref": "GoogleSheetsOptions", "description": "Optional. Additional options if sourceFormat is set to GOOGLE_SHEETS."}, "hivePartitioningOptions": {"$ref": "HivePartitioningOptions", "description": "Optional. When set, configures hive partitioning support. Not all storage formats support hive partitioning -- requesting hive partitioning on an unsupported format will lead to an error, as will providing an invalid specification."}, "ignoreUnknownValues": {"description": "Optional. Indicates if BigQuery should allow extra values that are not represented in the table schema. If true, the extra values are ignored. If false, records with extra columns are treated as bad records, and if there are too many bad records, an invalid error is returned in the job result. The default value is false. The sourceFormat property determines what BigQuery treats as an extra value: CSV: Trailing columns JSON: Named values that don't match any column names Google Cloud Bigtable: This setting is ignored. Google Cloud Datastore backups: This setting is ignored. Avro: This setting is ignored. ORC: This setting is ignored. <PERSON><PERSON><PERSON>: This setting is ignored.", "type": "boolean"}, "jsonExtension": {"description": "Optional. Load option to be used together with source_format newline-delimited JSON to indicate that a variant of JSON is being loaded. To load newline-delimited GeoJSON, specify GEOJSON (and source_format must be set to NEWLINE_DELIMITED_JSON).", "enum": ["JSON_EXTENSION_UNSPECIFIED", "GEOJSON"], "enumDescriptions": ["The default if provided value is not one included in the enum, or the value is not specified. The source format is parsed without any modification.", "Use GeoJSON variant of JSON. See https://tools.ietf.org/html/rfc7946."], "type": "string"}, "jsonOptions": {"$ref": "JsonOptions", "description": "Optional. Additional properties to set if sourceFormat is set to JSON."}, "maxBadRecords": {"description": "Optional. The maximum number of bad records that BigQuery can ignore when reading data. If the number of bad records exceeds this value, an invalid error is returned in the job result. The default value is 0, which requires that all records are valid. This setting is ignored for Google Cloud Bigtable, Google Cloud Datastore backups, Avro, ORC and Parquet formats.", "format": "int32", "type": "integer"}, "metadataCacheMode": {"description": "Optional. Metadata Cache Mode for the table. Set this to enable caching of metadata from external data source.", "enum": ["METADATA_CACHE_MODE_UNSPECIFIED", "AUTOMATIC", "MANUAL"], "enumDescriptions": ["Unspecified metadata cache mode.", "Set this mode to trigger automatic background refresh of metadata cache from the external source. Queries will use the latest available cache version within the table's maxStaleness interval.", "Set this mode to enable triggering manual refresh of the metadata cache from external source. Queries will use the latest manually triggered cache version within the table's maxStaleness interval."], "type": "string"}, "objectMetadata": {"description": "Optional. ObjectMetadata is used to create Object Tables. Object Tables contain a listing of objects (with their metadata) found at the source_uris. If ObjectMetadata is set, source_format should be omitted. Currently SIMPLE is the only supported Object Metadata type.", "enum": ["OBJECT_METADATA_UNSPECIFIED", "DIRECTORY", "SIMPLE"], "enumDescriptions": ["Unspecified by default.", "A synonym for `SIMPLE`.", "Directory listing of objects."], "type": "string"}, "parquetOptions": {"$ref": "ParquetOptions", "description": "Optional. Additional properties to set if sourceFormat is set to PARQUET."}, "referenceFileSchemaUri": {"description": "Optional. When creating an external table, the user can provide a reference file with the table schema. This is enabled for the following formats: AVRO, PARQUET, ORC.", "type": "string"}, "schema": {"$ref": "TableSchema", "description": "Optional. The schema for the data. Schema is required for CSV and JSON formats if autodetect is not on. Schema is disallowed for Google Cloud Bigtable, Cloud Datastore backups, Avro, ORC and Parquet formats."}, "sourceFormat": {"description": "[Required] The data format. For CSV files, specify \"CSV\". For Google sheets, specify \"GOOGLE_SHEETS\". For newline-delimited JSON, specify \"NEWLINE_DELIMITED_JSON\". For Avro files, specify \"AVRO\". For Google Cloud Datastore backups, specify \"DATASTORE_BACKUP\". For Apache Iceberg tables, specify \"ICEBERG\". For ORC files, specify \"ORC\". For Parquet files, specify \"PARQUET\". [Beta] For Google Cloud Bigtable, specify \"BIGTABLE\".", "type": "string"}, "sourceUris": {"description": "[Required] The fully-qualified URIs that point to your data in Google Cloud. For Google Cloud Storage URIs: Each URI can contain one '*' wildcard character and it must come after the 'bucket' name. Size limits related to load jobs apply to external data sources. For Google Cloud Bigtable URIs: Exactly one URI can be specified and it has be a fully specified and valid HTTPS URL for a Google Cloud Bigtable table. For Google Cloud Datastore backups, exactly one URI can be specified. Also, the '*' wildcard character is not allowed.", "items": {"type": "string"}, "type": "array"}, "timeFormat": {"description": "Optional. Format used to parse TIME values. Supports C-style and SQL-style values.", "type": "string"}, "timeZone": {"description": "Optional. Time zone used when parsing timestamp values that do not have specific time zone information (e.g. 2024-04-20 12:34:56). The expected format is a IANA timezone string (e.g. America/Los_Angeles).", "type": "string"}, "timestampFormat": {"description": "Optional. Format used to parse TIMESTAMP values. Supports C-style and SQL-style values.", "type": "string"}}, "type": "object"}, "ExternalDatasetReference": {"description": "Configures the access a dataset defined in an external metadata storage.", "id": "ExternalDatasetReference", "properties": {"connection": {"description": "Required. The connection id that is used to access the external_source. Format: projects/{project_id}/locations/{location_id}/connections/{connection_id}", "type": "string"}, "externalSource": {"description": "Required. External source that backs this dataset.", "type": "string"}}, "type": "object"}, "ExternalServiceCost": {"description": "The external service cost is a portion of the total cost, these costs are not additive with total_bytes_billed. Moreover, this field only track external service costs that will show up as BigQuery costs (e.g. training BigQuery ML job with google cloud CAIP or Automl Tables services), not other costs which may be accrued by running the query (e.g. reading from Bigtable or Cloud Storage). The external service costs with different billing sku (e.g. CAIP job is charged based on VM usage) are converted to BigQuery billed_bytes and slot_ms with equivalent amount of US dollars. Services may not directly correlate to these metrics, but these are the equivalents for billing purposes. Output only.", "id": "ExternalServiceCost", "properties": {"bytesBilled": {"description": "External service cost in terms of bigquery bytes billed.", "format": "int64", "type": "string"}, "bytesProcessed": {"description": "External service cost in terms of bigquery bytes processed.", "format": "int64", "type": "string"}, "externalService": {"description": "External service name.", "type": "string"}, "reservedSlotCount": {"description": "Non-preemptable reserved slots used for external job. For example, reserved slots for Cloua AI Platform job are the VM usages converted to BigQuery slot with equivalent mount of price.", "format": "int64", "type": "string"}, "slotMs": {"description": "External service cost in terms of bigquery slot milliseconds.", "format": "int64", "type": "string"}}, "type": "object"}, "FeatureValue": {"description": "Representative value of a single feature within the cluster.", "id": "FeatureValue", "properties": {"categoricalValue": {"$ref": "CategoricalValue", "description": "The categorical feature value."}, "featureColumn": {"description": "The feature column name.", "type": "string"}, "numericalValue": {"description": "The numerical feature value. This is the centroid value for this feature.", "format": "double", "type": "number"}}, "type": "object"}, "ForeignTypeInfo": {"description": "Metadata about the foreign data type definition such as the system in which the type is defined.", "id": "ForeignTypeInfo", "properties": {"typeSystem": {"description": "Required. Specifies the system which defines the foreign data type.", "enum": ["TYPE_SYSTEM_UNSPECIFIED", "HIVE"], "enumDescriptions": ["TypeSystem not specified.", "Represents Hive data types."], "type": "string"}}, "type": "object"}, "ForeignViewDefinition": {"description": "A view can be represented in multiple ways. Each representation has its own dialect. This message stores the metadata required for these representations.", "id": "ForeignViewDefinition", "properties": {"dialect": {"description": "Optional. Represents the dialect of the query.", "type": "string"}, "query": {"description": "Required. The query that defines the view.", "type": "string"}}, "type": "object"}, "GetIamPolicyRequest": {"description": "Request message for `GetIamPolicy` method.", "id": "GetIamPolicyRequest", "properties": {"options": {"$ref": "GetPolicyOptions", "description": "OPTIONAL: A `GetPolicyOptions` object for specifying options to `GetIamPolicy`."}}, "type": "object"}, "GetPolicyOptions": {"description": "Encapsulates settings provided to GetIamPolicy.", "id": "GetPolicyOptions", "properties": {"requestedPolicyVersion": {"description": "Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "type": "integer"}}, "type": "object"}, "GetQueryResultsResponse": {"description": "Response object of GetQueryResults.", "id": "GetQueryResultsResponse", "properties": {"cacheHit": {"description": "Whether the query result was fetched from the query cache.", "type": "boolean"}, "errors": {"description": "Output only. The first errors or warnings encountered during the running of the job. The final message includes the number of errors that caused the process to stop. Errors here do not necessarily mean that the job has completed or was unsuccessful. For more information about error messages, see [Error messages](https://cloud.google.com/bigquery/docs/error-messages).", "items": {"$ref": "<PERSON><PERSON>r<PERSON><PERSON><PERSON>"}, "readOnly": true, "type": "array"}, "etag": {"description": "A hash of this response.", "type": "string"}, "jobComplete": {"description": "Whether the query has completed or not. If rows or totalRows are present, this will always be true. If this is false, totalRows will not be available.", "type": "boolean"}, "jobReference": {"$ref": "JobReference", "description": "Reference to the BigQuery Job that was created to run the query. This field will be present even if the original request timed out, in which case GetQueryResults can be used to read the results once the query has completed. Since this API only returns the first page of results, subsequent pages can be fetched via the same mechanism (GetQueryResults)."}, "kind": {"default": "bigquery#getQueryResultsResponse", "description": "The resource type of the response.", "type": "string"}, "numDmlAffectedRows": {"description": "Output only. The number of rows affected by a DML statement. Present only for DML statements INSERT, UPDATE or DELETE.", "format": "int64", "readOnly": true, "type": "string"}, "pageToken": {"description": "A token used for paging results. When this token is non-empty, it indicates additional results are available.", "type": "string"}, "rows": {"description": "An object with as many results as can be contained within the maximum permitted reply size. To get any additional rows, you can call GetQueryResults and specify the jobReference returned above. Present only when the query completes successfully. The REST-based representation of this data leverages a series of JSON f,v objects for indicating fields and values.", "items": {"$ref": "TableRow"}, "type": "array"}, "schema": {"$ref": "TableSchema", "description": "The schema of the results. Present only when the query completes successfully."}, "totalBytesProcessed": {"description": "The total number of bytes processed for this query.", "format": "int64", "type": "string"}, "totalRows": {"description": "The total number of rows in the complete query result set, which can be more than the number of rows in this single page of results. Present only when the query completes successfully.", "format": "uint64", "type": "string"}}, "type": "object"}, "GetServiceAccountResponse": {"description": "Response object of GetServiceAccount", "id": "GetServiceAccountResponse", "properties": {"email": {"description": "The service account email address.", "type": "string"}, "kind": {"default": "bigquery#getServiceAccountResponse", "description": "The resource type of the response.", "type": "string"}}, "type": "object"}, "GlobalExplanation": {"description": "Global explanations containing the top most important features after training.", "id": "GlobalExplanation", "properties": {"classLabel": {"description": "Class label for this set of global explanations. Will be empty/null for binary logistic and linear regression models. Sorted alphabetically in descending order.", "type": "string"}, "explanations": {"description": "A list of the top global explanations. Sorted by absolute value of attribution in descending order.", "items": {"$ref": "Explanation"}, "type": "array"}}, "type": "object"}, "GoogleSheetsOptions": {"description": "Options specific to Google Sheets data sources.", "id": "GoogleSheetsOptions", "properties": {"range": {"description": "Optional. Range of a sheet to query from. Only used when non-empty. Typical format: sheet_name!top_left_cell_id:bottom_right_cell_id For example: sheet1!A1:B20", "type": "string"}, "skipLeadingRows": {"description": "Optional. The number of rows at the top of a sheet that BigQuery will skip when reading the data. The default value is 0. This property is useful if you have header rows that should be skipped. When autodetect is on, the behavior is the following: * skipLeadingRows unspecified - Autodetect tries to detect headers in the first row. If they are not detected, the row is read as data. Otherwise data is read starting from the second row. * skipLeadingRows is 0 - Instructs autodetect that there are no headers and data should be read starting from the first row. * skipLeadingRows = N > 0 - Autodetect skips N-1 rows and tries to detect headers in row N. If headers are not detected, row N is just skipped. Otherwise row N is used to extract column names for the detected schema.", "format": "int64", "type": "string"}}, "type": "object"}, "HighCardinalityJoin": {"description": "High cardinality join detailed information.", "id": "HighCardinalityJoin", "properties": {"leftRows": {"description": "Output only. Count of left input rows.", "format": "int64", "readOnly": true, "type": "string"}, "outputRows": {"description": "Output only. Count of the output rows.", "format": "int64", "readOnly": true, "type": "string"}, "rightRows": {"description": "Output only. Count of right input rows.", "format": "int64", "readOnly": true, "type": "string"}, "stepIndex": {"description": "Output only. The index of the join operator in the ExplainQueryStep lists.", "format": "int32", "readOnly": true, "type": "integer"}}, "type": "object"}, "HivePartitioningOptions": {"description": "Options for configuring hive partitioning detect.", "id": "HivePartitioningOptions", "properties": {"fields": {"description": "Output only. For permanent external tables, this field is populated with the hive partition keys in the order they were inferred. The types of the partition keys can be deduced by checking the table schema (which will include the partition keys). Not every API will populate this field in the output. For example, Tables.Get will populate it, but Tables.List will not contain this field.", "items": {"type": "string"}, "readOnly": true, "type": "array"}, "mode": {"description": "Optional. When set, what mode of hive partitioning to use when reading data. The following modes are supported: * AUTO: automatically infer partition key name(s) and type(s). * STRINGS: automatically infer partition key name(s). All types are strings. * CUSTOM: partition key schema is encoded in the source URI prefix. Not all storage formats support hive partitioning. Requesting hive partitioning on an unsupported format will lead to an error. Currently supported formats are: JSON, CSV, ORC, Avro and Parquet.", "type": "string"}, "requirePartitionFilter": {"default": "false", "description": "Optional. If set to true, queries over this table require a partition filter that can be used for partition elimination to be specified. Note that this field should only be true when creating a permanent external table or querying a temporary external table. Hive-partitioned loads with require_partition_filter explicitly set to true will fail.", "type": "boolean"}, "sourceUriPrefix": {"description": "Optional. When hive partition detection is requested, a common prefix for all source uris must be required. The prefix must end immediately before the partition key encoding begins. For example, consider files following this data layout: gs://bucket/path_to_table/dt=2019-06-01/country=USA/id=7/file.avro gs://bucket/path_to_table/dt=2019-05-31/country=CA/id=3/file.avro When hive partitioning is requested with either AUTO or STRINGS detection, the common prefix can be either of gs://bucket/path_to_table or gs://bucket/path_to_table/. CUSTOM detection requires encoding the partitioning schema immediately after the common prefix. For CUSTOM, any of * gs://bucket/path_to_table/{dt:DATE}/{country:STRING}/{id:INTEGER} * gs://bucket/path_to_table/{dt:STRING}/{country:STRING}/{id:INTEGER} * gs://bucket/path_to_table/{dt:DATE}/{country:STRING}/{id:STRING} would all be valid source URI prefixes.", "type": "string"}}, "type": "object"}, "HparamSearchSpaces": {"description": "Hyperparameter search spaces. These should be a subset of training_options.", "id": "HparamSearchSpaces", "properties": {"activationFn": {"$ref": "StringHparamSearchSpace", "description": "Activation functions of neural network models."}, "batchSize": {"$ref": "IntHparamSearchSpace", "description": "Mini batch sample size."}, "boosterType": {"$ref": "StringHparamSearchSpace", "description": "Booster type for boosted tree models."}, "colsampleBylevel": {"$ref": "DoubleHparamSearchSpace", "description": "Subsample ratio of columns for each level for boosted tree models."}, "colsampleBynode": {"$ref": "DoubleHparamSearchSpace", "description": "Subsample ratio of columns for each node(split) for boosted tree models."}, "colsampleBytree": {"$ref": "DoubleHparamSearchSpace", "description": "Subsample ratio of columns when constructing each tree for boosted tree models."}, "dartNormalizeType": {"$ref": "StringHparamSearchSpace", "description": "Dart normalization type for boosted tree models."}, "dropout": {"$ref": "DoubleHparamSearchSpace", "description": "Dropout probability for dnn model training and boosted tree models using dart booster."}, "hiddenUnits": {"$ref": "IntArrayHparamSearchSpace", "description": "Hidden units for neural network models."}, "l1Reg": {"$ref": "DoubleHparamSearchSpace", "description": "L1 regularization coefficient."}, "l2Reg": {"$ref": "DoubleHparamSearchSpace", "description": "L2 regularization coefficient."}, "learnRate": {"$ref": "DoubleHparamSearchSpace", "description": "Learning rate of training jobs."}, "maxTreeDepth": {"$ref": "IntHparamSearchSpace", "description": "Maximum depth of a tree for boosted tree models."}, "minSplitLoss": {"$ref": "DoubleHparamSearchSpace", "description": "Minimum split loss for boosted tree models."}, "minTreeChildWeight": {"$ref": "IntHparamSearchSpace", "description": "Minimum sum of instance weight needed in a child for boosted tree models."}, "numClusters": {"$ref": "IntHparamSearchSpace", "description": "Number of clusters for k-means."}, "numFactors": {"$ref": "IntHparamSearchSpace", "description": "Number of latent factors to train on."}, "numParallelTree": {"$ref": "IntHparamSearchSpace", "description": "Number of parallel trees for boosted tree models."}, "optimizer": {"$ref": "StringHparamSearchSpace", "description": "Optimizer of TF models."}, "subsample": {"$ref": "DoubleHparamSearchSpace", "description": "Subsample the training data to grow tree to prevent overfitting for boosted tree models."}, "treeMethod": {"$ref": "StringHparamSearchSpace", "description": "Tree construction algorithm for boosted tree models."}, "walsAlpha": {"$ref": "DoubleHparamSearchSpace", "description": "Hyperparameter for matrix factoration when implicit feedback type is specified."}}, "type": "object"}, "HparamTuningTrial": {"description": "Training info of a trial in [hyperparameter tuning](https://cloud.google.com/bigquery-ml/docs/reference/standard-sql/bigqueryml-syntax-hp-tuning-overview) models.", "id": "HparamTuningTrial", "properties": {"endTimeMs": {"description": "Ending time of the trial.", "format": "int64", "type": "string"}, "errorMessage": {"description": "Error message for FAILED and INFEASIBLE trial.", "type": "string"}, "evalLoss": {"description": "Loss computed on the eval data at the end of trial.", "format": "double", "type": "number"}, "evaluationMetrics": {"$ref": "EvaluationMetrics", "description": "Evaluation metrics of this trial calculated on the test data. Empty in Job API."}, "hparamTuningEvaluationMetrics": {"$ref": "EvaluationMetrics", "description": "Hyperparameter tuning evaluation metrics of this trial calculated on the eval data. Unlike evaluation_metrics, only the fields corresponding to the hparam_tuning_objectives are set."}, "hparams": {"$ref": "TrainingOptions", "description": "The hyperprameters selected for this trial."}, "startTimeMs": {"description": "Starting time of the trial.", "format": "int64", "type": "string"}, "status": {"description": "The status of the trial.", "enum": ["TRIAL_STATUS_UNSPECIFIED", "NOT_STARTED", "RUNNING", "SUCCEEDED", "FAILED", "INFEASIBLE", "STOPPED_EARLY"], "enumDescriptions": ["Default value.", "Scheduled but not started.", "Running state.", "The trial succeeded.", "The trial failed.", "The trial is infeasible due to the invalid params.", "Trial stopped early because it's not promising."], "type": "string"}, "trainingLoss": {"description": "Loss computed on the training data at the end of trial.", "format": "double", "type": "number"}, "trialId": {"description": "1-based index of the trial.", "format": "int64", "type": "string"}}, "type": "object"}, "IndexUnusedReason": {"description": "Reason about why no search index was used in the search query (or sub-query).", "id": "IndexUnusedReason", "properties": {"baseTable": {"$ref": "TableReference", "description": "Specifies the base table involved in the reason that no search index was used."}, "code": {"description": "Specifies the high-level reason for the scenario when no search index was used.", "enum": ["CODE_UNSPECIFIED", "INDEX_CONFIG_NOT_AVAILABLE", "PENDING_INDEX_CREATION", "BASE_TABLE_TRUNCATED", "INDEX_CONFIG_MODIFIED", "TIME_TRAVEL_QUERY", "NO_PRUNING_POWER", "UNINDEXED_SEARCH_FIELDS", "UNSUPPORTED_SEARCH_PATTERN", "OPTIMIZED_WITH_MATERIALIZED_VIEW", "SECURED_BY_DATA_MASKING", "MISMATCHED_TEXT_ANALYZER", "BASE_TABLE_TOO_SMALL", "BASE_TABLE_TOO_LARGE", "ESTIMATED_PERFORMANCE_GAIN_TOO_LOW", "NOT_SUPPORTED_IN_STANDARD_EDITION", "INDEX_SUPPRESSED_BY_FUNCTION_OPTION", "QUERY_CACHE_HIT", "STALE_INDEX", "INTERNAL_ERROR", "OTHER_REASON"], "enumDescriptions": ["Code not specified.", "Indicates the search index configuration has not been created.", "Indicates the search index creation has not been completed.", "Indicates the base table has been truncated (rows have been removed from table with TRUNCATE TABLE statement) since the last time the search index was refreshed.", "Indicates the search index configuration has been changed since the last time the search index was refreshed.", "Indicates the search query accesses data at a timestamp before the last time the search index was refreshed.", "Indicates the usage of search index will not contribute to any pruning improvement for the search function, e.g. when the search predicate is in a disjunction with other non-search predicates.", "Indicates the search index does not cover all fields in the search function.", "Indicates the search index does not support the given search query pattern.", "Indicates the query has been optimized by using a materialized view.", "Indicates the query has been secured by data masking, and thus search indexes are not applicable.", "Indicates that the search index and the search function call do not have the same text analyzer.", "Indicates the base table is too small (below a certain threshold). The index does not provide noticeable search performance gains when the base table is too small.", "Indicates that the total size of indexed base tables in your organization exceeds your region's limit and the index is not used in the query. To index larger base tables, you can use your own reservation for index-management jobs.", "Indicates that the estimated performance gain from using the search index is too low for the given search query.", "Indicates that search indexes can not be used for search query with STANDARD edition.", "Indicates that an option in the search function that cannot make use of the index has been selected.", "Indicates that the query was cached, and thus the search index was not used.", "The index cannot be used in the search query because it is stale.", "Indicates an internal error that causes the search index to be unused.", "Indicates that the reason search indexes cannot be used in the query is not covered by any of the other IndexUnusedReason options."], "type": "string"}, "indexName": {"description": "Specifies the name of the unused search index, if available.", "type": "string"}, "message": {"description": "Free form human-readable reason for the scenario when no search index was used.", "type": "string"}}, "type": "object"}, "InputDataChange": {"description": "Details about the input data change insight.", "id": "InputDataChange", "properties": {"recordsReadDiffPercentage": {"description": "Output only. Records read difference percentage compared to a previous run.", "format": "float", "readOnly": true, "type": "number"}}, "type": "object"}, "IntArray": {"description": "An array of int.", "id": "IntArray", "properties": {"elements": {"description": "Elements in the int array.", "items": {"format": "int64", "type": "string"}, "type": "array"}}, "type": "object"}, "IntArrayHparamSearchSpace": {"description": "Search space for int array.", "id": "IntArrayHparamSearchSpace", "properties": {"candidates": {"description": "Candidates for the int array parameter.", "items": {"$ref": "IntArray"}, "type": "array"}}, "type": "object"}, "IntCandidates": {"description": "Discrete candidates of an int hyperparameter.", "id": "IntCandidates", "properties": {"candidates": {"description": "Candidates for the int parameter in increasing order.", "items": {"format": "int64", "type": "string"}, "type": "array"}}, "type": "object"}, "IntHparamSearchSpace": {"description": "Search space for an int hyperparameter.", "id": "IntHparamSearchSpace", "properties": {"candidates": {"$ref": "IntCandidates", "description": "Candidates of the int hyperparameter."}, "range": {"$ref": "IntRange", "description": "Range of the int hyperparameter."}}, "type": "object"}, "IntRange": {"description": "Range of an int hyperparameter.", "id": "IntRange", "properties": {"max": {"description": "Max value of the int parameter.", "format": "int64", "type": "string"}, "min": {"description": "Min value of the int parameter.", "format": "int64", "type": "string"}}, "type": "object"}, "IterationResult": {"description": "Information about a single iteration of the training run.", "id": "IterationResult", "properties": {"arimaResult": {"$ref": "ArimaResult", "description": "Arima result."}, "clusterInfos": {"description": "Information about top clusters for clustering models.", "items": {"$ref": "ClusterInfo"}, "type": "array"}, "durationMs": {"description": "Time taken to run the iteration in milliseconds.", "format": "int64", "type": "string"}, "evalLoss": {"description": "Loss computed on the eval data at the end of iteration.", "format": "double", "type": "number"}, "index": {"description": "Index of the iteration, 0 based.", "format": "int32", "type": "integer"}, "learnRate": {"description": "Learn rate used for this iteration.", "format": "double", "type": "number"}, "principalComponentInfos": {"description": "The information of the principal components.", "items": {"$ref": "PrincipalComponentInfo"}, "type": "array"}, "trainingLoss": {"description": "Loss computed on the training data at the end of iteration.", "format": "double", "type": "number"}}, "type": "object"}, "Job": {"id": "Job", "properties": {"configuration": {"$ref": "JobConfiguration", "description": "Required. Describes the job configuration."}, "etag": {"description": "Output only. A hash of this resource.", "readOnly": true, "type": "string"}, "id": {"description": "Output only. Opaque ID field of the job.", "readOnly": true, "type": "string"}, "jobCreationReason": {"$ref": "JobCreationReason", "description": "Output only. The reason why a Job was created. [Preview](https://cloud.google.com/products/#product-launch-stages)", "readOnly": true}, "jobReference": {"$ref": "JobReference", "description": "Optional. Reference describing the unique-per-user name of the job."}, "kind": {"default": "bigquery#job", "description": "Output only. The type of the resource.", "readOnly": true, "type": "string"}, "principal_subject": {"description": "Output only. [Full-projection-only] String representation of identity of requesting party. Populated for both first- and third-party identities. Only present for APIs that support third-party identities.", "readOnly": true, "type": "string"}, "selfLink": {"description": "Output only. A URL that can be used to access the resource again.", "readOnly": true, "type": "string"}, "statistics": {"$ref": "JobStatistics", "description": "Output only. Information about the job, including starting time and ending time of the job.", "readOnly": true}, "status": {"$ref": "JobStatus", "description": "Output only. The status of this job. Examine this value when polling an asynchronous job to see if the job is complete.", "readOnly": true}, "user_email": {"description": "Output only. Email address of the user who ran the job.", "readOnly": true, "type": "string"}}, "type": "object"}, "JobCancelResponse": {"description": "Describes format of a jobs cancellation response.", "id": "JobCancelResponse", "properties": {"job": {"$ref": "Job", "description": "The final state of the job."}, "kind": {"default": "bigquery#jobCancelResponse", "description": "The resource type of the response.", "type": "string"}}, "type": "object"}, "JobConfiguration": {"id": "JobConfiguration", "properties": {"copy": {"$ref": "JobConfigurationTableCopy", "description": "[Pick one] Copies a table."}, "dryRun": {"description": "Optional. If set, don't actually run this job. A valid query will return a mostly empty response with some processing statistics, while an invalid query will return the same error it would if it wasn't a dry run. Behavior of non-query jobs is undefined.", "type": "boolean"}, "extract": {"$ref": "JobConfigurationExtract", "description": "[Pick one] Configures an extract job."}, "jobTimeoutMs": {"description": "Optional. Job timeout in milliseconds. If this time limit is exceeded, <PERSON><PERSON><PERSON><PERSON> will attempt to stop a longer job, but may not always succeed in canceling it before the job completes. For example, a job that takes more than 60 seconds to complete has a better chance of being stopped than a job that takes 10 seconds to complete.", "format": "int64", "type": "string"}, "jobType": {"description": "Output only. The type of the job. Can be QUERY, LOAD, EXTRACT, COPY or UNKNOWN.", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "The labels associated with this job. You can use these to organize and group your jobs. Label keys and values can be no longer than 63 characters, can only contain lowercase letters, numeric characters, underscores and dashes. International characters are allowed. Label values are optional. Label keys must start with a letter and each label in the list must have a different key.", "type": "object"}, "load": {"$ref": "JobConfigurationLoad", "description": "[Pick one] Configures a load job."}, "query": {"$ref": "JobConfigurationQuery", "description": "[Pick one] Configures a query job."}, "reservation": {"description": "Optional. The reservation that job would use. User can specify a reservation to execute the job. If reservation is not set, reservation is determined based on the rules defined by the reservation assignments. The expected format is `projects/{project}/locations/{location}/reservations/{reservation}`.", "type": "string"}}, "type": "object"}, "JobConfigurationExtract": {"description": "JobConfigurationExtract configures a job that exports data from a BigQuery table into Google Cloud Storage.", "id": "JobConfigurationExtract", "properties": {"compression": {"description": "Optional. The compression type to use for exported files. Possible values include DEFLATE, GZIP, NONE, SNAPPY, and ZSTD. The default value is NONE. Not all compression formats are support for all file formats. DEFLATE is only supported for Avro. ZSTD is only supported for Parquet. Not applicable when extracting models.", "type": "string"}, "destinationFormat": {"description": "Optional. The exported file format. Possible values include CSV, NEWLINE_DELIMITED_JSON, PARQUET, or AVRO for tables and ML_TF_SAVED_MODEL or ML_XGBOOST_BOOSTER for models. The default value for tables is CSV. Tables with nested or repeated fields cannot be exported as CSV. The default value for models is ML_TF_SAVED_MODEL.", "type": "string"}, "destinationUri": {"description": "[Pick one] DEPRECATED: Use destinationUris instead, passing only one URI as necessary. The fully-qualified Google Cloud Storage URI where the extracted table should be written.", "type": "string"}, "destinationUris": {"description": "[Pick one] A list of fully-qualified Google Cloud Storage URIs where the extracted table should be written.", "items": {"type": "string"}, "type": "array"}, "fieldDelimiter": {"description": "Optional. When extracting data in CSV format, this defines the delimiter to use between fields in the exported data. Default is ','. Not applicable when extracting models.", "type": "string"}, "modelExtractOptions": {"$ref": "ModelExtractOptions", "description": "Optional. Model extract options only applicable when extracting models."}, "printHeader": {"default": "true", "description": "Optional. Whether to print out a header row in the results. Default is true. Not applicable when extracting models.", "type": "boolean"}, "sourceModel": {"$ref": "ModelReference", "description": "A reference to the model being exported."}, "sourceTable": {"$ref": "TableReference", "description": "A reference to the table being exported."}, "useAvroLogicalTypes": {"description": "Whether to use logical types when extracting to AVRO format. Not applicable when extracting models.", "type": "boolean"}}, "type": "object"}, "JobConfigurationLoad": {"description": "JobConfigurationLoad contains the configuration properties for loading data into a destination table.", "id": "JobConfigurationLoad", "properties": {"allowJaggedRows": {"description": "Optional. Accept rows that are missing trailing optional columns. The missing values are treated as nulls. If false, records with missing trailing columns are treated as bad records, and if there are too many bad records, an invalid error is returned in the job result. The default value is false. Only applicable to CSV, ignored for other formats.", "type": "boolean"}, "allowQuotedNewlines": {"description": "Indicates if BigQuery should allow quoted data sections that contain newline characters in a CSV file. The default value is false.", "type": "boolean"}, "autodetect": {"description": "Optional. Indicates if we should automatically infer the options and schema for CSV and JSON sources.", "type": "boolean"}, "clustering": {"$ref": "Clustering", "description": "Clustering specification for the destination table."}, "columnNameCharacterMap": {"description": "Optional. Character map supported for column names in CSV/Parquet loads. Defaults to STRICT and can be overridden by Project Config Service. Using this option with unsupporting load formats will result in an error.", "enum": ["COLUMN_NAME_CHARACTER_MAP_UNSPECIFIED", "STRICT", "V1", "V2"], "enumDescriptions": ["Unspecified column name character map.", "Support flexible column name and reject invalid column names.", "Support alphanumeric + underscore characters and names must start with a letter or underscore. Invalid column names will be normalized.", "Support flexible column name. Invalid column names will be normalized."], "type": "string"}, "connectionProperties": {"description": "Optional. Connection properties which can modify the load job behavior. Currently, only the 'session_id' connection property is supported, and is used to resolve _SESSION appearing as the dataset id.", "items": {"$ref": "ConnectionProperty"}, "type": "array"}, "copyFilesOnly": {"description": "Optional. [Experimental] Configures the load job to copy files directly to the destination BigLake managed table, bypassing file content reading and rewriting. Copying files only is supported when all the following are true: * `source_uris` are located in the same Cloud Storage location as the destination table's `storage_uri` location. * `source_format` is `PARQUET`. * `destination_table` is an existing BigLake managed table. The table's schema does not have flexible column names. The table's columns do not have type parameters other than precision and scale. * No options other than the above are specified.", "type": "boolean"}, "createDisposition": {"description": "Optional. Specifies whether the job is allowed to create new tables. The following values are supported: * CREATE_IF_NEEDED: If the table does not exist, BigQuery creates the table. * CREATE_NEVER: The table must already exist. If it does not, a 'notFound' error is returned in the job result. The default value is CREATE_IF_NEEDED. Creation, truncation and append actions occur as one atomic update upon job completion.", "type": "string"}, "createSession": {"description": "Optional. If this property is true, the job creates a new session using a randomly generated session_id. To continue using a created session with subsequent queries, pass the existing session identifier as a `ConnectionProperty` value. The session identifier is returned as part of the `SessionInfo` message within the query statistics. The new session's location will be set to `Job.JobReference.location` if it is present, otherwise it's set to the default location based on existing routing logic.", "type": "boolean"}, "dateFormat": {"description": "Optional. Date format used for parsing DATE values.", "type": "string"}, "datetimeFormat": {"description": "Optional. Date format used for parsing DATETIME values.", "type": "string"}, "decimalTargetTypes": {"description": "Defines the list of possible SQL data types to which the source decimal values are converted. This list and the precision and the scale parameters of the decimal field determine the target type. In the order of NUMERIC, BIGNUMERIC, and STRING, a type is picked if it is in the specified list and if it supports the precision and the scale. STRING supports all precision and scale values. If none of the listed types supports the precision and the scale, the type supporting the widest range in the specified list is picked, and if a value exceeds the supported range when reading the data, an error will be thrown. Example: Suppose the value of this field is [\"NUMER<PERSON>\", \"BIGNUME<PERSON><PERSON>\"]. If (precision,scale) is: * (38,9) -> NUMERIC; * (39,9) -> BIGNUMERIC (NUMERIC cannot hold 30 integer digits); * (38,10) -> BIGNUMERIC (NUMERIC cannot hold 10 fractional digits); * (76,38) -> BIGNUMERIC; * (77,38) -> B<PERSON><PERSON><PERSON>RI<PERSON> (error if value exceeds supported range). This field cannot contain duplicate types. The order of the types in this field is ignored. For example, [\"<PERSON>IG<PERSON><PERSON>RI<PERSON>\", \"NUMER<PERSON>\"] is the same as [\"<PERSON><PERSON>ER<PERSON>\", \"<PERSON>IGNUME<PERSON><PERSON>\"] and NUMERIC always takes precedence over <PERSON><PERSON>NUMERI<PERSON>. Defaults to [\"<PERSON><PERSON>ER<PERSON>\", \"STRING\"] for ORC and [\"NUMERIC\"] for the other file formats.", "items": {"enum": ["DECIMAL_TARGET_TYPE_UNSPECIFIED", "NUMERIC", "BIGNUMERIC", "STRING"], "enumDescriptions": ["Invalid type.", "Decimal values could be converted to NUMERIC type.", "Decimal values could be converted to BIGNUMERIC type.", "Decimal values could be converted to STRING type."], "type": "string"}, "type": "array"}, "destinationEncryptionConfiguration": {"$ref": "EncryptionConfiguration", "description": "Custom encryption configuration (e.g., Cloud KMS keys)"}, "destinationTable": {"$ref": "TableReference", "description": "[Required] The destination table to load the data into."}, "destinationTableProperties": {"$ref": "DestinationTableProperties", "description": "Optional. [Experimental] Properties with which to create the destination table if it is new."}, "encoding": {"description": "Optional. The character encoding of the data. The supported values are UTF-8, ISO-8859-1, UTF-16BE, UTF-16LE, UTF-32BE, and UTF-32LE. The default value is UTF-8. BigQuery decodes the data after the raw, binary data has been split using the values of the `quote` and `fieldDelimiter` properties. If you don't specify an encoding, or if you specify a UTF-8 encoding when the CSV file is not UTF-8 encoded, BigQuery attempts to convert the data to UTF-8. Generally, your data loads successfully, but it may not match byte-for-byte what you expect. To avoid this, specify the correct encoding by using the `--encoding` flag. If BigQuery can't convert a character other than the ASCII `0` character, BigQuery converts the character to the standard Unicode replacement character: �.", "type": "string"}, "fieldDelimiter": {"description": "Optional. The separator character for fields in a CSV file. The separator is interpreted as a single byte. For files encoded in ISO-8859-1, any single character can be used as a separator. For files encoded in UTF-8, characters represented in decimal range 1-127 (U+0001-U+007F) can be used without any modification. UTF-8 characters encoded with multiple bytes (i.e. U+0080 and above) will have only the first byte used for separating fields. The remaining bytes will be treated as a part of the field. BigQuery also supports the escape sequence \"\\t\" (U+0009) to specify a tab separator. The default value is comma (\",\", U+002C).", "type": "string"}, "fileSetSpecType": {"description": "Optional. Specifies how source URIs are interpreted for constructing the file set to load. By default, source URIs are expanded against the underlying storage. You can also specify manifest files to control how the file set is constructed. This option is only applicable to object storage systems.", "enum": ["FILE_SET_SPEC_TYPE_FILE_SYSTEM_MATCH", "FILE_SET_SPEC_TYPE_NEW_LINE_DELIMITED_MANIFEST"], "enumDescriptions": ["This option expands source URIs by listing files from the object store. It is the default behavior if FileSetSpecType is not set.", "This option indicates that the provided URIs are newline-delimited manifest files, with one URI per line. Wildcard URIs are not supported."], "type": "string"}, "hivePartitioningOptions": {"$ref": "HivePartitioningOptions", "description": "Optional. When set, configures hive partitioning support. Not all storage formats support hive partitioning -- requesting hive partitioning on an unsupported format will lead to an error, as will providing an invalid specification."}, "ignoreUnknownValues": {"description": "Optional. Indicates if BigQuery should allow extra values that are not represented in the table schema. If true, the extra values are ignored. If false, records with extra columns are treated as bad records, and if there are too many bad records, an invalid error is returned in the job result. The default value is false. The sourceFormat property determines what BigQuery treats as an extra value: CSV: Trailing columns JSON: Named values that don't match any column names in the table schema Avro, Parquet, ORC: Fields in the file schema that don't exist in the table schema.", "type": "boolean"}, "jsonExtension": {"description": "Optional. Load option to be used together with source_format newline-delimited JSON to indicate that a variant of JSON is being loaded. To load newline-delimited GeoJSON, specify GEOJSON (and source_format must be set to NEWLINE_DELIMITED_JSON).", "enum": ["JSON_EXTENSION_UNSPECIFIED", "GEOJSON"], "enumDescriptions": ["The default if provided value is not one included in the enum, or the value is not specified. The source format is parsed without any modification.", "Use GeoJSON variant of JSON. See https://tools.ietf.org/html/rfc7946."], "type": "string"}, "maxBadRecords": {"description": "Optional. The maximum number of bad records that <PERSON><PERSON><PERSON><PERSON> can ignore when running the job. If the number of bad records exceeds this value, an invalid error is returned in the job result. The default value is 0, which requires that all records are valid. This is only supported for CSV and NEWLINE_DELIMITED_JSON file formats.", "format": "int32", "type": "integer"}, "nullMarker": {"description": "Optional. Specifies a string that represents a null value in a CSV file. For example, if you specify \"\\N\", BigQuery interprets \"\\N\" as a null value when loading a CSV file. The default value is the empty string. If you set this property to a custom value, BigQuery throws an error if an empty string is present for all data types except for STRING and BYTE. For STRING and BYTE columns, BigQuery interprets the empty string as an empty value.", "type": "string"}, "nullMarkers": {"description": "Optional. A list of strings represented as SQL NULL value in a CSV file. null_marker and null_markers can't be set at the same time. If null_marker is set, null_markers has to be not set. If null_markers is set, null_marker has to be not set. If both null_marker and null_markers are set at the same time, a user error would be thrown. Any strings listed in null_markers, including empty string would be interpreted as SQL NULL. This applies to all column types.", "items": {"type": "string"}, "type": "array"}, "parquetOptions": {"$ref": "ParquetOptions", "description": "Optional. Additional properties to set if sourceFormat is set to PARQUET."}, "preserveAsciiControlCharacters": {"description": "Optional. When sourceFormat is set to \"CSV\", this indicates whether the embedded ASCII control characters (the first 32 characters in the ASCII-table, from '\\x00' to '\\x1F') are preserved.", "type": "boolean"}, "projectionFields": {"description": "If sourceFormat is set to \"DATASTORE_BACKUP\", indicates which entity properties to load into BigQuery from a Cloud Datastore backup. Property names are case sensitive and must be top-level properties. If no properties are specified, BigQuery loads all properties. If any named property isn't found in the Cloud Datastore backup, an invalid error is returned in the job result.", "items": {"type": "string"}, "type": "array"}, "quote": {"default": "\"", "description": "Optional. The value that is used to quote data sections in a CSV file. BigQuery converts the string to ISO-8859-1 encoding, and then uses the first byte of the encoded string to split the data in its raw, binary state. The default value is a double-quote ('\"'). If your data does not contain quoted sections, set the property value to an empty string. If your data contains quoted newline characters, you must also set the allowQuotedNewlines property to true. To include the specific quote character within a quoted value, precede it with an additional matching quote character. For example, if you want to escape the default character ' \" ', use ' \"\" '. @default \"", "pattern": ".?", "type": "string"}, "rangePartitioning": {"$ref": "RangePartitioning", "description": "Range partitioning specification for the destination table. Only one of timePartitioning and rangePartitioning should be specified."}, "referenceFileSchemaUri": {"description": "Optional. The user can provide a reference file with the reader schema. This file is only loaded if it is part of source URIs, but is not loaded otherwise. It is enabled for the following formats: AVRO, PARQUET, ORC.", "type": "string"}, "schema": {"$ref": "TableSchema", "description": "Optional. The schema for the destination table. The schema can be omitted if the destination table already exists, or if you're loading data from Google Cloud Datastore."}, "schemaInline": {"description": "[Deprecated] The inline schema. For CSV schemas, specify as \"Field1:Type1[,Field2:Type2]*\". For example, \"foo:STRING, bar:INTEGER, baz:FLOAT\".", "type": "string"}, "schemaInlineFormat": {"description": "[Deprecated] The format of the schemaInline property.", "type": "string"}, "schemaUpdateOptions": {"description": "Allows the schema of the destination table to be updated as a side effect of the load job if a schema is autodetected or supplied in the job configuration. Schema update options are supported in two cases: when writeDisposition is WRITE_APPEND; when writeDisposition is WRITE_TRUNCATE and the destination table is a partition of a table, specified by partition decorators. For normal tables, WRITE_TRUNCATE will always overwrite the schema. One or more of the following values are specified: * ALLOW_FIELD_ADDITION: allow adding a nullable field to the schema. * ALLOW_FIELD_RELAXATION: allow relaxing a required field in the original schema to nullable.", "items": {"type": "string"}, "type": "array"}, "skipLeadingRows": {"description": "Optional. The number of rows at the top of a CSV file that BigQuery will skip when loading the data. The default value is 0. This property is useful if you have header rows in the file that should be skipped. When autodetect is on, the behavior is the following: * skipLeadingRows unspecified - Autodetect tries to detect headers in the first row. If they are not detected, the row is read as data. Otherwise data is read starting from the second row. * skipLeadingRows is 0 - Instructs autodetect that there are no headers and data should be read starting from the first row. * skipLeadingRows = N > 0 - Autodetect skips N-1 rows and tries to detect headers in row N. If headers are not detected, row N is just skipped. Otherwise row N is used to extract column names for the detected schema.", "format": "int32", "type": "integer"}, "sourceColumnMatch": {"description": "Optional. Controls the strategy used to match loaded columns to the schema. If not set, a sensible default is chosen based on how the schema is provided. If autodetect is used, then columns are matched by name. Otherwise, columns are matched by position. This is done to keep the behavior backward-compatible.", "enum": ["SOURCE_COLUMN_MATCH_UNSPECIFIED", "POSITION", "NAME"], "enumDescriptions": ["Uses sensible defaults based on how the schema is provided. If autodetect is used, then columns are matched by name. Otherwise, columns are matched by position. This is done to keep the behavior backward-compatible.", "Matches by position. This assumes that the columns are ordered the same way as the schema.", "Matches by name. This reads the header row as column names and reorders columns to match the field names in the schema."], "type": "string"}, "sourceFormat": {"description": "Optional. The format of the data files. For CSV files, specify \"CSV\". For datastore backups, specify \"DATASTORE_BACKUP\". For newline-delimited JSON, specify \"NEWLINE_DELIMITED_JSON\". For Avro, specify \"AVRO\". For parquet, specify \"PARQUET\". For orc, specify \"ORC\". The default value is CSV.", "type": "string"}, "sourceUris": {"description": "[Required] The fully-qualified URIs that point to your data in Google Cloud. For Google Cloud Storage URIs: Each URI can contain one '*' wildcard character and it must come after the 'bucket' name. Size limits related to load jobs apply to external data sources. For Google Cloud Bigtable URIs: Exactly one URI can be specified and it has be a fully specified and valid HTTPS URL for a Google Cloud Bigtable table. For Google Cloud Datastore backups: Exactly one URI can be specified. Also, the '*' wildcard character is not allowed.", "items": {"type": "string"}, "type": "array"}, "timeFormat": {"description": "Optional. Date format used for parsing TIME values.", "type": "string"}, "timePartitioning": {"$ref": "TimePartitioning", "description": "Time-based partitioning specification for the destination table. Only one of timePartitioning and rangePartitioning should be specified."}, "timeZone": {"description": "Optional. Default time zone that will apply when parsing timestamp values that have no specific time zone.", "type": "string"}, "timestampFormat": {"description": "Optional. Date format used for parsing TIMESTAMP values.", "type": "string"}, "useAvroLogicalTypes": {"description": "Optional. If sourceFormat is set to \"AVRO\", indicates whether to interpret logical types as the corresponding BigQuery data type (for example, TIMESTAMP), instead of using the raw type (for example, INTEGER).", "type": "boolean"}, "writeDisposition": {"description": "Optional. Specifies the action that occurs if the destination table already exists. The following values are supported: * WRITE_TRUNCATE: If the table already exists, <PERSON><PERSON><PERSON><PERSON> overwrites the data, removes the constraints and uses the schema from the load job. * WRITE_TRUNCATE_DATA: If the table already exists, <PERSON><PERSON><PERSON><PERSON> overwrites the data, but keeps the constraints and schema of the existing table. * WRITE_APPEND: If the table already exists, <PERSON><PERSON><PERSON><PERSON> appends the data to the table. * WRITE_EMPTY: If the table already exists and contains data, a 'duplicate' error is returned in the job result. The default value is WRITE_APPEND. Each action is atomic and only occurs if <PERSON><PERSON><PERSON><PERSON> is able to complete the job successfully. Creation, truncation and append actions occur as one atomic update upon job completion.", "type": "string"}}, "type": "object"}, "JobConfigurationQuery": {"description": "JobConfigurationQuery configures a BigQuery query job.", "id": "JobConfigurationQuery", "properties": {"allowLargeResults": {"default": "false", "description": "Optional. If true and query uses legacy SQL dialect, allows the query to produce arbitrarily large result tables at a slight cost in performance. Requires destinationTable to be set. For GoogleSQL queries, this flag is ignored and large results are always allowed. However, you must still set destinationTable when result size exceeds the allowed maximum response size.", "type": "boolean"}, "clustering": {"$ref": "Clustering", "description": "Clustering specification for the destination table."}, "connectionProperties": {"description": "Connection properties which can modify the query behavior.", "items": {"$ref": "ConnectionProperty"}, "type": "array"}, "continuous": {"description": "[Optional] Specifies whether the query should be executed as a continuous query. The default value is false.", "type": "boolean"}, "createDisposition": {"description": "Optional. Specifies whether the job is allowed to create new tables. The following values are supported: * CREATE_IF_NEEDED: If the table does not exist, BigQuery creates the table. * CREATE_NEVER: The table must already exist. If it does not, a 'notFound' error is returned in the job result. The default value is CREATE_IF_NEEDED. Creation, truncation and append actions occur as one atomic update upon job completion.", "type": "string"}, "createSession": {"description": "If this property is true, the job creates a new session using a randomly generated session_id. To continue using a created session with subsequent queries, pass the existing session identifier as a `ConnectionProperty` value. The session identifier is returned as part of the `SessionInfo` message within the query statistics. The new session's location will be set to `Job.JobReference.location` if it is present, otherwise it's set to the default location based on existing routing logic.", "type": "boolean"}, "defaultDataset": {"$ref": "DatasetReference", "description": "Optional. Specifies the default dataset to use for unqualified table names in the query. This setting does not alter behavior of unqualified dataset names. Setting the system variable `@@dataset_id` achieves the same behavior. See https://cloud.google.com/bigquery/docs/reference/system-variables for more information on system variables."}, "destinationEncryptionConfiguration": {"$ref": "EncryptionConfiguration", "description": "Custom encryption configuration (e.g., Cloud KMS keys)"}, "destinationTable": {"$ref": "TableReference", "description": "Optional. Describes the table where the query results should be stored. This property must be set for large results that exceed the maximum response size. For queries that produce anonymous (cached) results, this field will be populated by BigQuery."}, "flattenResults": {"default": "true", "description": "Optional. If true and query uses legacy SQL dialect, flattens all nested and repeated fields in the query results. allowLargeResults must be true if this is set to false. For GoogleSQL queries, this flag is ignored and results are never flattened.", "type": "boolean"}, "maximumBillingTier": {"default": "1", "description": "Optional. [Deprecated] Maximum billing tier allowed for this query. The billing tier controls the amount of compute resources allotted to the query, and multiplies the on-demand cost of the query accordingly. A query that runs within its allotted resources will succeed and indicate its billing tier in statistics.query.billingTier, but if the query exceeds its allotted resources, it will fail with billingTierLimitExceeded. WARNING: The billed byte amount can be multiplied by an amount up to this number! Most users should not need to alter this setting, and we recommend that you avoid introducing new uses of it.", "format": "int32", "type": "integer"}, "maximumBytesBilled": {"description": "Limits the bytes billed for this job. Queries that will have bytes billed beyond this limit will fail (without incurring a charge). If unspecified, this will be set to your project default.", "format": "int64", "type": "string"}, "parameterMode": {"description": "GoogleSQL only. Set to POSITIONAL to use positional (?) query parameters or to NAMED to use named (@myparam) query parameters in this query.", "type": "string"}, "preserveNulls": {"description": "[Deprecated] This property is deprecated.", "type": "boolean"}, "priority": {"description": "Optional. Specifies a priority for the query. Possible values include INTERACTIVE and BATCH. The default value is INTERACTIVE.", "type": "string"}, "query": {"description": "[Required] SQL query text to execute. The useLegacySql field can be used to indicate whether the query uses legacy SQL or GoogleSQL.", "type": "string"}, "queryParameters": {"description": "Query parameters for GoogleSQL queries.", "items": {"$ref": "QueryParameter"}, "type": "array"}, "rangePartitioning": {"$ref": "RangePartitioning", "description": "Range partitioning specification for the destination table. Only one of timePartitioning and rangePartitioning should be specified."}, "schemaUpdateOptions": {"description": "Allows the schema of the destination table to be updated as a side effect of the query job. Schema update options are supported in two cases: when writeDisposition is WRITE_APPEND; when writeDisposition is WRITE_TRUNCATE and the destination table is a partition of a table, specified by partition decorators. For normal tables, WRITE_TRUNCATE will always overwrite the schema. One or more of the following values are specified: * ALLOW_FIELD_ADDITION: allow adding a nullable field to the schema. * ALLOW_FIELD_RELAXATION: allow relaxing a required field in the original schema to nullable.", "items": {"type": "string"}, "type": "array"}, "scriptOptions": {"$ref": "ScriptOptions", "description": "Options controlling the execution of scripts."}, "systemVariables": {"$ref": "SystemVariables", "description": "Output only. System variables for GoogleSQL queries. A system variable is output if the variable is settable and its value differs from the system default. \"@@\" prefix is not included in the name of the System variables.", "readOnly": true}, "tableDefinitions": {"additionalProperties": {"$ref": "ExternalDataConfiguration"}, "description": "Optional. You can specify external table definitions, which operate as ephemeral tables that can be queried. These definitions are configured using a JSON map, where the string key represents the table identifier, and the value is the corresponding external data configuration object.", "type": "object"}, "timePartitioning": {"$ref": "TimePartitioning", "description": "Time-based partitioning specification for the destination table. Only one of timePartitioning and rangePartitioning should be specified."}, "useLegacySql": {"default": "true", "description": "Optional. Specifies whether to use BigQuery's legacy SQL dialect for this query. The default value is true. If set to false, the query will use BigQuery's GoogleSQL: https://cloud.google.com/bigquery/sql-reference/ When useLegacySql is set to false, the value of flattenResults is ignored; query will be run as if flattenResults is false.", "type": "boolean"}, "useQueryCache": {"default": "true", "description": "Optional. Whether to look for the result in the query cache. The query cache is a best-effort cache that will be flushed whenever tables in the query are modified. Moreover, the query cache is only available when a query does not have a destination table specified. The default value is true.", "type": "boolean"}, "userDefinedFunctionResources": {"description": "Describes user-defined function resources used in the query.", "items": {"$ref": "UserDefinedFunctionResource"}, "type": "array"}, "writeDisposition": {"description": "Optional. Specifies the action that occurs if the destination table already exists. The following values are supported: * WRITE_TRUNCATE: If the table already exists, <PERSON><PERSON><PERSON><PERSON> overwrites the data, removes the constraints, and uses the schema from the query result. * WRITE_TRUNCATE_DATA: If the table already exists, <PERSON><PERSON><PERSON><PERSON> overwrites the data, but keeps the constraints and schema of the existing table. * WRITE_APPEND: If the table already exists, <PERSON><PERSON><PERSON><PERSON> appends the data to the table. * WRITE_EMPTY: If the table already exists and contains data, a 'duplicate' error is returned in the job result. The default value is WRITE_EMPTY. Each action is atomic and only occurs if <PERSON><PERSON><PERSON><PERSON> is able to complete the job successfully. Creation, truncation and append actions occur as one atomic update upon job completion.", "type": "string"}, "writeIncrementalResults": {"description": "Optional. This is only supported for a SELECT query using a temporary table. If set, the query is allowed to write results incrementally to the temporary result table. This may incur a performance penalty. This option cannot be used with Legacy SQL. This feature is not yet available.", "type": "boolean"}}, "type": "object"}, "JobConfigurationTableCopy": {"description": "JobConfigurationTableCopy configures a job that copies data from one table to another. For more information on copying tables, see [Copy a table](https://cloud.google.com/bigquery/docs/managing-tables#copy-table).", "id": "JobConfigurationTableCopy", "properties": {"createDisposition": {"description": "Optional. Specifies whether the job is allowed to create new tables. The following values are supported: * CREATE_IF_NEEDED: If the table does not exist, BigQuery creates the table. * CREATE_NEVER: The table must already exist. If it does not, a 'notFound' error is returned in the job result. The default value is CREATE_IF_NEEDED. Creation, truncation and append actions occur as one atomic update upon job completion.", "type": "string"}, "destinationEncryptionConfiguration": {"$ref": "EncryptionConfiguration", "description": "Custom encryption configuration (e.g., Cloud KMS keys)."}, "destinationExpirationTime": {"description": "Optional. The time when the destination table expires. Expired tables will be deleted and their storage reclaimed.", "format": "google-datetime", "type": "string"}, "destinationTable": {"$ref": "TableReference", "description": "[Required] The destination table."}, "operationType": {"description": "Optional. Supported operation types in table copy job.", "enum": ["OPERATION_TYPE_UNSPECIFIED", "COPY", "SNAPSHOT", "RESTORE", "CLONE"], "enumDescriptions": ["Unspecified operation type.", "The source and destination table have the same table type.", "The source table type is TABLE and the destination table type is SNAPSHOT.", "The source table type is SNAPSHOT and the destination table type is TABLE.", "The source and destination table have the same table type, but only bill for unique data."], "type": "string"}, "sourceTable": {"$ref": "TableReference", "description": "[Pick one] Source table to copy."}, "sourceTables": {"description": "[Pick one] Source tables to copy.", "items": {"$ref": "TableReference"}, "type": "array"}, "writeDisposition": {"description": "Optional. Specifies the action that occurs if the destination table already exists. The following values are supported: * WRITE_TRUNCATE: If the table already exists, <PERSON><PERSON><PERSON><PERSON> overwrites the table data and uses the schema and table constraints from the source table. * WRITE_APPEND: If the table already exists, <PERSON>Query appends the data to the table. * WRITE_EMPTY: If the table already exists and contains data, a 'duplicate' error is returned in the job result. The default value is WRITE_EMPTY. Each action is atomic and only occurs if <PERSON><PERSON><PERSON><PERSON> is able to complete the job successfully. Creation, truncation and append actions occur as one atomic update upon job completion.", "type": "string"}}, "type": "object"}, "JobCreationReason": {"description": "Reason about why a Job was created from a [`jobs.query`](https://cloud.google.com/bigquery/docs/reference/rest/v2/jobs/query) method when used with `JOB_CREATION_OPTIONAL` Job creation mode. For [`jobs.insert`](https://cloud.google.com/bigquery/docs/reference/rest/v2/jobs/insert) method calls it will always be `REQUESTED`. [Preview](https://cloud.google.com/products/#product-launch-stages)", "id": "JobCreationReason", "properties": {"code": {"description": "Output only. Specifies the high level reason why a Job was created.", "enum": ["CODE_UNSPECIFIED", "REQUESTED", "LONG_RUNNING", "LARGE_RESULTS", "OTHER"], "enumDescriptions": ["Reason is not specified.", "Job creation was requested.", "The query request ran beyond a system defined timeout specified by the [timeoutMs field in the QueryRequest](https://cloud.google.com/bigquery/docs/reference/rest/v2/jobs/query#queryrequest). As a result it was considered a long running operation for which a job was created.", "The results from the query cannot fit in the response.", "BigQuery has determined that the query needs to be executed as a Job."], "readOnly": true, "type": "string"}}, "type": "object"}, "JobList": {"description": "JobList is the response format for a jobs.list call.", "id": "JobList", "properties": {"etag": {"description": "A hash of this page of results.", "type": "string"}, "jobs": {"description": "List of jobs that were requested.", "items": {"description": "ListFormatJob is a partial projection of job information returned as part of a jobs.list response.", "properties": {"configuration": {"$ref": "JobConfiguration", "description": "Required. Describes the job configuration."}, "errorResult": {"$ref": "<PERSON><PERSON>r<PERSON><PERSON><PERSON>", "description": "A result object that will be present only if the job has failed."}, "id": {"description": "Unique opaque ID of the job.", "type": "string"}, "jobReference": {"$ref": "JobReference", "description": "Unique opaque ID of the job."}, "kind": {"description": "The resource type.", "type": "string"}, "principal_subject": {"description": "[Full-projection-only] String representation of identity of requesting party. Populated for both first- and third-party identities. Only present for APIs that support third-party identities.", "type": "string"}, "state": {"description": "Running state of the job. When the state is DONE, errorResult can be checked to determine whether the job succeeded or failed.", "type": "string"}, "statistics": {"$ref": "JobStatistics", "description": "Output only. Information about the job, including starting time and ending time of the job.", "readOnly": true}, "status": {"$ref": "JobStatus", "description": "[Full-projection-only] Describes the status of this job."}, "user_email": {"description": "[Full-projection-only] Email address of the user who ran the job.", "type": "string"}}, "type": "object"}, "type": "array"}, "kind": {"default": "bigquery#jobList", "description": "The resource type of the response.", "type": "string"}, "nextPageToken": {"description": "A token to request the next page of results.", "type": "string"}, "unreachable": {"description": "A list of skipped locations that were unreachable. For more information about BigQuery locations, see: https://cloud.google.com/bigquery/docs/locations. Example: \"europe-west5\"", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "JobReference": {"description": "A job reference is a fully qualified identifier for referring to a job.", "id": "JobReference", "properties": {"jobId": {"description": "Required. The ID of the job. The ID must contain only letters (a-z, A-Z), numbers (0-9), underscores (_), or dashes (-). The maximum length is 1,024 characters.", "type": "string"}, "location": {"description": "Optional. The geographic location of the job. The default value is US. For more information about BigQuery locations, see: https://cloud.google.com/bigquery/docs/locations", "type": "string"}, "projectId": {"description": "Required. The ID of the project containing this job.", "type": "string"}}, "type": "object"}, "JobStatistics": {"description": "Statistics for a single job execution.", "id": "JobStatistics", "properties": {"completionRatio": {"description": "Output only. [TrustedTester] Job progress (0.0 -> 1.0) for LOAD and EXTRACT jobs.", "format": "double", "readOnly": true, "type": "number"}, "copy": {"$ref": "JobStatistics5", "description": "Output only. Statistics for a copy job.", "readOnly": true}, "creationTime": {"description": "Output only. Creation time of this job, in milliseconds since the epoch. This field will be present on all jobs.", "format": "int64", "readOnly": true, "type": "string"}, "dataMaskingStatistics": {"$ref": "DataMaskingStatistics", "description": "Output only. Statistics for data-masking. Present only for query and extract jobs.", "readOnly": true}, "edition": {"description": "Output only. Name of edition corresponding to the reservation for this job at the time of this update.", "enum": ["RESERVATION_EDITION_UNSPECIFIED", "STANDARD", "ENTERPRISE", "ENTERPRISE_PLUS"], "enumDescriptions": ["Default value, which will be treated as ENTERPRISE.", "Standard edition.", "Enterprise edition.", "Enterprise Plus edition."], "readOnly": true, "type": "string"}, "endTime": {"description": "Output only. End time of this job, in milliseconds since the epoch. This field will be present whenever a job is in the DONE state.", "format": "int64", "readOnly": true, "type": "string"}, "extract": {"$ref": "JobStatistics4", "description": "Output only. Statistics for an extract job.", "readOnly": true}, "finalExecutionDurationMs": {"description": "Output only. The duration in milliseconds of the execution of the final attempt of this job, as BigQuery may internally re-attempt to execute the job.", "format": "int64", "readOnly": true, "type": "string"}, "load": {"$ref": "JobStatistics3", "description": "Output only. Statistics for a load job.", "readOnly": true}, "numChildJobs": {"description": "Output only. Number of child jobs executed.", "format": "int64", "readOnly": true, "type": "string"}, "parentJobId": {"description": "Output only. If this is a child job, specifies the job ID of the parent.", "readOnly": true, "type": "string"}, "query": {"$ref": "JobStatistics2", "description": "Output only. Statistics for a query job.", "readOnly": true}, "quotaDeferments": {"description": "Output only. Quotas which delayed this job's start time.", "items": {"type": "string"}, "readOnly": true, "type": "array"}, "reservationUsage": {"deprecated": true, "description": "Output only. Job resource usage breakdown by reservation. This field reported misleading information and will no longer be populated.", "items": {"description": "Job resource usage breakdown by reservation.", "properties": {"name": {"description": "Reservation name or \"unreserved\" for on-demand resource usage and multi-statement queries.", "type": "string"}, "slotMs": {"description": "Total slot milliseconds used by the reservation for a particular job.", "format": "int64", "type": "string"}}, "type": "object"}, "readOnly": true, "type": "array"}, "reservation_id": {"description": "Output only. Name of the primary reservation assigned to this job. Note that this could be different than reservations reported in the reservation usage field if parent reservations were used to execute this job.", "readOnly": true, "type": "string"}, "rowLevelSecurityStatistics": {"$ref": "RowLevelSecurityStatistics", "description": "Output only. Statistics for row-level security. Present only for query and extract jobs.", "readOnly": true}, "scriptStatistics": {"$ref": "ScriptStatistics", "description": "Output only. If this a child job of a script, specifies information about the context of this job within the script.", "readOnly": true}, "sessionInfo": {"$ref": "SessionInfo", "description": "Output only. Information of the session if this job is part of one.", "readOnly": true}, "startTime": {"description": "Output only. Start time of this job, in milliseconds since the epoch. This field will be present when the job transitions from the PENDING state to either RUNNING or DONE.", "format": "int64", "readOnly": true, "type": "string"}, "totalBytesProcessed": {"description": "Output only. Total bytes processed for the job.", "format": "int64", "readOnly": true, "type": "string"}, "totalSlotMs": {"description": "Output only. Slot-milliseconds for the job.", "format": "int64", "readOnly": true, "type": "string"}, "transactionInfo": {"$ref": "TransactionInfo", "description": "Output only. [Alpha] Information of the multi-statement transaction if this job is part of one. This property is only expected on a child job or a job that is in a session. A script parent job is not part of the transaction started in the script.", "readOnly": true}}, "type": "object"}, "JobStatistics2": {"description": "Statistics for a query job.", "id": "JobStatistics2", "properties": {"biEngineStatistics": {"$ref": "BiEngineStatistics", "description": "Output only. BI Engine specific Statistics.", "readOnly": true}, "billingTier": {"description": "Output only. Billing tier for the job. This is a BigQuery-specific concept which is not related to the Google Cloud notion of \"free tier\". The value here is a measure of the query's resource consumption relative to the amount of data scanned. For on-demand queries, the limit is 100, and all queries within this limit are billed at the standard on-demand rates. On-demand queries that exceed this limit will fail with a billingTierLimitExceeded error.", "format": "int32", "readOnly": true, "type": "integer"}, "cacheHit": {"description": "Output only. Whether the query result was fetched from the query cache.", "readOnly": true, "type": "boolean"}, "dclTargetDataset": {"$ref": "DatasetReference", "description": "Output only. Referenced dataset for DCL statement.", "readOnly": true}, "dclTargetTable": {"$ref": "TableReference", "description": "Output only. Referenced table for DCL statement.", "readOnly": true}, "dclTargetView": {"$ref": "TableReference", "description": "Output only. Referenced view for DCL statement.", "readOnly": true}, "ddlAffectedRowAccessPolicyCount": {"description": "Output only. The number of row access policies affected by a DDL statement. Present only for DROP ALL ROW ACCESS POLICIES queries.", "format": "int64", "readOnly": true, "type": "string"}, "ddlDestinationTable": {"$ref": "TableReference", "description": "Output only. The table after rename. Present only for ALTER TABLE RENAME TO query.", "readOnly": true}, "ddlOperationPerformed": {"description": "Output only. The DDL operation performed, possibly dependent on the pre-existence of the DDL target.", "readOnly": true, "type": "string"}, "ddlTargetDataset": {"$ref": "DatasetReference", "description": "Output only. The DDL target dataset. Present only for CREATE/ALTER/DROP SCHEMA(dataset) queries.", "readOnly": true}, "ddlTargetRoutine": {"$ref": "RoutineReference", "description": "Output only. [Beta] The DDL target routine. Present only for CREATE/DROP FUNCTION/PROCEDURE queries.", "readOnly": true}, "ddlTargetRowAccessPolicy": {"$ref": "RowAccessPolicyReference", "description": "Output only. The DDL target row access policy. Present only for CREATE/DROP ROW ACCESS POLICY queries.", "readOnly": true}, "ddlTargetTable": {"$ref": "TableReference", "description": "Output only. The DDL target table. Present only for CREATE/DROP TABLE/VIEW and DROP ALL ROW ACCESS POLICIES queries.", "readOnly": true}, "dmlStats": {"$ref": "DmlStatistics", "description": "Output only. Detailed statistics for DML statements INSERT, UPDATE, DELETE, MERGE or TRUNCATE.", "readOnly": true}, "estimatedBytesProcessed": {"description": "Output only. The original estimate of bytes processed for the job.", "format": "int64", "readOnly": true, "type": "string"}, "exportDataStatistics": {"$ref": "ExportDataStatistics", "description": "Output only. Stats for EXPORT DATA statement.", "readOnly": true}, "externalServiceCosts": {"description": "Output only. Job cost breakdown as bigquery internal cost and external service costs.", "items": {"$ref": "ExternalServiceCost"}, "readOnly": true, "type": "array"}, "loadQueryStatistics": {"$ref": "LoadQueryStatistics", "description": "Output only. Statistics for a LOAD query.", "readOnly": true}, "materializedViewStatistics": {"$ref": "MaterializedViewStatistics", "description": "Output only. Statistics of materialized views of a query job.", "readOnly": true}, "metadataCacheStatistics": {"$ref": "MetadataCacheStatistics", "description": "Output only. Statistics of metadata cache usage in a query for BigLake tables.", "readOnly": true}, "mlStatistics": {"$ref": "MlStatistics", "description": "Output only. Statistics of a BigQuery ML training job.", "readOnly": true}, "modelTraining": {"$ref": "BigQueryModelTraining", "description": "Deprecated."}, "modelTrainingCurrentIteration": {"description": "Deprecated.", "format": "int32", "type": "integer"}, "modelTrainingExpectedTotalIteration": {"description": "Deprecated.", "format": "int64", "type": "string"}, "numDmlAffectedRows": {"description": "Output only. The number of rows affected by a DML statement. Present only for DML statements INSERT, UPDATE or DELETE.", "format": "int64", "readOnly": true, "type": "string"}, "performanceInsights": {"$ref": "PerformanceInsights", "description": "Output only. Performance insights.", "readOnly": true}, "queryInfo": {"$ref": "QueryInfo", "description": "Output only. Query optimization information for a QUERY job.", "readOnly": true}, "queryPlan": {"description": "Output only. Describes execution plan for the query.", "items": {"$ref": "ExplainQueryStage"}, "readOnly": true, "type": "array"}, "referencedRoutines": {"description": "Output only. Referenced routines for the job.", "items": {"$ref": "RoutineReference"}, "readOnly": true, "type": "array"}, "referencedTables": {"description": "Output only. Referenced tables for the job.", "items": {"$ref": "TableReference"}, "readOnly": true, "type": "array"}, "reservationUsage": {"deprecated": true, "description": "Output only. Job resource usage breakdown by reservation. This field reported misleading information and will no longer be populated.", "items": {"description": "Job resource usage breakdown by reservation.", "properties": {"name": {"description": "Reservation name or \"unreserved\" for on-demand resource usage and multi-statement queries.", "type": "string"}, "slotMs": {"description": "Total slot milliseconds used by the reservation for a particular job.", "format": "int64", "type": "string"}}, "type": "object"}, "readOnly": true, "type": "array"}, "schema": {"$ref": "TableSchema", "description": "Output only. The schema of the results. Present only for successful dry run of non-legacy SQL queries.", "readOnly": true}, "searchStatistics": {"$ref": "SearchStatistics", "description": "Output only. Search query specific statistics.", "readOnly": true}, "sparkStatistics": {"$ref": "SparkStatistics", "description": "Output only. Statistics of a Spark procedure job.", "readOnly": true}, "statementType": {"description": "Output only. The type of query statement, if valid. Possible values: * `SELECT`: [`SELECT`](https://cloud.google.com/bigquery/docs/reference/standard-sql/query-syntax#select_list) statement. * `ASSERT`: [`ASSERT`](https://cloud.google.com/bigquery/docs/reference/standard-sql/debugging-statements#assert) statement. * `INSERT`: [`INSERT`](https://cloud.google.com/bigquery/docs/reference/standard-sql/dml-syntax#insert_statement) statement. * `UPDATE`: [`UPDATE`](https://cloud.google.com/bigquery/docs/reference/standard-sql/dml-syntax#update_statement) statement. * `DELETE`: [`DELETE`](https://cloud.google.com/bigquery/docs/reference/standard-sql/data-manipulation-language) statement. * `MERGE`: [`MERGE`](https://cloud.google.com/bigquery/docs/reference/standard-sql/data-manipulation-language) statement. * `CREATE_TABLE`: [`CREATE TABLE`](https://cloud.google.com/bigquery/docs/reference/standard-sql/data-definition-language#create_table_statement) statement, without `AS SELECT`. * `CREATE_TABLE_AS_SELECT`: [`CREATE TABLE AS SELECT`](https://cloud.google.com/bigquery/docs/reference/standard-sql/data-definition-language#create_table_statement) statement. * `CREATE_VIEW`: [`CREATE VIEW`](https://cloud.google.com/bigquery/docs/reference/standard-sql/data-definition-language#create_view_statement) statement. * `CREATE_MODEL`: [`CREATE MODEL`](https://cloud.google.com/bigquery-ml/docs/reference/standard-sql/bigqueryml-syntax-create#create_model_statement) statement. * `CREATE_MATERIALIZED_VIEW`: [`CREATE MATERIALIZED VIEW`](https://cloud.google.com/bigquery/docs/reference/standard-sql/data-definition-language#create_materialized_view_statement) statement. * `CREATE_FUNCTION`: [`CREATE FUNCTION`](https://cloud.google.com/bigquery/docs/reference/standard-sql/data-definition-language#create_function_statement) statement. * `CREATE_TABLE_FUNCTION`: [`CREATE TABLE FUNCTION`](https://cloud.google.com/bigquery/docs/reference/standard-sql/data-definition-language#create_table_function_statement) statement. * `CREATE_PROCEDURE`: [`CREATE PROCEDURE`](https://cloud.google.com/bigquery/docs/reference/standard-sql/data-definition-language#create_procedure) statement. * `CREATE_ROW_ACCESS_POLICY`: [`CREATE ROW ACCESS POLICY`](https://cloud.google.com/bigquery/docs/reference/standard-sql/data-definition-language#create_row_access_policy_statement) statement. * `CREATE_SCHEMA`: [`CREATE SCHEMA`](https://cloud.google.com/bigquery/docs/reference/standard-sql/data-definition-language#create_schema_statement) statement. * `CREATE_SNAPSHOT_TABLE`: [`CREATE SNAPSHOT TABLE`](https://cloud.google.com/bigquery/docs/reference/standard-sql/data-definition-language#create_snapshot_table_statement) statement. * `CREATE_SEARCH_INDEX`: [`CREATE SEARCH INDEX`](https://cloud.google.com/bigquery/docs/reference/standard-sql/data-definition-language#create_search_index_statement) statement. * `DROP_TABLE`: [`DROP TABLE`](https://cloud.google.com/bigquery/docs/reference/standard-sql/data-definition-language#drop_table_statement) statement. * `DROP_EXTERNAL_TABLE`: [`DROP EXTERNAL TABLE`](https://cloud.google.com/bigquery/docs/reference/standard-sql/data-definition-language#drop_external_table_statement) statement. * `DROP_VIEW`: [`DROP VIEW`](https://cloud.google.com/bigquery/docs/reference/standard-sql/data-definition-language#drop_view_statement) statement. * `DROP_MODEL`: [`DROP MODEL`](https://cloud.google.com/bigquery-ml/docs/reference/standard-sql/bigqueryml-syntax-drop-model) statement. * `DROP_MATERIALIZED_VIEW`: [`DROP MATERIALIZED VIEW`](https://cloud.google.com/bigquery/docs/reference/standard-sql/data-definition-language#drop_materialized_view_statement) statement. * `DROP_FUNCTION` : [`DROP FUNCTION`](https://cloud.google.com/bigquery/docs/reference/standard-sql/data-definition-language#drop_function_statement) statement. * `DROP_TABLE_FUNCTION` : [`DROP TABLE FUNCTION`](https://cloud.google.com/bigquery/docs/reference/standard-sql/data-definition-language#drop_table_function) statement. * `DROP_PROCEDURE`: [`DROP PROCEDURE`](https://cloud.google.com/bigquery/docs/reference/standard-sql/data-definition-language#drop_procedure_statement) statement. * `DROP_SEARCH_INDEX`: [`DROP SEARCH INDEX`](https://cloud.google.com/bigquery/docs/reference/standard-sql/data-definition-language#drop_search_index) statement. * `DROP_SCHEMA`: [`DROP SCHEMA`](https://cloud.google.com/bigquery/docs/reference/standard-sql/data-definition-language#drop_schema_statement) statement. * `DROP_SNAPSHOT_TABLE`: [`DROP SNAPSHOT TABLE`](https://cloud.google.com/bigquery/docs/reference/standard-sql/data-definition-language#drop_snapshot_table_statement) statement. * `DROP_ROW_ACCESS_POLICY`: [`DROP [ALL] ROW ACCESS POLICY|POLICIES`](https://cloud.google.com/bigquery/docs/reference/standard-sql/data-definition-language#drop_row_access_policy_statement) statement. * `ALTER_TABLE`: [`ALTER TABLE`](https://cloud.google.com/bigquery/docs/reference/standard-sql/data-definition-language#alter_table_set_options_statement) statement. * `ALTER_VIEW`: [`ALTER VIEW`](https://cloud.google.com/bigquery/docs/reference/standard-sql/data-definition-language#alter_view_set_options_statement) statement. * `ALTER_MATERIALIZED_VIEW`: [`ALTER MATERIALIZED VIEW`](https://cloud.google.com/bigquery/docs/reference/standard-sql/data-definition-language#alter_materialized_view_set_options_statement) statement. * `ALTER_SCHEMA`: [`ALTER SCHEMA`](https://cloud.google.com/bigquery/docs/reference/standard-sql/data-definition-language#alter_schema_set_options_statement) statement. * `SCRIPT`: [`SCRIPT`](https://cloud.google.com/bigquery/docs/reference/standard-sql/procedural-language). * `TRUNCATE_TABLE`: [`TRUNCATE TABLE`](https://cloud.google.com/bigquery/docs/reference/standard-sql/dml-syntax#truncate_table_statement) statement. * `CREATE_EXTERNAL_TABLE`: [`CREATE EXTERNAL TABLE`](https://cloud.google.com/bigquery/docs/reference/standard-sql/data-definition-language#create_external_table_statement) statement. * `EXPORT_DATA`: [`EXPORT DATA`](https://cloud.google.com/bigquery/docs/reference/standard-sql/other-statements#export_data_statement) statement. * `EXPORT_MODEL`: [`EXPORT MODEL`](https://cloud.google.com/bigquery-ml/docs/reference/standard-sql/bigqueryml-syntax-export-model) statement. * `LOAD_DATA`: [`LOAD DATA`](https://cloud.google.com/bigquery/docs/reference/standard-sql/other-statements#load_data_statement) statement. * `CALL`: [`CALL`](https://cloud.google.com/bigquery/docs/reference/standard-sql/procedural-language#call) statement.", "readOnly": true, "type": "string"}, "timeline": {"description": "Output only. Describes a timeline of job execution.", "items": {"$ref": "QueryTimelineSample"}, "readOnly": true, "type": "array"}, "totalBytesBilled": {"description": "Output only. If the project is configured to use on-demand pricing, then this field contains the total bytes billed for the job. If the project is configured to use flat-rate pricing, then you are not billed for bytes and this field is informational only.", "format": "int64", "readOnly": true, "type": "string"}, "totalBytesProcessed": {"description": "Output only. Total bytes processed for the job.", "format": "int64", "readOnly": true, "type": "string"}, "totalBytesProcessedAccuracy": {"description": "Output only. For dry-run jobs, totalBytesProcessed is an estimate and this field specifies the accuracy of the estimate. Possible values can be: UNKNOWN: accuracy of the estimate is unknown. PRECISE: estimate is precise. LOWER_BOUND: estimate is lower bound of what the query would cost. UPPER_BOUND: estimate is upper bound of what the query would cost.", "readOnly": true, "type": "string"}, "totalPartitionsProcessed": {"description": "Output only. Total number of partitions processed from all partitioned tables referenced in the job.", "format": "int64", "readOnly": true, "type": "string"}, "totalSlotMs": {"description": "Output only. Slot-milliseconds for the job.", "format": "int64", "readOnly": true, "type": "string"}, "transferredBytes": {"description": "Output only. Total bytes transferred for cross-cloud queries such as Cross Cloud Transfer and CREATE TABLE AS SELECT (CTAS).", "format": "int64", "readOnly": true, "type": "string"}, "undeclaredQueryParameters": {"description": "Output only. GoogleSQL only: list of undeclared query parameters detected during a dry run validation.", "items": {"$ref": "QueryParameter"}, "readOnly": true, "type": "array"}, "vectorSearchStatistics": {"$ref": "VectorSearchStatistics", "description": "Output only. Vector Search query specific statistics.", "readOnly": true}}, "type": "object"}, "JobStatistics3": {"description": "Statistics for a load job.", "id": "JobStatistics3", "properties": {"badRecords": {"description": "Output only. The number of bad records encountered. Note that if the job has failed because of more bad records encountered than the maximum allowed in the load job configuration, then this number can be less than the total number of bad records present in the input data.", "format": "int64", "readOnly": true, "type": "string"}, "inputFileBytes": {"description": "Output only. Number of bytes of source data in a load job.", "format": "int64", "readOnly": true, "type": "string"}, "inputFiles": {"description": "Output only. Number of source files in a load job.", "format": "int64", "readOnly": true, "type": "string"}, "outputBytes": {"description": "Output only. Size of the loaded data in bytes. Note that while a load job is in the running state, this value may change.", "format": "int64", "readOnly": true, "type": "string"}, "outputRows": {"description": "Output only. Number of rows imported in a load job. Note that while an import job is in the running state, this value may change.", "format": "int64", "readOnly": true, "type": "string"}, "timeline": {"description": "Output only. Describes a timeline of job execution.", "items": {"$ref": "QueryTimelineSample"}, "readOnly": true, "type": "array"}}, "type": "object"}, "JobStatistics4": {"description": "Statistics for an extract job.", "id": "JobStatistics4", "properties": {"destinationUriFileCounts": {"description": "Output only. Number of files per destination URI or URI pattern specified in the extract configuration. These values will be in the same order as the URIs specified in the 'destinationUris' field.", "items": {"format": "int64", "type": "string"}, "readOnly": true, "type": "array"}, "inputBytes": {"description": "Output only. Number of user bytes extracted into the result. This is the byte count as computed by BigQuery for billing purposes and doesn't have any relationship with the number of actual result bytes extracted in the desired format.", "format": "int64", "readOnly": true, "type": "string"}, "timeline": {"description": "Output only. Describes a timeline of job execution.", "items": {"$ref": "QueryTimelineSample"}, "readOnly": true, "type": "array"}}, "type": "object"}, "JobStatistics5": {"description": "Statistics for a copy job.", "id": "JobStatistics5", "properties": {"copiedLogicalBytes": {"description": "Output only. Number of logical bytes copied to the destination table.", "format": "int64", "readOnly": true, "type": "string"}, "copiedRows": {"description": "Output only. Number of rows copied to the destination table.", "format": "int64", "readOnly": true, "type": "string"}}, "type": "object"}, "JobStatus": {"id": "JobStatus", "properties": {"errorResult": {"$ref": "<PERSON><PERSON>r<PERSON><PERSON><PERSON>", "description": "Output only. Final error result of the job. If present, indicates that the job has completed and was unsuccessful.", "readOnly": true}, "errors": {"description": "Output only. The first errors encountered during the running of the job. The final message includes the number of errors that caused the process to stop. Errors here do not necessarily mean that the job has not completed or was unsuccessful.", "items": {"$ref": "<PERSON><PERSON>r<PERSON><PERSON><PERSON>"}, "readOnly": true, "type": "array"}, "state": {"description": "Output only. Running state of the job. Valid states include 'PENDING', 'RUNNING', and 'DONE'.", "readOnly": true, "type": "string"}}, "type": "object"}, "JoinRestrictionPolicy": {"description": "Represents privacy policy associated with \"join restrictions\". Join restriction gives data providers the ability to enforce joins on the 'join_allowed_columns' when data is queried from a privacy protected view.", "id": "JoinRestrictionPolicy", "properties": {"joinAllowedColumns": {"description": "Optional. The only columns that joins are allowed on. This field is must be specified for join_conditions JOIN_ANY and JOIN_ALL and it cannot be set for JOIN_BLOCKED.", "items": {"type": "string"}, "type": "array"}, "joinCondition": {"description": "Optional. Specifies if a join is required or not on queries for the view. Default is JOIN_CONDITION_UNSPECIFIED.", "enum": ["JOIN_CONDITION_UNSPECIFIED", "JOIN_ANY", "JOIN_ALL", "JOIN_NOT_REQUIRED", "JOIN_BLOCKED"], "enumDescriptions": ["A join is neither required nor restricted on any column. Default value.", "A join is required on at least one of the specified columns.", "A join is required on all specified columns.", "A join is not required, but if present it is only permitted on 'join_allowed_columns'", "Joins are blocked for all queries."], "type": "string"}}, "type": "object"}, "JsonObject": {"additionalProperties": {"$ref": "JsonValue"}, "description": "Represents a single JSON object.", "id": "JsonObject", "type": "object"}, "JsonOptions": {"description": "Json Options for load and make external tables.", "id": "JsonOptions", "properties": {"encoding": {"description": "Optional. The character encoding of the data. The supported values are UTF-8, UTF-16BE, UTF-16LE, UTF-32BE, and UTF-32LE. The default value is UTF-8.", "type": "string"}}, "type": "object"}, "JsonValue": {"id": "JsonValue", "type": "any"}, "LinkedDatasetMetadata": {"description": "Metadata about the Linked Dataset.", "id": "LinkedDatasetMetadata", "properties": {"linkState": {"description": "Output only. Specifies whether Linked Dataset is currently in a linked state or not.", "enum": ["LINK_STATE_UNSPECIFIED", "LINKED", "UNLINKED"], "enumDescriptions": ["The default value. Default to the LINKED state.", "Normal Linked Dataset state. Data is queryable via the Linked Dataset.", "Data publisher or owner has unlinked this Linked Dataset. It means you can no longer query or see the data in the Linked Dataset."], "readOnly": true, "type": "string"}}, "type": "object"}, "LinkedDatasetSource": {"description": "A dataset source type which refers to another BigQuery dataset.", "id": "LinkedDatasetSource", "properties": {"sourceDataset": {"$ref": "DatasetReference", "description": "The source dataset reference contains project numbers and not project ids."}}, "type": "object"}, "ListModelsResponse": {"description": "Response format for a single page when listing BigQuery ML models.", "id": "ListModelsResponse", "properties": {"models": {"description": "Models in the requested dataset. Only the following fields are populated: model_reference, model_type, creation_time, last_modified_time and labels.", "items": {"$ref": "Model"}, "type": "array"}, "nextPageToken": {"description": "A token to request the next page of results.", "type": "string"}}, "type": "object"}, "ListRoutinesResponse": {"description": "Describes the format of a single result page when listing routines.", "id": "ListRoutinesResponse", "properties": {"nextPageToken": {"description": "A token to request the next page of results.", "type": "string"}, "routines": {"description": "Routines in the requested dataset. Unless read_mask is set in the request, only the following fields are populated: etag, project_id, dataset_id, routine_id, routine_type, creation_time, last_modified_time, language, and remote_function_options.", "items": {"$ref": "Routine"}, "type": "array"}}, "type": "object"}, "ListRowAccessPoliciesResponse": {"description": "Response message for the ListRowAccessPolicies method.", "id": "ListRowAccessPoliciesResponse", "properties": {"nextPageToken": {"description": "A token to request the next page of results.", "type": "string"}, "rowAccessPolicies": {"description": "Row access policies on the requested table.", "items": {"$ref": "RowAccessPolicy"}, "type": "array"}}, "type": "object"}, "LoadQueryStatistics": {"description": "Statistics for a LOAD query.", "id": "LoadQueryStatistics", "properties": {"badRecords": {"description": "Output only. The number of bad records encountered while processing a LOAD query. Note that if the job has failed because of more bad records encountered than the maximum allowed in the load job configuration, then this number can be less than the total number of bad records present in the input data.", "format": "int64", "readOnly": true, "type": "string"}, "bytesTransferred": {"deprecated": true, "description": "Output only. This field is deprecated. The number of bytes of source data copied over the network for a `LOAD` query. `transferred_bytes` has the canonical value for physical transferred bytes, which is used for BigQuery Omni billing.", "format": "int64", "readOnly": true, "type": "string"}, "inputFileBytes": {"description": "Output only. Number of bytes of source data in a LOAD query.", "format": "int64", "readOnly": true, "type": "string"}, "inputFiles": {"description": "Output only. Number of source files in a LOAD query.", "format": "int64", "readOnly": true, "type": "string"}, "outputBytes": {"description": "Output only. Size of the loaded data in bytes. Note that while a LOAD query is in the running state, this value may change.", "format": "int64", "readOnly": true, "type": "string"}, "outputRows": {"description": "Output only. Number of rows imported in a LOAD query. Note that while a LOAD query is in the running state, this value may change.", "format": "int64", "readOnly": true, "type": "string"}}, "type": "object"}, "LocationMetadata": {"description": "BigQuery-specific metadata about a location. This will be set on google.cloud.location.Location.metadata in Cloud Location API responses.", "id": "LocationMetadata", "properties": {"legacyLocationId": {"description": "The legacy BigQuery location ID, e.g. “EU” for the “europe” location. This is for any API consumers that need the legacy “US” and “EU” locations.", "type": "string"}}, "type": "object"}, "MaterializedView": {"description": "A materialized view considered for a query job.", "id": "MaterializedView", "properties": {"chosen": {"description": "Whether the materialized view is chosen for the query. A materialized view can be chosen to rewrite multiple parts of the same query. If a materialized view is chosen to rewrite any part of the query, then this field is true, even if the materialized view was not chosen to rewrite others parts.", "type": "boolean"}, "estimatedBytesSaved": {"description": "If present, specifies a best-effort estimation of the bytes saved by using the materialized view rather than its base tables.", "format": "int64", "type": "string"}, "rejectedReason": {"description": "If present, specifies the reason why the materialized view was not chosen for the query.", "enum": ["REJECTED_REASON_UNSPECIFIED", "NO_DATA", "COST", "BASE_TABLE_TRUNCATED", "BASE_TABLE_DATA_CHANGE", "BASE_TABLE_PARTITION_EXPIRATION_CHANGE", "BASE_TABLE_EXPIRED_PARTITION", "BASE_TABLE_INCOMPATIBLE_METADATA_CHANGE", "TIME_ZONE", "OUT_OF_TIME_TRAVEL_WINDOW", "BASE_TABLE_FINE_GRAINED_SECURITY_POLICY", "BASE_TABLE_TOO_STALE"], "enumDescriptions": ["Default unspecified value.", "View has no cached data because it has not refreshed yet.", "The estimated cost of the view is more expensive than another view or the base table. Note: The estimate cost might not match the billed cost.", "View has no cached data because a base table is truncated.", "View is invalidated because of a data change in one or more base tables. It could be any recent change if the [`maxStaleness`](https://cloud.google.com/bigquery/docs/reference/rest/v2/tables#Table.FIELDS.max_staleness) option is not set for the view, or otherwise any change outside of the staleness window.", "View is invalidated because a base table's partition expiration has changed.", "View is invalidated because a base table's partition has expired.", "View is invalidated because a base table has an incompatible metadata change.", "View is invalidated because it was refreshed with a time zone other than that of the current job.", "View is outside the time travel window.", "View is inaccessible to the user because of a fine-grained security policy on one of its base tables.", "One of the view's base tables is too stale. For example, the cached metadata of a BigLake external table needs to be updated."], "type": "string"}, "tableReference": {"$ref": "TableReference", "description": "The candidate materialized view."}}, "type": "object"}, "MaterializedViewDefinition": {"description": "Definition and configuration of a materialized view.", "id": "MaterializedViewDefinition", "properties": {"allowNonIncrementalDefinition": {"description": "Optional. This option declares the intention to construct a materialized view that isn't refreshed incrementally. Non-incremental materialized views support an expanded range of SQL queries. The `allow_non_incremental_definition` option can't be changed after the materialized view is created.", "type": "boolean"}, "enableRefresh": {"description": "Optional. Enable automatic refresh of the materialized view when the base table is updated. The default value is \"true\".", "type": "boolean"}, "lastRefreshTime": {"description": "Output only. The time when this materialized view was last refreshed, in milliseconds since the epoch.", "format": "int64", "readOnly": true, "type": "string"}, "maxStaleness": {"description": "[Optional] Max staleness of data that could be returned when materizlized view is queried (formatted as Google SQL Interval type).", "format": "byte", "type": "string"}, "query": {"description": "Required. A query whose results are persisted.", "type": "string"}, "refreshIntervalMs": {"description": "Optional. The maximum frequency at which this materialized view will be refreshed. The default value is \"1800000\" (30 minutes).", "format": "int64", "type": "string"}}, "type": "object"}, "MaterializedViewStatistics": {"description": "Statistics of materialized views considered in a query job.", "id": "MaterializedViewStatistics", "properties": {"materializedView": {"description": "Materialized views considered for the query job. Only certain materialized views are used. For a detailed list, see the child message. If many materialized views are considered, then the list might be incomplete.", "items": {"$ref": "MaterializedView"}, "type": "array"}}, "type": "object"}, "MaterializedViewStatus": {"description": "Status of a materialized view. The last refresh timestamp status is omitted here, but is present in the MaterializedViewDefinition message.", "id": "MaterializedViewStatus", "properties": {"lastRefreshStatus": {"$ref": "<PERSON><PERSON>r<PERSON><PERSON><PERSON>", "description": "Output only. Error result of the last automatic refresh. If present, indicates that the last automatic refresh was unsuccessful.", "readOnly": true}, "refreshWatermark": {"description": "Output only. Refresh watermark of materialized view. The base tables' data were collected into the materialized view cache until this time.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "MetadataCacheStatistics": {"description": "Statistics for metadata caching in queried tables.", "id": "MetadataCacheStatistics", "properties": {"tableMetadataCacheUsage": {"description": "Set for the Metadata caching eligible tables referenced in the query.", "items": {"$ref": "TableMetadataCacheUsage"}, "type": "array"}}, "type": "object"}, "MlStatistics": {"description": "Job statistics specific to a BigQuery ML training job.", "id": "MlStatistics", "properties": {"hparamTrials": {"description": "Output only. Trials of a [hyperparameter tuning job](https://cloud.google.com/bigquery-ml/docs/reference/standard-sql/bigqueryml-syntax-hp-tuning-overview) sorted by trial_id.", "items": {"$ref": "HparamTuningTrial"}, "readOnly": true, "type": "array"}, "iterationResults": {"description": "Results for all completed iterations. Empty for [hyperparameter tuning jobs](https://cloud.google.com/bigquery-ml/docs/reference/standard-sql/bigqueryml-syntax-hp-tuning-overview).", "items": {"$ref": "IterationResult"}, "type": "array"}, "maxIterations": {"description": "Output only. Maximum number of iterations specified as max_iterations in the 'CREATE MODEL' query. The actual number of iterations may be less than this number due to early stop.", "format": "int64", "readOnly": true, "type": "string"}, "modelType": {"description": "Output only. The type of the model that is being trained.", "enum": ["MODEL_TYPE_UNSPECIFIED", "LINEAR_REGRESSION", "LOGISTIC_REGRESSION", "KMEANS", "MATRIX_FACTORIZATION", "DNN_CLASSIFIER", "TENSORFLOW", "DNN_REGRESSOR", "XGBOOST", "BOOSTED_TREE_REGRESSOR", "BOOSTED_TREE_CLASSIFIER", "ARIMA", "AUTOML_REGRESSOR", "AUTOML_CLASSIFIER", "PCA", "DNN_LINEAR_COMBINED_CLASSIFIER", "DNN_LINEAR_COMBINED_REGRESSOR", "AUTOENCODER", "ARIMA_PLUS", "ARIMA_PLUS_XREG", "RANDOM_FOREST_REGRESSOR", "RANDOM_FOREST_CLASSIFIER", "TENSORFLOW_LITE", "ONNX", "TRANSFORM_ONLY", "CONTRIBUTION_ANALYSIS"], "enumDescriptions": ["Default value.", "Linear regression model.", "Logistic regression based classification model.", "K-means clustering model.", "Matrix factorization model.", "DNN classifier model.", "An imported TensorFlow model.", "DNN regressor model.", "An imported XGBoost model.", "Boosted tree regressor model.", "Boosted tree classifier model.", "ARIMA model.", "AutoML Tables regression model.", "AutoML Tables classification model.", "Prinpical Component Analysis model.", "Wide-and-deep classifier model.", "Wide-and-deep regressor model.", "Autoencoder model.", "New name for the ARIMA model.", "ARIMA with external regressors.", "Random forest regressor model.", "Random forest classifier model.", "An imported TensorFlow Lite model.", "An imported ONNX model.", "Model to capture the columns and logic in the TRANSFORM clause along with statistics useful for ML analytic functions.", "The contribution analysis model."], "readOnly": true, "type": "string"}, "trainingType": {"description": "Output only. Training type of the job.", "enum": ["TRAINING_TYPE_UNSPECIFIED", "SINGLE_TRAINING", "HPARAM_TUNING"], "enumDescriptions": ["Unspecified training type.", "Single training with fixed parameter space.", "[Hyperparameter tuning training](https://cloud.google.com/bigquery-ml/docs/reference/standard-sql/bigqueryml-syntax-hp-tuning-overview)."], "readOnly": true, "type": "string"}}, "type": "object"}, "Model": {"id": "Model", "properties": {"bestTrialId": {"deprecated": true, "description": "The best trial_id across all training runs.", "format": "int64", "type": "string"}, "creationTime": {"description": "Output only. The time when this model was created, in millisecs since the epoch.", "format": "int64", "readOnly": true, "type": "string"}, "defaultTrialId": {"description": "Output only. The default trial_id to use in TVFs when the trial_id is not passed in. For single-objective [hyperparameter tuning](https://cloud.google.com/bigquery-ml/docs/reference/standard-sql/bigqueryml-syntax-hp-tuning-overview) models, this is the best trial ID. For multi-objective [hyperparameter tuning](https://cloud.google.com/bigquery-ml/docs/reference/standard-sql/bigqueryml-syntax-hp-tuning-overview) models, this is the smallest trial ID among all Pareto optimal trials.", "format": "int64", "readOnly": true, "type": "string"}, "description": {"description": "Optional. A user-friendly description of this model.", "type": "string"}, "encryptionConfiguration": {"$ref": "EncryptionConfiguration", "description": "Custom encryption configuration (e.g., Cloud KMS keys). This shows the encryption configuration of the model data while stored in BigQuery storage. This field can be used with PatchModel to update encryption key for an already encrypted model."}, "etag": {"description": "Output only. A hash of this resource.", "readOnly": true, "type": "string"}, "expirationTime": {"description": "Optional. The time when this model expires, in milliseconds since the epoch. If not present, the model will persist indefinitely. Expired models will be deleted and their storage reclaimed. The defaultTableExpirationMs property of the encapsulating dataset can be used to set a default expirationTime on newly created models.", "format": "int64", "type": "string"}, "featureColumns": {"description": "Output only. Input feature columns for the model inference. If the model is trained with TRANSFORM clause, these are the input of the TRANSFORM clause.", "items": {"$ref": "StandardSqlField"}, "readOnly": true, "type": "array"}, "friendlyName": {"description": "Optional. A descriptive name for this model.", "type": "string"}, "hparamSearchSpaces": {"$ref": "HparamSearchSpaces", "description": "Output only. All hyperparameter search spaces in this model.", "readOnly": true}, "hparamTrials": {"description": "Output only. Trials of a [hyperparameter tuning](https://cloud.google.com/bigquery-ml/docs/reference/standard-sql/bigqueryml-syntax-hp-tuning-overview) model sorted by trial_id.", "items": {"$ref": "HparamTuningTrial"}, "readOnly": true, "type": "array"}, "labelColumns": {"description": "Output only. Label columns that were used to train this model. The output of the model will have a \"predicted_\" prefix to these columns.", "items": {"$ref": "StandardSqlField"}, "readOnly": true, "type": "array"}, "labels": {"additionalProperties": {"type": "string"}, "description": "The labels associated with this model. You can use these to organize and group your models. Label keys and values can be no longer than 63 characters, can only contain lowercase letters, numeric characters, underscores and dashes. International characters are allowed. Label values are optional. Label keys must start with a letter and each label in the list must have a different key.", "type": "object"}, "lastModifiedTime": {"description": "Output only. The time when this model was last modified, in millisecs since the epoch.", "format": "int64", "readOnly": true, "type": "string"}, "location": {"description": "Output only. The geographic location where the model resides. This value is inherited from the dataset.", "readOnly": true, "type": "string"}, "modelReference": {"$ref": "ModelReference", "description": "Required. Unique identifier for this model."}, "modelType": {"description": "Output only. Type of the model resource.", "enum": ["MODEL_TYPE_UNSPECIFIED", "LINEAR_REGRESSION", "LOGISTIC_REGRESSION", "KMEANS", "MATRIX_FACTORIZATION", "DNN_CLASSIFIER", "TENSORFLOW", "DNN_REGRESSOR", "XGBOOST", "BOOSTED_TREE_REGRESSOR", "BOOSTED_TREE_CLASSIFIER", "ARIMA", "AUTOML_REGRESSOR", "AUTOML_CLASSIFIER", "PCA", "DNN_LINEAR_COMBINED_CLASSIFIER", "DNN_LINEAR_COMBINED_REGRESSOR", "AUTOENCODER", "ARIMA_PLUS", "ARIMA_PLUS_XREG", "RANDOM_FOREST_REGRESSOR", "RANDOM_FOREST_CLASSIFIER", "TENSORFLOW_LITE", "ONNX", "TRANSFORM_ONLY", "CONTRIBUTION_ANALYSIS"], "enumDescriptions": ["Default value.", "Linear regression model.", "Logistic regression based classification model.", "K-means clustering model.", "Matrix factorization model.", "DNN classifier model.", "An imported TensorFlow model.", "DNN regressor model.", "An imported XGBoost model.", "Boosted tree regressor model.", "Boosted tree classifier model.", "ARIMA model.", "AutoML Tables regression model.", "AutoML Tables classification model.", "Prinpical Component Analysis model.", "Wide-and-deep classifier model.", "Wide-and-deep regressor model.", "Autoencoder model.", "New name for the ARIMA model.", "ARIMA with external regressors.", "Random forest regressor model.", "Random forest classifier model.", "An imported TensorFlow Lite model.", "An imported ONNX model.", "Model to capture the columns and logic in the TRANSFORM clause along with statistics useful for ML analytic functions.", "The contribution analysis model."], "readOnly": true, "type": "string"}, "optimalTrialIds": {"description": "Output only. For single-objective [hyperparameter tuning](https://cloud.google.com/bigquery-ml/docs/reference/standard-sql/bigqueryml-syntax-hp-tuning-overview) models, it only contains the best trial. For multi-objective [hyperparameter tuning](https://cloud.google.com/bigquery-ml/docs/reference/standard-sql/bigqueryml-syntax-hp-tuning-overview) models, it contains all Pareto optimal trials sorted by trial_id.", "items": {"format": "int64", "type": "string"}, "readOnly": true, "type": "array"}, "remoteModelInfo": {"$ref": "RemoteModelInfo", "description": "Output only. Remote model info", "readOnly": true}, "trainingRuns": {"description": "Information for all training runs in increasing order of start_time.", "items": {"$ref": "TrainingRun"}, "type": "array"}, "transformColumns": {"description": "Output only. This field will be populated if a TRANSFORM clause was used to train a model. TRANSFORM clause (if used) takes feature_columns as input and outputs transform_columns. transform_columns then are used to train the model.", "items": {"$ref": "TransformColumn"}, "readOnly": true, "type": "array"}}, "type": "object"}, "ModelDefinition": {"id": "ModelDefinition", "properties": {"modelOptions": {"description": "Deprecated.", "properties": {"labels": {"items": {"type": "string"}, "type": "array"}, "lossType": {"type": "string"}, "modelType": {"type": "string"}}, "type": "object"}, "trainingRuns": {"description": "Deprecated.", "items": {"$ref": "BqmlTrainingRun"}, "type": "array"}}, "type": "object"}, "ModelExtractOptions": {"description": "Options related to model extraction.", "id": "ModelExtractOptions", "properties": {"trialId": {"description": "The 1-based ID of the trial to be exported from a hyperparameter tuning model. If not specified, the trial with id = [Model](https://cloud.google.com/bigquery/docs/reference/rest/v2/models#resource:-model).defaultTrialId is exported. This field is ignored for models not trained with hyperparameter tuning.", "format": "int64", "type": "string"}}, "type": "object"}, "ModelReference": {"description": "Id path of a model.", "id": "ModelReference", "properties": {"datasetId": {"description": "Required. The ID of the dataset containing this model.", "type": "string"}, "modelId": {"description": "Required. The ID of the model. The ID must contain only letters (a-z, A-Z), numbers (0-9), or underscores (_). The maximum length is 1,024 characters.", "type": "string"}, "projectId": {"description": "Required. The ID of the project containing this model.", "type": "string"}}, "type": "object"}, "MultiClassClassificationMetrics": {"description": "Evaluation metrics for multi-class classification/classifier models.", "id": "MultiClassClassificationMetrics", "properties": {"aggregateClassificationMetrics": {"$ref": "AggregateClassificationMetrics", "description": "Aggregate classification metrics."}, "confusionMatrixList": {"description": "Confusion matrix at different thresholds.", "items": {"$ref": "ConfusionMatrix"}, "type": "array"}}, "type": "object"}, "ParquetOptions": {"description": "Parquet Options for load and make external tables.", "id": "ParquetOptions", "properties": {"enableListInference": {"description": "Optional. Indicates whether to use schema inference specifically for Parquet LIST logical type.", "type": "boolean"}, "enumAsString": {"description": "Optional. Indicates whether to infer Parquet ENUM logical type as STRING instead of BYTES by default.", "type": "boolean"}, "mapTargetType": {"description": "Optional. Indicates how to represent a Parquet map if present.", "enum": ["MAP_TARGET_TYPE_UNSPECIFIED", "ARRAY_OF_STRUCT"], "enumDescriptions": ["In this mode, the map will have the following schema: struct map_field_name { repeated struct key_value { key value } }.", "In this mode, the map will have the following schema: repeated struct map_field_name { key value }."], "type": "string"}}, "type": "object"}, "PartitionSkew": {"description": "Partition skew detailed information.", "id": "PartitionSkew", "properties": {"skewSources": {"description": "Output only. Source stages which produce skewed data.", "items": {"$ref": "SkewSource"}, "readOnly": true, "type": "array"}}, "type": "object"}, "PartitionedColumn": {"description": "The partitioning column information.", "id": "PartitionedColumn", "properties": {"field": {"description": "Required. The name of the partition column.", "type": "string"}}, "type": "object"}, "PartitioningDefinition": {"description": "The partitioning information, which includes managed table, external table and metastore partitioned table partition information.", "id": "PartitioningDefinition", "properties": {"partitionedColumn": {"description": "Optional. Details about each partitioning column. This field is output only for all partitioning types other than metastore partitioned tables. BigQuery native tables only support 1 partitioning column. Other table types may support 0, 1 or more partitioning columns. For metastore partitioned tables, the order must match the definition order in the Hive Metastore, where it must match the physical layout of the table. For example, CREATE TABLE a_table(id BIGINT, name STRING) PARTITIONED BY (city STRING, state STRING). In this case the values must be ['city', 'state'] in that order.", "items": {"$ref": "PartitionedColumn"}, "type": "array"}}, "type": "object"}, "PerformanceInsights": {"description": "Performance insights for the job.", "id": "PerformanceInsights", "properties": {"avgPreviousExecutionMs": {"description": "Output only. Average execution ms of previous runs. Indicates the job ran slow compared to previous executions. To find previous executions, use INFORMATION_SCHEMA tables and filter jobs with same query hash.", "format": "int64", "readOnly": true, "type": "string"}, "stagePerformanceChangeInsights": {"description": "Output only. Query stage performance insights compared to previous runs, for diagnosing performance regression.", "items": {"$ref": "StagePerformanceChangeInsight"}, "readOnly": true, "type": "array"}, "stagePerformanceStandaloneInsights": {"description": "Output only. Standalone query stage performance insights, for exploring potential improvements.", "items": {"$ref": "StagePerformanceStandaloneInsight"}, "readOnly": true, "type": "array"}}, "type": "object"}, "Policy": {"description": "An Identity and Access Management (IAM) policy, which specifies access controls for Google Cloud resources. A `Policy` is a collection of `bindings`. A `binding` binds one or more `members`, or principals, to a single `role`. Principals can be user accounts, service accounts, Google groups, and domains (such as G Suite). A `role` is a named list of permissions; each `role` can be an IAM predefined role or a user-created custom role. For some types of Google Cloud resources, a `binding` can also specify a `condition`, which is a logical expression that allows access to a resource only if the expression evaluates to `true`. A condition can add constraints based on attributes of the request, the resource, or both. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies). **JSON example:** ``` { \"bindings\": [ { \"role\": \"roles/resourcemanager.organizationAdmin\", \"members\": [ \"user:<EMAIL>\", \"group:<EMAIL>\", \"domain:google.com\", \"serviceAccount:<EMAIL>\" ] }, { \"role\": \"roles/resourcemanager.organizationViewer\", \"members\": [ \"user:<EMAIL>\" ], \"condition\": { \"title\": \"expirable access\", \"description\": \"Does not grant access after Sep 2020\", \"expression\": \"request.time < timestamp('2020-10-01T00:00:00.000Z')\", } } ], \"etag\": \"BwWWja0YfJA=\", \"version\": 3 } ``` **YAML example:** ``` bindings: - members: - user:<EMAIL> - group:<EMAIL> - domain:google.com - serviceAccount:<EMAIL> role: roles/resourcemanager.organizationAdmin - members: - user:<EMAIL> role: roles/resourcemanager.organizationViewer condition: title: expirable access description: Does not grant access after Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z') etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features, see the [IAM documentation](https://cloud.google.com/iam/docs/).", "id": "Policy", "properties": {"auditConfigs": {"description": "Specifies cloud audit logging configuration for this policy.", "items": {"$ref": "AuditConfig"}, "type": "array"}, "bindings": {"description": "Associates a list of `members`, or principals, with a `role`. Optionally, may specify a `condition` that determines how and when the `bindings` are applied. Each of the `bindings` must contain at least one principal. The `bindings` in a `Policy` can refer to up to 1,500 principals; up to 250 of these principals can be Google groups. Each occurrence of a principal counts towards these limits. For example, if the `bindings` grant 50 different roles to `user:<EMAIL>`, and not to any other principal, then you can add another 1,450 principals to the `bindings` in the `Policy`.", "items": {"$ref": "Binding"}, "type": "array"}, "etag": {"description": "`etag` is used for optimistic concurrency control as a way to help prevent simultaneous updates of a policy from overwriting each other. It is strongly suggested that systems make use of the `etag` in the read-modify-write cycle to perform policy updates in order to avoid race conditions: An `etag` is returned in the response to `getIamPolicy`, and systems are expected to put that etag in the request to `setIamPolicy` to ensure that their change will be applied to the same version of the policy. **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost.", "format": "byte", "type": "string"}, "version": {"description": "Specifies the format of the policy. Valid values are `0`, `1`, and `3`. Requests that specify an invalid value are rejected. Any operation that affects conditional role bindings must specify version `3`. This requirement applies to the following operations: * Getting a policy that includes a conditional role binding * Adding a conditional role binding to a policy * Changing a conditional role binding in a policy * Removing any role binding, with or without a condition, from a policy that includes conditions **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost. If a policy does not include any conditions, operations on that policy may specify any valid version or leave the field unset. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "type": "integer"}}, "type": "object"}, "PrincipalComponentInfo": {"description": "Principal component infos, used only for eigen decomposition based models, e.g., PCA. Ordered by explained_variance in the descending order.", "id": "PrincipalComponentInfo", "properties": {"cumulativeExplainedVarianceRatio": {"description": "The explained_variance is pre-ordered in the descending order to compute the cumulative explained variance ratio.", "format": "double", "type": "number"}, "explainedVariance": {"description": "Explained variance by this principal component, which is simply the eigenvalue.", "format": "double", "type": "number"}, "explainedVarianceRatio": {"description": "Explained_variance over the total explained variance.", "format": "double", "type": "number"}, "principalComponentId": {"description": "Id of the principal component.", "format": "int64", "type": "string"}}, "type": "object"}, "PrivacyPolicy": {"description": "Represents privacy policy that contains the privacy requirements specified by the data owner. Currently, this is only supported on views.", "id": "PrivacyPolicy", "properties": {"aggregationThresholdPolicy": {"$ref": "AggregationThresholdPolicy", "description": "Optional. Policy used for aggregation thresholds."}, "differentialPrivacyPolicy": {"$ref": "DifferentialPrivacyPolicy", "description": "Optional. Policy used for differential privacy."}, "joinRestrictionPolicy": {"$ref": "JoinRestrictionPolicy", "description": "Optional. Join restriction policy is outside of the one of policies, since this policy can be set along with other policies. This policy gives data providers the ability to enforce joins on the 'join_allowed_columns' when data is queried from a privacy protected view."}}, "type": "object"}, "ProjectList": {"description": "Response object of ListProjects", "id": "ProjectList", "properties": {"etag": {"description": "A hash of the page of results.", "type": "string"}, "kind": {"default": "bigquery#projectList", "description": "The resource type of the response.", "type": "string"}, "nextPageToken": {"description": "Use this token to request the next page of results.", "type": "string"}, "projects": {"description": "Projects to which the user has at least READ access.", "items": {"description": "Information about a single project.", "properties": {"friendlyName": {"description": "A descriptive name for this project. A wrapper is used here because friendlyName can be set to the empty string.", "type": "string"}, "id": {"description": "An opaque ID of this project.", "type": "string"}, "kind": {"description": "The resource type.", "type": "string"}, "numericId": {"description": "The numeric ID of this project.", "format": "uint64", "type": "string"}, "projectReference": {"$ref": "ProjectReference", "description": "A unique reference to this project."}}, "type": "object"}, "type": "array"}, "totalItems": {"description": "The total number of projects in the page. A wrapper is used here because the field should still be in the response when the value is 0.", "format": "int32", "type": "integer"}}, "type": "object"}, "ProjectReference": {"description": "A unique reference to a project.", "id": "ProjectReference", "properties": {"projectId": {"description": "Required. ID of the project. Can be either the numeric ID or the assigned ID of the project.", "type": "string"}}, "type": "object"}, "QueryInfo": {"description": "Query optimization information for a QUERY job.", "id": "QueryInfo", "properties": {"optimizationDetails": {"additionalProperties": {"description": "Properties of the object.", "type": "any"}, "description": "Output only. Information about query optimizations.", "readOnly": true, "type": "object"}}, "type": "object"}, "QueryParameter": {"description": "A parameter given to a query.", "id": "QueryParameter", "properties": {"name": {"description": "Optional. If unset, this is a positional parameter. Otherwise, should be unique within a query.", "type": "string"}, "parameterType": {"$ref": "QueryParameterType", "description": "Required. The type of this parameter."}, "parameterValue": {"$ref": "QueryParameterValue", "description": "Required. The value of this parameter."}}, "type": "object"}, "QueryParameterType": {"description": "The type of a query parameter.", "id": "QueryParameterType", "properties": {"arrayType": {"$ref": "QueryParameterType", "description": "Optional. The type of the array's elements, if this is an array."}, "rangeElementType": {"$ref": "QueryParameterType", "description": "Optional. The element type of the range, if this is a range."}, "structTypes": {"description": "Optional. The types of the fields of this struct, in order, if this is a struct.", "items": {"description": "The type of a struct parameter.", "properties": {"description": {"description": "Optional. Human-oriented description of the field.", "type": "string"}, "name": {"description": "Optional. The name of this field.", "type": "string"}, "type": {"$ref": "QueryParameterType", "description": "Required. The type of this field."}}, "type": "object"}, "type": "array"}, "type": {"description": "Required. The top level type of this field.", "type": "string"}}, "type": "object"}, "QueryParameterValue": {"description": "The value of a query parameter.", "id": "QueryParameterValue", "properties": {"arrayValues": {"description": "Optional. The array values, if this is an array type.", "items": {"$ref": "QueryParameterValue"}, "type": "array"}, "rangeValue": {"$ref": "RangeValue", "description": "Optional. The range value, if this is a range type."}, "structValues": {"additionalProperties": {"$ref": "QueryParameterValue"}, "description": "The struct field values.", "type": "object"}, "value": {"description": "Optional. The value of this value, if a simple scalar type.", "type": "string"}}, "type": "object"}, "QueryRequest": {"description": "Describes the format of the jobs.query request.", "id": "QueryRequest", "properties": {"connectionProperties": {"description": "Optional. Connection properties which can modify the query behavior.", "items": {"$ref": "ConnectionProperty"}, "type": "array"}, "continuous": {"description": "[Optional] Specifies whether the query should be executed as a continuous query. The default value is false.", "type": "boolean"}, "createSession": {"description": "Optional. If true, creates a new session using a randomly generated session_id. If false, runs query with an existing session_id passed in ConnectionProperty, otherwise runs query in non-session mode. The session location will be set to QueryRequest.location if it is present, otherwise it's set to the default location based on existing routing logic.", "type": "boolean"}, "defaultDataset": {"$ref": "DatasetReference", "description": "Optional. Specifies the default datasetId and projectId to assume for any unqualified table names in the query. If not set, all table names in the query string must be qualified in the format 'datasetId.tableId'."}, "destinationEncryptionConfiguration": {"$ref": "EncryptionConfiguration", "description": "Optional. Custom encryption configuration (e.g., Cloud KMS keys)"}, "dryRun": {"description": "Optional. If set to true, <PERSON>Query doesn't run the job. Instead, if the query is valid, BigQuery returns statistics about the job such as how many bytes would be processed. If the query is invalid, an error returns. The default value is false.", "type": "boolean"}, "formatOptions": {"$ref": "DataFormatOptions", "description": "Optional. Output format adjustments."}, "jobCreationMode": {"description": "Optional. If not set, jobs are always required. If set, the query request will follow the behavior described JobCreationMode. [Preview](https://cloud.google.com/products/#product-launch-stages)", "enum": ["JOB_CREATION_MODE_UNSPECIFIED", "JOB_CREATION_REQUIRED", "JOB_CREATION_OPTIONAL"], "enumDescriptions": ["If unspecified JOB_CREATION_REQUIRED is the default.", "Default. Job creation is always required.", "Job creation is optional. Returning immediate results is prioritized. BigQuery will automatically determine if a Job needs to be created. The conditions under which BigQuery can decide to not create a Job are subject to change. If Job creation is required, JOB_CREATION_REQUIRED mode should be used, which is the default."], "type": "string"}, "jobTimeoutMs": {"description": "Optional. Job timeout in milliseconds. If this time limit is exceeded, <PERSON><PERSON><PERSON><PERSON> will attempt to stop a longer job, but may not always succeed in canceling it before the job completes. For example, a job that takes more than 60 seconds to complete has a better chance of being stopped than a job that takes 10 seconds to complete. This timeout applies to the query even if a job does not need to be created.", "format": "int64", "type": "string"}, "kind": {"default": "bigquery#queryRequest", "description": "The resource type of the request.", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. The labels associated with this query. Labels can be used to organize and group query jobs. Label keys and values can be no longer than 63 characters, can only contain lowercase letters, numeric characters, underscores and dashes. International characters are allowed. Label keys must start with a letter and each label in the list must have a different key.", "type": "object"}, "location": {"description": "The geographic location where the job should run. For more information, see how to [specify locations](https://cloud.google.com/bigquery/docs/locations#specify_locations).", "type": "string"}, "maxResults": {"description": "Optional. The maximum number of rows of data to return per page of results. Setting this flag to a small value such as 1000 and then paging through results might improve reliability when the query result set is large. In addition to this limit, responses are also limited to 10 MB. By default, there is no maximum row count, and only the byte limit applies.", "format": "uint32", "type": "integer"}, "maximumBytesBilled": {"description": "Optional. Limits the bytes billed for this query. Queries with bytes billed above this limit will fail (without incurring a charge). If unspecified, the project default is used.", "format": "int64", "type": "string"}, "parameterMode": {"description": "GoogleSQL only. Set to POSITIONAL to use positional (?) query parameters or to NAMED to use named (@myparam) query parameters in this query.", "type": "string"}, "preserveNulls": {"deprecated": true, "description": "This property is deprecated.", "type": "boolean"}, "query": {"description": "Required. A query string to execute, using Google Standard SQL or legacy SQL syntax. Example: \"SELECT COUNT(f1) FROM myProjectId.myDatasetId.myTableId\".", "type": "string"}, "queryParameters": {"description": "Query parameters for GoogleSQL queries.", "items": {"$ref": "QueryParameter"}, "type": "array"}, "requestId": {"description": "Optional. A unique user provided identifier to ensure idempotent behavior for queries. Note that this is different from the job_id. It has the following properties: 1. It is case-sensitive, limited to up to 36 ASCII characters. A UUID is recommended. 2. Read only queries can ignore this token since they are nullipotent by definition. 3. For the purposes of idempotency ensured by the request_id, a request is considered duplicate of another only if they have the same request_id and are actually duplicates. When determining whether a request is a duplicate of another request, all parameters in the request that may affect the result are considered. For example, query, connection_properties, query_parameters, use_legacy_sql are parameters that affect the result and are considered when determining whether a request is a duplicate, but properties like timeout_ms don't affect the result and are thus not considered. Dry run query requests are never considered duplicate of another request. 4. When a duplicate mutating query request is detected, it returns: a. the results of the mutation if it completes successfully within the timeout. b. the running operation if it is still in progress at the end of the timeout. 5. Its lifetime is limited to 15 minutes. In other words, if two requests are sent with the same request_id, but more than 15 minutes apart, idempotency is not guaranteed.", "type": "string"}, "reservation": {"description": "Optional. The reservation that jobs.query request would use. User can specify a reservation to execute the job.query. The expected format is `projects/{project}/locations/{location}/reservations/{reservation}`.", "type": "string"}, "timeoutMs": {"description": "Optional. Optional: Specifies the maximum amount of time, in milliseconds, that the client is willing to wait for the query to complete. By default, this limit is 10 seconds (10,000 milliseconds). If the query is complete, the jobComplete field in the response is true. If the query has not yet completed, jobComplete is false. You can request a longer timeout period in the timeoutMs field. However, the call is not guaranteed to wait for the specified timeout; it typically returns after around 200 seconds (200,000 milliseconds), even if the query is not complete. If jobComplete is false, you can continue to wait for the query to complete by calling the getQueryResults method until the jobComplete field in the getQueryResults response is true.", "format": "uint32", "type": "integer"}, "useLegacySql": {"default": "true", "description": "Specifies whether to use BigQuery's legacy SQL dialect for this query. The default value is true. If set to false, the query will use BigQuery's GoogleSQL: https://cloud.google.com/bigquery/sql-reference/ When useLegacySql is set to false, the value of flattenResults is ignored; query will be run as if flattenResults is false.", "type": "boolean"}, "useQueryCache": {"default": "true", "description": "Optional. Whether to look for the result in the query cache. The query cache is a best-effort cache that will be flushed whenever tables in the query are modified. The default value is true.", "type": "boolean"}, "writeIncrementalResults": {"description": "Optional. This is only supported for SELECT query. If set, the query is allowed to write results incrementally to the temporary result table. This may incur a performance penalty. This option cannot be used with Legacy SQL. This feature is not yet available.", "type": "boolean"}}, "type": "object"}, "QueryResponse": {"id": "QueryResponse", "properties": {"cacheHit": {"description": "Whether the query result was fetched from the query cache.", "type": "boolean"}, "creationTime": {"description": "Output only. Creation time of this query, in milliseconds since the epoch. This field will be present on all queries.", "format": "int64", "readOnly": true, "type": "string"}, "dmlStats": {"$ref": "DmlStatistics", "description": "Output only. Detailed statistics for DML statements INSERT, UPDATE, DELETE, MERGE or TRUNCATE.", "readOnly": true}, "endTime": {"description": "Output only. End time of this query, in milliseconds since the epoch. This field will be present whenever a query job is in the DONE state.", "format": "int64", "readOnly": true, "type": "string"}, "errors": {"description": "Output only. The first errors or warnings encountered during the running of the job. The final message includes the number of errors that caused the process to stop. Errors here do not necessarily mean that the job has completed or was unsuccessful. For more information about error messages, see [Error messages](https://cloud.google.com/bigquery/docs/error-messages).", "items": {"$ref": "<PERSON><PERSON>r<PERSON><PERSON><PERSON>"}, "readOnly": true, "type": "array"}, "jobComplete": {"description": "Whether the query has completed or not. If rows or totalRows are present, this will always be true. If this is false, totalRows will not be available.", "type": "boolean"}, "jobCreationReason": {"$ref": "JobCreationReason", "description": "Optional. The reason why a Job was created. Only relevant when a job_reference is present in the response. If job_reference is not present it will always be unset. [Preview](https://cloud.google.com/products/#product-launch-stages)"}, "jobReference": {"$ref": "JobReference", "description": "Reference to the Job that was created to run the query. This field will be present even if the original request timed out, in which case GetQueryResults can be used to read the results once the query has completed. Since this API only returns the first page of results, subsequent pages can be fetched via the same mechanism (GetQueryResults). If job_creation_mode was set to `JOB_CREATION_OPTIONAL` and the query completes without creating a job, this field will be empty."}, "kind": {"default": "bigquery#queryResponse", "description": "The resource type.", "type": "string"}, "location": {"description": "Output only. The geographic location of the query. For more information about BigQuery locations, see: https://cloud.google.com/bigquery/docs/locations", "readOnly": true, "type": "string"}, "numDmlAffectedRows": {"description": "Output only. The number of rows affected by a DML statement. Present only for DML statements INSERT, UPDATE or DELETE.", "format": "int64", "readOnly": true, "type": "string"}, "pageToken": {"description": "A token used for paging results. A non-empty token indicates that additional results are available. To see additional results, query the [`jobs.getQueryResults`](https://cloud.google.com/bigquery/docs/reference/rest/v2/jobs/getQueryResults) method. For more information, see [Paging through table data](https://cloud.google.com/bigquery/docs/paging-results).", "type": "string"}, "queryId": {"description": "Auto-generated ID for the query. [Preview](https://cloud.google.com/products/#product-launch-stages)", "type": "string"}, "rows": {"description": "An object with as many results as can be contained within the maximum permitted reply size. To get any additional rows, you can call GetQueryResults and specify the jobReference returned above.", "items": {"$ref": "TableRow"}, "type": "array"}, "schema": {"$ref": "TableSchema", "description": "The schema of the results. Present only when the query completes successfully."}, "sessionInfo": {"$ref": "SessionInfo", "description": "Output only. Information of the session if this job is part of one.", "readOnly": true}, "startTime": {"description": "Output only. Start time of this query, in milliseconds since the epoch. This field will be present when the query job transitions from the PENDING state to either RUNNING or DONE.", "format": "int64", "readOnly": true, "type": "string"}, "totalBytesBilled": {"description": "Output only. If the project is configured to use on-demand pricing, then this field contains the total bytes billed for the job. If the project is configured to use flat-rate pricing, then you are not billed for bytes and this field is informational only.", "format": "int64", "readOnly": true, "type": "string"}, "totalBytesProcessed": {"description": "The total number of bytes processed for this query. If this query was a dry run, this is the number of bytes that would be processed if the query were run.", "format": "int64", "type": "string"}, "totalRows": {"description": "The total number of rows in the complete query result set, which can be more than the number of rows in this single page of results.", "format": "uint64", "type": "string"}, "totalSlotMs": {"description": "Output only. Number of slot ms the user is actually billed for.", "format": "int64", "readOnly": true, "type": "string"}}, "type": "object"}, "QueryTimelineSample": {"description": "Summary of the state of query execution at a given time.", "id": "QueryTimelineSample", "properties": {"activeUnits": {"description": "Total number of active workers. This does not correspond directly to slot usage. This is the largest value observed since the last sample.", "format": "int64", "type": "string"}, "completedUnits": {"description": "Total parallel units of work completed by this query.", "format": "int64", "type": "string"}, "elapsedMs": {"description": "Milliseconds elapsed since the start of query execution.", "format": "int64", "type": "string"}, "estimatedRunnableUnits": {"description": "Units of work that can be scheduled immediately. Providing additional slots for these units of work will accelerate the query, if no other query in the reservation needs additional slots.", "format": "int64", "type": "string"}, "pendingUnits": {"description": "Total units of work remaining for the query. This number can be revised (increased or decreased) while the query is running.", "format": "int64", "type": "string"}, "shuffleRamUsageRatio": {"description": "Total shuffle usage ratio in shuffle RAM per reservation of this query. This will be provided for reservation customers only.", "format": "double", "type": "number"}, "totalSlotMs": {"description": "Cumulative slot-ms consumed by the query.", "format": "int64", "type": "string"}}, "type": "object"}, "RangePartitioning": {"id": "RangePartitioning", "properties": {"field": {"description": "Required. The name of the column to partition the table on. It must be a top-level, INT64 column whose mode is NULLABLE or REQUIRED.", "type": "string"}, "range": {"description": "[Experimental] Defines the ranges for range partitioning.", "properties": {"end": {"description": "[Experimental] The end of range partitioning, exclusive.", "format": "int64", "type": "string"}, "interval": {"description": "[Experimental] The width of each interval.", "format": "int64", "type": "string"}, "start": {"description": "[Experimental] The start of range partitioning, inclusive.", "format": "int64", "type": "string"}}, "type": "object"}}, "type": "object"}, "RangeValue": {"description": "Represents the value of a range.", "id": "RangeValue", "properties": {"end": {"$ref": "QueryParameterValue", "description": "Optional. The end value of the range. A missing value represents an unbounded end."}, "start": {"$ref": "QueryParameterValue", "description": "Optional. The start value of the range. A missing value represents an unbounded start."}}, "type": "object"}, "RankingMetrics": {"description": "Evaluation metrics used by weighted-ALS models specified by feedback_type=implicit.", "id": "RankingMetrics", "properties": {"averageRank": {"description": "Determines the goodness of a ranking by computing the percentile rank from the predicted confidence and dividing it by the original rank.", "format": "double", "type": "number"}, "meanAveragePrecision": {"description": "Calculates a precision per user for all the items by ranking them and then averages all the precisions across all the users.", "format": "double", "type": "number"}, "meanSquaredError": {"description": "Similar to the mean squared error computed in regression and explicit recommendation models except instead of computing the rating directly, the output from evaluate is computed against a preference which is 1 or 0 depending on if the rating exists or not.", "format": "double", "type": "number"}, "normalizedDiscountedCumulativeGain": {"description": "A metric to determine the goodness of a ranking calculated from the predicted confidence by comparing it to an ideal rank measured by the original ratings.", "format": "double", "type": "number"}}, "type": "object"}, "RegressionMetrics": {"description": "Evaluation metrics for regression and explicit feedback type matrix factorization models.", "id": "RegressionMetrics", "properties": {"meanAbsoluteError": {"description": "Mean absolute error.", "format": "double", "type": "number"}, "meanSquaredError": {"description": "Mean squared error.", "format": "double", "type": "number"}, "meanSquaredLogError": {"description": "Mean squared log error.", "format": "double", "type": "number"}, "medianAbsoluteError": {"description": "Median absolute error.", "format": "double", "type": "number"}, "rSquared": {"description": "R^2 score. This corresponds to r2_score in ML.EVALUATE.", "format": "double", "type": "number"}}, "type": "object"}, "RemoteFunctionOptions": {"description": "Options for a remote user-defined function.", "id": "RemoteFunctionOptions", "properties": {"connection": {"description": "Fully qualified name of the user-provided connection object which holds the authentication information to send requests to the remote service. Format: ```\"projects/{projectId}/locations/{locationId}/connections/{connectionId}\"```", "type": "string"}, "endpoint": {"description": "Endpoint of the user-provided remote service, e.g. ```https://us-east1-my_gcf_project.cloudfunctions.net/remote_add```", "type": "string"}, "maxBatchingRows": {"description": "Max number of rows in each batch sent to the remote service. If absent or if 0, BigQuery dynamically decides the number of rows in a batch.", "format": "int64", "type": "string"}, "userDefinedContext": {"additionalProperties": {"type": "string"}, "description": "User-defined context as a set of key/value pairs, which will be sent as function invocation context together with batched arguments in the requests to the remote service. The total number of bytes of keys and values must be less than 8KB.", "type": "object"}}, "type": "object"}, "RemoteModelInfo": {"description": "Remote Model Info", "id": "RemoteModelInfo", "properties": {"connection": {"description": "Output only. Fully qualified name of the user-provided connection object of the remote model. Format: ```\"projects/{project_id}/locations/{location_id}/connections/{connection_id}\"```", "readOnly": true, "type": "string"}, "endpoint": {"description": "Output only. The endpoint for remote model.", "readOnly": true, "type": "string"}, "maxBatchingRows": {"description": "Output only. Max number of rows in each batch sent to the remote service. If unset, the number of rows in each batch is set dynamically.", "format": "int64", "readOnly": true, "type": "string"}, "remoteModelVersion": {"description": "Output only. The model version for LLM.", "readOnly": true, "type": "string"}, "remoteServiceType": {"description": "Output only. The remote service type for remote model.", "enum": ["REMOTE_SERVICE_TYPE_UNSPECIFIED", "CLOUD_AI_TRANSLATE_V3", "CLOUD_AI_VISION_V1", "CLOUD_AI_NATURAL_LANGUAGE_V1", "CLOUD_AI_SPEECH_TO_TEXT_V2"], "enumDescriptions": ["Unspecified remote service type.", "V3 Cloud AI Translation API. See more details at [Cloud Translation API] (https://cloud.google.com/translate/docs/reference/rest).", "V1 Cloud AI Vision API See more details at [Cloud Vision API] (https://cloud.google.com/vision/docs/reference/rest).", "V1 Cloud AI Natural Language API. See more details at [REST Resource: documents](https://cloud.google.com/natural-language/docs/reference/rest/v1/documents).", "V2 Speech-to-Text API. See more details at [Google Cloud Speech-to-Text V2 API](https://cloud.google.com/speech-to-text/v2/docs)"], "readOnly": true, "type": "string"}, "speechRecognizer": {"description": "Output only. The name of the speech recognizer to use for speech recognition. The expected format is `projects/{project}/locations/{location}/recognizers/{recognizer}`. Customers can specify this field at model creation. If not specified, a default recognizer `projects/{model project}/locations/global/recognizers/_` will be used. See more details at [recognizers](https://cloud.google.com/speech-to-text/v2/docs/reference/rest/v2/projects.locations.recognizers)", "readOnly": true, "type": "string"}}, "type": "object"}, "RestrictionConfig": {"id": "RestrictionConfig", "properties": {"type": {"description": "Output only. Specifies the type of dataset/table restriction.", "enum": ["RESTRICTION_TYPE_UNSPECIFIED", "RESTRICTED_DATA_EGRESS"], "enumDescriptions": ["Should never be used.", "Restrict data egress. See [Data egress](https://cloud.google.com/bigquery/docs/analytics-hub-introduction#data_egress) for more details."], "readOnly": true, "type": "string"}}, "type": "object"}, "Routine": {"description": "A user-defined function or a stored procedure.", "id": "Routine", "properties": {"arguments": {"description": "Optional.", "items": {"$ref": "Argument"}, "type": "array"}, "creationTime": {"description": "Output only. The time when this routine was created, in milliseconds since the epoch.", "format": "int64", "readOnly": true, "type": "string"}, "dataGovernanceType": {"description": "Optional. If set to `DATA_MASKING`, the function is validated and made available as a masking function. For more information, see [Create custom masking routines](https://cloud.google.com/bigquery/docs/user-defined-functions#custom-mask).", "enum": ["DATA_GOVERNANCE_TYPE_UNSPECIFIED", "DATA_MASKING"], "enumDescriptions": ["The data governance type is unspecified.", "The data governance type is data masking."], "type": "string"}, "definitionBody": {"description": "Required. The body of the routine. For functions, this is the expression in the AS clause. If language=SQL, it is the substring inside (but excluding) the parentheses. For example, for the function created with the following statement: `CREATE FUNCTION JoinLines(x string, y string) as (concat(x, \"\\n\", y))` The definition_body is `concat(x, \"\\n\", y)` (\\n is not replaced with linebreak). If language=JAVASCRIPT, it is the evaluated string in the AS clause. For example, for the function created with the following statement: `CREATE FUNCTION f() RETURNS STRING LANGUAGE js AS 'return \"\\n\";\\n'` The definition_body is `return \"\\n\";\\n` Note that both \\n are replaced with linebreaks.", "type": "string"}, "description": {"description": "Optional. The description of the routine, if defined.", "type": "string"}, "determinismLevel": {"description": "Optional. The determinism level of the JavaScript UDF, if defined.", "enum": ["DETERMINISM_LEVEL_UNSPECIFIED", "DETERMINISTIC", "NOT_DETERMINISTIC"], "enumDescriptions": ["The determinism of the UDF is unspecified.", "The UDF is deterministic, meaning that 2 function calls with the same inputs always produce the same result, even across 2 query runs.", "The UDF is not deterministic."], "type": "string"}, "etag": {"description": "Output only. A hash of this resource.", "readOnly": true, "type": "string"}, "importedLibraries": {"description": "Optional. If language = \"JAVASCRIPT\", this field stores the path of the imported JAVASCRIPT libraries.", "items": {"type": "string"}, "type": "array"}, "language": {"description": "Optional. Defaults to \"SQL\" if remote_function_options field is absent, not set otherwise.", "enum": ["LANGUAGE_UNSPECIFIED", "SQL", "JAVASCRIPT", "PYTHON", "JAVA", "SCALA"], "enumDescriptions": ["Default value.", "SQL language.", "JavaScript language.", "Python language.", "Java language.", "Scala language."], "type": "string"}, "lastModifiedTime": {"description": "Output only. The time when this routine was last modified, in milliseconds since the epoch.", "format": "int64", "readOnly": true, "type": "string"}, "remoteFunctionOptions": {"$ref": "RemoteFunctionOptions", "description": "Optional. Remote function specific options."}, "returnTableType": {"$ref": "StandardSqlTableType", "description": "Optional. Can be set only if routine_type = \"TABLE_VALUED_FUNCTION\". If absent, the return table type is inferred from definition_body at query time in each query that references this routine. If present, then the columns in the evaluated table result will be cast to match the column types specified in return table type, at query time."}, "returnType": {"$ref": "StandardSqlDataType", "description": "Optional if language = \"SQL\"; required otherwise. Cannot be set if routine_type = \"TABLE_VALUED_FUNCTION\". If absent, the return type is inferred from definition_body at query time in each query that references this routine. If present, then the evaluated result will be cast to the specified returned type at query time. For example, for the functions created with the following statements: * `CREATE FUNCTION Add(x FLOAT64, y FLOAT64) RETURNS FLOAT64 AS (x + y);` * `CREATE FUNCTION Increment(x FLOAT64) AS (Add(x, 1));` * `CREATE FUNCTION Decrement(x FLOAT64) RETURNS FLOAT64 AS (Add(x, -1));` The return_type is `{type_kind: \"FLOAT64\"}` for `Add` and `Decrement`, and is absent for `Increment` (inferred as FLOAT64 at query time). Suppose the function `Add` is replaced by `CREATE OR REPLACE FUNCTION Add(x INT64, y INT64) AS (x + y);` Then the inferred return type of `Increment` is automatically changed to INT64 at query time, while the return type of `Decrement` remains FLOAT64."}, "routineReference": {"$ref": "RoutineReference", "description": "Required. Reference describing the ID of this routine."}, "routineType": {"description": "Required. The type of routine.", "enum": ["ROUTINE_TYPE_UNSPECIFIED", "SCALAR_FUNCTION", "PROCEDURE", "TABLE_VALUED_FUNCTION", "AGGREGATE_FUNCTION"], "enumDescriptions": ["Default value.", "Non-built-in persistent scalar function.", "Stored procedure.", "Non-built-in persistent TVF.", "Non-built-in persistent aggregate function."], "type": "string"}, "securityMode": {"description": "Optional. The security mode of the routine, if defined. If not defined, the security mode is automatically determined from the routine's configuration.", "enum": ["SECURITY_MODE_UNSPECIFIED", "DEFINER", "INVOKER"], "enumDescriptions": ["The security mode of the routine is unspecified.", "The routine is to be executed with the privileges of the user who defines it.", "The routine is to be executed with the privileges of the user who invokes it."], "type": "string"}, "sparkOptions": {"$ref": "SparkOptions", "description": "Optional. Spark specific options."}, "strictMode": {"description": "Optional. Use this option to catch many common errors. Error checking is not exhaustive, and successfully creating a procedure doesn't guarantee that the procedure will successfully execute at runtime. If `strictMode` is set to `TRUE`, the procedure body is further checked for errors such as non-existent tables or columns. The `CREATE PROCEDURE` statement fails if the body fails any of these checks. If `strictMode` is set to `FALSE`, the procedure body is checked only for syntax. For procedures that invoke themselves recursively, specify `strictMode=FALSE` to avoid non-existent procedure errors during validation. Default value is `TRUE`.", "type": "boolean"}}, "type": "object"}, "RoutineReference": {"description": "Id path of a routine.", "id": "RoutineReference", "properties": {"datasetId": {"description": "Required. The ID of the dataset containing this routine.", "type": "string"}, "projectId": {"description": "Required. The ID of the project containing this routine.", "type": "string"}, "routineId": {"description": "Required. The ID of the routine. The ID must contain only letters (a-z, A-Z), numbers (0-9), or underscores (_). The maximum length is 256 characters.", "type": "string"}}, "type": "object"}, "Row": {"description": "A single row in the confusion matrix.", "id": "Row", "properties": {"actualLabel": {"description": "The original label of this row.", "type": "string"}, "entries": {"description": "Info describing predicted label distribution.", "items": {"$ref": "Entry"}, "type": "array"}}, "type": "object"}, "RowAccessPolicy": {"description": "Represents access on a subset of rows on the specified table, defined by its filter predicate. Access to the subset of rows is controlled by its IAM policy.", "id": "RowAccessPolicy", "properties": {"creationTime": {"description": "Output only. The time when this row access policy was created, in milliseconds since the epoch.", "format": "google-datetime", "readOnly": true, "type": "string"}, "etag": {"description": "Output only. A hash of this resource.", "readOnly": true, "type": "string"}, "filterPredicate": {"description": "Required. A SQL boolean expression that represents the rows defined by this row access policy, similar to the boolean expression in a WHERE clause of a SELECT query on a table. References to other tables, routines, and temporary functions are not supported. Examples: region=\"EU\" date_field = CAST('2019-9-27' as DATE) nullable_field is not NULL numeric_field BETWEEN 1.0 AND 5.0", "type": "string"}, "grantees": {"description": "Optional. Input only. The optional list of iam_member users or groups that specifies the initial members that the row-level access policy should be created with. grantees types: - \"user:<EMAIL>\": An email address that represents a specific Google account. - \"serviceAccount:<EMAIL>\": An email address that represents a service account. - \"group:<EMAIL>\": An email address that represents a Google group. - \"domain:example.com\":The Google Workspace domain (primary) that represents all the users of that domain. - \"allAuthenticatedUsers\": A special identifier that represents all service accounts and all users on the internet who have authenticated with a Google Account. This identifier includes accounts that aren't connected to a Google Workspace or Cloud Identity domain, such as personal Gmail accounts. Users who aren't authenticated, such as anonymous visitors, aren't included. - \"allUsers\":A special identifier that represents anyone who is on the internet, including authenticated and unauthenticated users. Because BigQuery requires authentication before a user can access the service, allUsers includes only authenticated users.", "items": {"type": "string"}, "type": "array"}, "lastModifiedTime": {"description": "Output only. The time when this row access policy was last modified, in milliseconds since the epoch.", "format": "google-datetime", "readOnly": true, "type": "string"}, "rowAccessPolicyReference": {"$ref": "RowAccessPolicyReference", "description": "Required. Reference describing the ID of this row access policy."}}, "type": "object"}, "RowAccessPolicyReference": {"description": "Id path of a row access policy.", "id": "RowAccessPolicyReference", "properties": {"datasetId": {"description": "Required. The ID of the dataset containing this row access policy.", "type": "string"}, "policyId": {"description": "Required. The ID of the row access policy. The ID must contain only letters (a-z, A-Z), numbers (0-9), or underscores (_). The maximum length is 256 characters.", "type": "string"}, "projectId": {"description": "Required. The ID of the project containing this row access policy.", "type": "string"}, "tableId": {"description": "Required. The ID of the table containing this row access policy.", "type": "string"}}, "type": "object"}, "RowLevelSecurityStatistics": {"description": "Statistics for row-level security.", "id": "RowLevelSecurityStatistics", "properties": {"rowLevelSecurityApplied": {"description": "Whether any accessed data was protected by row access policies.", "type": "boolean"}}, "type": "object"}, "ScriptOptions": {"description": "Options related to script execution.", "id": "ScriptOptions", "properties": {"keyResultStatement": {"description": "Determines which statement in the script represents the \"key result\", used to populate the schema and query results of the script job. Default is LAST.", "enum": ["KEY_RESULT_STATEMENT_KIND_UNSPECIFIED", "LAST", "FIRST_SELECT"], "enumDescriptions": ["Default value.", "The last result determines the key result.", "The first SELECT statement determines the key result."], "type": "string"}, "statementByteBudget": {"description": "Limit on the number of bytes billed per statement. Exceeding this budget results in an error.", "format": "int64", "type": "string"}, "statementTimeoutMs": {"description": "Timeout period for each statement in a script.", "format": "int64", "type": "string"}}, "type": "object"}, "ScriptStackFrame": {"description": "Represents the location of the statement/expression being evaluated. Line and column numbers are defined as follows: - Line and column numbers start with one. That is, line 1 column 1 denotes the start of the script. - When inside a stored procedure, all line/column numbers are relative to the procedure body, not the script in which the procedure was defined. - Start/end positions exclude leading/trailing comments and whitespace. The end position always ends with a \";\", when present. - Multi-byte Unicode characters are treated as just one column. - If the original script (or procedure definition) contains TAB characters, a tab \"snaps\" the indentation forward to the nearest multiple of 8 characters, plus 1. For example, a TAB on column 1, 2, 3, 4, 5, 6 , or 8 will advance the next character to column 9. A TAB on column 9, 10, 11, 12, 13, 14, 15, or 16 will advance the next character to column 17.", "id": "ScriptStackFrame", "properties": {"endColumn": {"description": "Output only. One-based end column.", "format": "int32", "readOnly": true, "type": "integer"}, "endLine": {"description": "Output only. One-based end line.", "format": "int32", "readOnly": true, "type": "integer"}, "procedureId": {"description": "Output only. Name of the active procedure, empty if in a top-level script.", "readOnly": true, "type": "string"}, "startColumn": {"description": "Output only. One-based start column.", "format": "int32", "readOnly": true, "type": "integer"}, "startLine": {"description": "Output only. One-based start line.", "format": "int32", "readOnly": true, "type": "integer"}, "text": {"description": "Output only. Text of the current statement/expression.", "readOnly": true, "type": "string"}}, "type": "object"}, "ScriptStatistics": {"description": "Job statistics specific to the child job of a script.", "id": "ScriptStatistics", "properties": {"evaluationKind": {"description": "Whether this child job was a statement or expression.", "enum": ["EVALUATION_KIND_UNSPECIFIED", "STATEMENT", "EXPRESSION"], "enumDescriptions": ["Default value.", "The statement appears directly in the script.", "The statement evaluates an expression that appears in the script."], "type": "string"}, "stackFrames": {"description": "Stack trace showing the line/column/procedure name of each frame on the stack at the point where the current evaluation happened. The leaf frame is first, the primary script is last. Never empty.", "items": {"$ref": "ScriptStackFrame"}, "type": "array"}}, "type": "object"}, "SearchStatistics": {"description": "Statistics for a search query. Populated as part of JobStatistics2.", "id": "SearchStatistics", "properties": {"indexUnusedReasons": {"description": "When `indexUsageMode` is `UNUSED` or `PARTIALLY_USED`, this field explains why indexes were not used in all or part of the search query. If `indexUsageMode` is `FULLY_USED`, this field is not populated.", "items": {"$ref": "IndexUnusedReason"}, "type": "array"}, "indexUsageMode": {"description": "Specifies the index usage mode for the query.", "enum": ["INDEX_USAGE_MODE_UNSPECIFIED", "UNUSED", "PARTIALLY_USED", "FULLY_USED"], "enumDescriptions": ["Index usage mode not specified.", "No search indexes were used in the search query. See [`indexUnusedReasons`] (/bigquery/docs/reference/rest/v2/Job#IndexUnusedReason) for detailed reasons.", "Part of the search query used search indexes. See [`indexUnusedReasons`] (/bigquery/docs/reference/rest/v2/Job#IndexUnusedReason) for why other parts of the query did not use search indexes.", "The entire search query used search indexes."], "type": "string"}}, "type": "object"}, "SerDeInfo": {"description": "Serializer and deserializer information.", "id": "SerDeInfo", "properties": {"name": {"description": "Optional. Name of the SerDe. The maximum length is 256 characters.", "type": "string"}, "parameters": {"additionalProperties": {"type": "string"}, "description": "Optional. Key-value pairs that define the initialization parameters for the serialization library. Maximum size 10 Kib.", "type": "object"}, "serializationLibrary": {"description": "Required. Specifies a fully-qualified class name of the serialization library that is responsible for the translation of data between table representation and the underlying low-level input and output format structures. The maximum length is 256 characters.", "type": "string"}}, "type": "object"}, "SessionInfo": {"description": "[Preview] Information related to sessions.", "id": "SessionInfo", "properties": {"sessionId": {"description": "Output only. The id of the session.", "readOnly": true, "type": "string"}}, "type": "object"}, "SetIamPolicyRequest": {"description": "Request message for `SetIamPolicy` method.", "id": "SetIamPolicyRequest", "properties": {"policy": {"$ref": "Policy", "description": "REQUIRED: The complete policy to be applied to the `resource`. The size of the policy is limited to a few 10s of KB. An empty policy is a valid policy but certain Google Cloud services (such as Projects) might reject them."}, "updateMask": {"description": "OPTIONAL: A FieldMask specifying which fields of the policy to modify. Only the fields in the mask will be modified. If no mask is provided, the following default mask is used: `paths: \"bindings, etag\"`", "format": "google-fieldmask", "type": "string"}}, "type": "object"}, "SkewSource": {"description": "Details about source stages which produce skewed data.", "id": "SkewSource", "properties": {"stageId": {"description": "Output only. Stage id of the skew source stage.", "format": "int64", "readOnly": true, "type": "string"}}, "type": "object"}, "SnapshotDefinition": {"description": "Information about base table and snapshot time of the snapshot.", "id": "SnapshotDefinition", "properties": {"baseTableReference": {"$ref": "TableReference", "description": "Required. Reference describing the ID of the table that was snapshot."}, "snapshotTime": {"description": "Required. The time at which the base table was snapshot. This value is reported in the JSON response using RFC3339 format.", "format": "date-time", "type": "string"}}, "type": "object"}, "SparkLoggingInfo": {"description": "Spark job logs can be filtered by these fields in Cloud Logging.", "id": "SparkLoggingInfo", "properties": {"projectId": {"description": "Output only. Project ID where the Spark logs were written.", "readOnly": true, "type": "string"}, "resourceType": {"description": "Output only. Resource type used for logging.", "readOnly": true, "type": "string"}}, "type": "object"}, "SparkOptions": {"description": "Options for a user-defined Spark routine.", "id": "SparkOptions", "properties": {"archiveUris": {"description": "Archive files to be extracted into the working directory of each executor. For more information about Apache Spark, see [Apache Spark](https://spark.apache.org/docs/latest/index.html).", "items": {"type": "string"}, "type": "array"}, "connection": {"description": "Fully qualified name of the user-provided Spark connection object. Format: ```\"projects/{project_id}/locations/{location_id}/connections/{connection_id}\"```", "type": "string"}, "containerImage": {"description": "Custom container image for the runtime environment.", "type": "string"}, "fileUris": {"description": "Files to be placed in the working directory of each executor. For more information about Apache Spark, see [Apache Spark](https://spark.apache.org/docs/latest/index.html).", "items": {"type": "string"}, "type": "array"}, "jarUris": {"description": "JARs to include on the driver and executor CLASSPATH. For more information about Apache Spark, see [Apache Spark](https://spark.apache.org/docs/latest/index.html).", "items": {"type": "string"}, "type": "array"}, "mainClass": {"description": "The fully qualified name of a class in jar_uris, for example, com.example.wordcount. Exactly one of main_class and main_jar_uri field should be set for Java/Scala language type.", "type": "string"}, "mainFileUri": {"description": "The main file/jar URI of the Spark application. Exactly one of the definition_body field and the main_file_uri field must be set for Python. Exactly one of main_class and main_file_uri field should be set for Java/Scala language type.", "type": "string"}, "properties": {"additionalProperties": {"type": "string"}, "description": "Configuration properties as a set of key/value pairs, which will be passed on to the Spark application. For more information, see [Apache Spark](https://spark.apache.org/docs/latest/index.html) and the [procedure option list](https://cloud.google.com/bigquery/docs/reference/standard-sql/data-definition-language#procedure_option_list).", "type": "object"}, "pyFileUris": {"description": "Python files to be placed on the PYTHONPATH for PySpark application. Supported file types: `.py`, `.egg`, and `.zip`. For more information about Apache Spark, see [Apache Spark](https://spark.apache.org/docs/latest/index.html).", "items": {"type": "string"}, "type": "array"}, "runtimeVersion": {"description": "Runtime version. If not specified, the default runtime version is used.", "type": "string"}}, "type": "object"}, "SparkStatistics": {"description": "Statistics for a BigSpark query. Populated as part of JobStatistics2", "id": "SparkStatistics", "properties": {"endpoints": {"additionalProperties": {"type": "string"}, "description": "Output only. Endpoints returned from Dataproc. Key list: - history_server_endpoint: A link to Spark job UI.", "readOnly": true, "type": "object"}, "gcsStagingBucket": {"description": "Output only. The Google Cloud Storage bucket that is used as the default file system by the Spark application. This field is only filled when the Spark procedure uses the invoker security mode. The `gcsStagingBucket` bucket is inferred from the `@@spark_proc_properties.staging_bucket` system variable (if it is provided). Otherwise, BigQuery creates a default staging bucket for the job and returns the bucket name in this field. Example: * `gs://[bucket_name]`", "readOnly": true, "type": "string"}, "kmsKeyName": {"description": "Output only. The Cloud KMS encryption key that is used to protect the resources created by the Spark job. If the Spark procedure uses the invoker security mode, the Cloud KMS encryption key is either inferred from the provided system variable, `@@spark_proc_properties.kms_key_name`, or the default key of the BigQuery job's project (if the CMEK organization policy is enforced). Otherwise, the Cloud KMS key is either inferred from the Spark connection associated with the procedure (if it is provided), or from the default key of the Spark connection's project if the CMEK organization policy is enforced. Example: * `projects/[kms_project_id]/locations/[region]/keyRings/[key_region]/cryptoKeys/[key]`", "readOnly": true, "type": "string"}, "loggingInfo": {"$ref": "SparkLoggingInfo", "description": "Output only. Logging info is used to generate a link to Cloud Logging.", "readOnly": true}, "sparkJobId": {"description": "Output only. Spark job ID if a Spark job is created successfully.", "readOnly": true, "type": "string"}, "sparkJobLocation": {"description": "Output only. Location where the Spark job is executed. A location is selected by BigQueury for jobs configured to run in a multi-region.", "readOnly": true, "type": "string"}}, "type": "object"}, "StagePerformanceChangeInsight": {"description": "Performance insights compared to the previous executions for a specific stage.", "id": "StagePerformanceChangeInsight", "properties": {"inputDataChange": {"$ref": "InputDataChange", "description": "Output only. Input data change insight of the query stage.", "readOnly": true}, "stageId": {"description": "Output only. The stage id that the insight mapped to.", "format": "int64", "readOnly": true, "type": "string"}}, "type": "object"}, "StagePerformanceStandaloneInsight": {"description": "Standalone performance insights for a specific stage.", "id": "StagePerformanceStandaloneInsight", "properties": {"biEngineReasons": {"description": "Output only. If present, the stage had the following reasons for being disqualified from BI Engine execution.", "items": {"$ref": "BiEngineReason"}, "readOnly": true, "type": "array"}, "highCardinalityJoins": {"description": "Output only. High cardinality joins in the stage.", "items": {"$ref": "HighCardinalityJoin"}, "readOnly": true, "type": "array"}, "insufficientShuffleQuota": {"description": "Output only. True if the stage has insufficient shuffle quota.", "readOnly": true, "type": "boolean"}, "partitionSkew": {"$ref": "PartitionSkew", "description": "Output only. Partition skew in the stage.", "readOnly": true}, "slotContention": {"description": "Output only. True if the stage has a slot contention issue.", "readOnly": true, "type": "boolean"}, "stageId": {"description": "Output only. The stage id that the insight mapped to.", "format": "int64", "readOnly": true, "type": "string"}}, "type": "object"}, "StandardSqlDataType": {"description": "The data type of a variable such as a function argument. Examples include: * INT64: `{\"typeKind\": \"INT64\"}` * ARRAY: { \"typeKind\": \"ARRAY\", \"arrayElementType\": {\"typeKind\": \"STRING\"} } * STRUCT>: { \"typeKind\": \"STRUCT\", \"structType\": { \"fields\": [ { \"name\": \"x\", \"type\": {\"typeKind\": \"STRING\"} }, { \"name\": \"y\", \"type\": { \"typeKind\": \"ARRAY\", \"arrayElementType\": {\"typeKind\": \"DATE\"} } } ] } } * RANGE: { \"typeKind\": \"RANGE\", \"rangeElementType\": {\"typeKind\": \"DATE\"} }", "id": "StandardSqlDataType", "properties": {"arrayElementType": {"$ref": "StandardSqlDataType", "description": "The type of the array's elements, if type_kind = \"ARRAY\"."}, "rangeElementType": {"$ref": "StandardSqlDataType", "description": "The type of the range's elements, if type_kind = \"RANGE\"."}, "structType": {"$ref": "StandardSqlStructType", "description": "The fields of this struct, in order, if type_kind = \"STRUCT\"."}, "typeKind": {"description": "Required. The top level type of this field. Can be any GoogleSQL data type (e.g., \"INT64\", \"DATE\", \"ARRAY\").", "enum": ["TYPE_KIND_UNSPECIFIED", "INT64", "BOOL", "FLOAT64", "STRING", "BYTES", "TIMESTAMP", "DATE", "TIME", "DATETIME", "INTERVAL", "GEOGRAPHY", "NUMERIC", "BIGNUMERIC", "JSON", "ARRAY", "STRUCT", "RANGE"], "enumDescriptions": ["Invalid type.", "Encoded as a string in decimal format.", "Encoded as a boolean \"false\" or \"true\".", "Encoded as a number, or string \"NaN\", \"Infinity\" or \"-Infinity\".", "Encoded as a string value.", "Encoded as a base64 string per RFC 4648, section 4.", "Encoded as an RFC 3339 timestamp with mandatory \"Z\" time zone string: 1985-04-12T23:20:50.52Z", "Encoded as RFC 3339 full-date format string: 1985-04-12", "Encoded as RFC 3339 partial-time format string: 23:20:50.52", "Encoded as RFC 3339 full-date \"T\" partial-time: 1985-04-12T23:20:50.52", "Encoded as fully qualified 3 part: 0-5 15 2:30:45.6", "Encoded as WKT", "Encoded as a decimal string.", "Encoded as a decimal string.", "Encoded as a string.", "Encoded as a list with types matching Type.array_type.", "Encoded as a list with fields of type Type.struct_type[i]. List is used because a JSON object cannot have duplicate field names.", "Encoded as a pair with types matching range_element_type. Pairs must begin with \"[\", end with \")\", and be separated by \", \"."], "type": "string"}}, "type": "object"}, "StandardSqlField": {"description": "A field or a column.", "id": "StandardSqlField", "properties": {"name": {"description": "Optional. The name of this field. Can be absent for struct fields.", "type": "string"}, "type": {"$ref": "StandardSqlDataType", "description": "Optional. The type of this parameter. Absent if not explicitly specified (e.g., CREATE FUNCTION statement can omit the return type; in this case the output parameter does not have this \"type\" field)."}}, "type": "object"}, "StandardSqlStructType": {"description": "The representation of a SQL STRUCT type.", "id": "StandardSqlStructType", "properties": {"fields": {"description": "Fields within the struct.", "items": {"$ref": "StandardSqlField"}, "type": "array"}}, "type": "object"}, "StandardSqlTableType": {"description": "A table type", "id": "StandardSqlTableType", "properties": {"columns": {"description": "The columns in this table type", "items": {"$ref": "StandardSqlField"}, "type": "array"}}, "type": "object"}, "StorageDescriptor": {"description": "Contains information about how a table's data is stored and accessed by open source query engines.", "id": "StorageDescriptor", "properties": {"inputFormat": {"description": "Optional. Specifies the fully qualified class name of the InputFormat (e.g. \"org.apache.hadoop.hive.ql.io.orc.OrcInputFormat\"). The maximum length is 128 characters.", "type": "string"}, "locationUri": {"description": "Optional. The physical location of the table (e.g. `gs://spark-dataproc-data/pangea-data/case_sensitive/` or `gs://spark-dataproc-data/pangea-data/*`). The maximum length is 2056 bytes.", "type": "string"}, "outputFormat": {"description": "Optional. Specifies the fully qualified class name of the OutputFormat (e.g. \"org.apache.hadoop.hive.ql.io.orc.OrcOutputFormat\"). The maximum length is 128 characters.", "type": "string"}, "serdeInfo": {"$ref": "SerDeInfo", "description": "Optional. Serializer and deserializer information."}}, "type": "object"}, "StoredColumnsUnusedReason": {"description": "If the stored column was not used, explain why.", "id": "StoredColumnsUnusedReason", "properties": {"code": {"description": "Specifies the high-level reason for the unused scenario, each reason must have a code associated.", "enum": ["CODE_UNSPECIFIED", "STORED_COLUMNS_COVER_INSUFFICIENT", "BASE_TABLE_HAS_RLS", "BASE_TABLE_HAS_CLS", "UNSUPPORTED_PREFILTER", "INTERNAL_ERROR", "OTHER_REASON"], "enumDescriptions": ["Default value.", "If stored columns do not fully cover the columns.", "If the base table has RLS (Row Level Security).", "If the base table has CLS (Column Level Security).", "If the provided prefilter is not supported.", "If an internal error is preventing stored columns from being used.", "Indicates that the reason stored columns cannot be used in the query is not covered by any of the other StoredColumnsUnusedReason options."], "type": "string"}, "message": {"description": "Specifies the detailed description for the scenario.", "type": "string"}, "uncoveredColumns": {"description": "Specifies which columns were not covered by the stored columns for the specified code up to 20 columns. This is populated when the code is STORED_COLUMNS_COVER_INSUFFICIENT and BASE_TABLE_HAS_CLS.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "StoredColumnsUsage": {"description": "Indicates the stored columns usage in the query.", "id": "StoredColumnsUsage", "properties": {"baseTable": {"$ref": "TableReference", "description": "Specifies the base table."}, "isQueryAccelerated": {"description": "Specifies whether the query was accelerated with stored columns.", "type": "boolean"}, "storedColumnsUnusedReasons": {"description": "If stored columns were not used, explain why.", "items": {"$ref": "StoredColumnsUnusedReason"}, "type": "array"}}, "type": "object"}, "Streamingbuffer": {"id": "Streamingbuffer", "properties": {"estimatedBytes": {"description": "Output only. A lower-bound estimate of the number of bytes currently in the streaming buffer.", "format": "uint64", "readOnly": true, "type": "string"}, "estimatedRows": {"description": "Output only. A lower-bound estimate of the number of rows currently in the streaming buffer.", "format": "uint64", "readOnly": true, "type": "string"}, "oldestEntryTime": {"description": "Output only. Contains the timestamp of the oldest entry in the streaming buffer, in milliseconds since the epoch, if the streaming buffer is available.", "format": "uint64", "readOnly": true, "type": "string"}}, "type": "object"}, "StringHparamSearchSpace": {"description": "Search space for string and enum.", "id": "StringHparamSearchSpace", "properties": {"candidates": {"description": "Canididates for the string or enum parameter in lower case.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "SystemVariables": {"description": "System variables given to a query.", "id": "SystemVariables", "properties": {"types": {"additionalProperties": {"$ref": "StandardSqlDataType"}, "description": "Output only. Data type for each system variable.", "readOnly": true, "type": "object"}, "values": {"additionalProperties": {"description": "Properties of the object.", "type": "any"}, "description": "Output only. Value for each system variable.", "readOnly": true, "type": "object"}}, "type": "object"}, "Table": {"id": "Table", "properties": {"biglakeConfiguration": {"$ref": "BigLakeConfiguration", "description": "Optional. Specifies the configuration of a BigQuery table for Apache Iceberg."}, "cloneDefinition": {"$ref": "CloneDefinition", "description": "Output only. Contains information about the clone. This value is set via the clone operation.", "readOnly": true}, "clustering": {"$ref": "Clustering", "description": "Clustering specification for the table. Must be specified with time-based partitioning, data in the table will be first partitioned and subsequently clustered."}, "creationTime": {"description": "Output only. The time when this table was created, in milliseconds since the epoch.", "format": "int64", "readOnly": true, "type": "string"}, "defaultCollation": {"description": "Optional. Defines the default collation specification of new STRING fields in the table. During table creation or update, if a STRING field is added to this table without explicit collation specified, then the table inherits the table default collation. A change to this field affects only fields added afterwards, and does not alter the existing fields. The following values are supported: * 'und:ci': undetermined locale, case insensitive. * '': empty string. Default to case-sensitive behavior.", "type": "string"}, "defaultRoundingMode": {"description": "Optional. Defines the default rounding mode specification of new decimal fields (NUMERIC OR BIGNUMERIC) in the table. During table creation or update, if a decimal field is added to this table without an explicit rounding mode specified, then the field inherits the table default rounding mode. Changing this field doesn't affect existing fields.", "enum": ["ROUNDING_MODE_UNSPECIFIED", "ROUND_HALF_AWAY_FROM_ZERO", "ROUND_HALF_EVEN"], "enumDescriptions": ["Unspecified will default to using ROUND_HALF_AWAY_FROM_ZERO.", "ROUND_HALF_AWAY_FROM_ZERO rounds half values away from zero when applying precision and scale upon writing of NUMERIC and BIGNUMERIC values. For Scale: 0 1.1, 1.2, 1.3, 1.4 => 1 1.5, 1.6, 1.7, 1.8, 1.9 => 2", "ROUND_HALF_EVEN rounds half values to the nearest even value when applying precision and scale upon writing of NUMERIC and BIGNUMERIC values. For Scale: 0 1.1, 1.2, 1.3, 1.4 => 1 1.5 => 2 1.6, 1.7, 1.8, 1.9 => 2 2.5 => 2"], "type": "string"}, "description": {"description": "Optional. A user-friendly description of this table.", "type": "string"}, "encryptionConfiguration": {"$ref": "EncryptionConfiguration", "description": "Custom encryption configuration (e.g., Cloud KMS keys)."}, "etag": {"description": "Output only. A hash of this resource.", "readOnly": true, "type": "string"}, "expirationTime": {"description": "Optional. The time when this table expires, in milliseconds since the epoch. If not present, the table will persist indefinitely. Expired tables will be deleted and their storage reclaimed. The defaultTableExpirationMs property of the encapsulating dataset can be used to set a default expirationTime on newly created tables.", "format": "int64", "type": "string"}, "externalCatalogTableOptions": {"$ref": "ExternalCatalogTableOptions", "description": "Optional. Options defining open source compatible table."}, "externalDataConfiguration": {"$ref": "ExternalDataConfiguration", "description": "Optional. Describes the data format, location, and other properties of a table stored outside of BigQuery. By defining these properties, the data source can then be queried as if it were a standard BigQuery table."}, "friendlyName": {"description": "Optional. A descriptive name for this table.", "type": "string"}, "id": {"description": "Output only. An opaque ID uniquely identifying the table.", "readOnly": true, "type": "string"}, "kind": {"default": "bigquery#table", "description": "The type of resource ID.", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "The labels associated with this table. You can use these to organize and group your tables. Label keys and values can be no longer than 63 characters, can only contain lowercase letters, numeric characters, underscores and dashes. International characters are allowed. Label values are optional. Label keys must start with a letter and each label in the list must have a different key.", "type": "object"}, "lastModifiedTime": {"description": "Output only. The time when this table was last modified, in milliseconds since the epoch.", "format": "uint64", "readOnly": true, "type": "string"}, "location": {"description": "Output only. The geographic location where the table resides. This value is inherited from the dataset.", "readOnly": true, "type": "string"}, "managedTableType": {"description": "Optional. If set, overrides the default managed table type configured in the dataset.", "enum": ["MANAGED_TABLE_TYPE_UNSPECIFIED", "NATIVE", "ICEBERG"], "enumDescriptions": ["No managed table type specified.", "The managed table is a native BigQuery table.", "The managed table is a BigQuery table for Apache Iceberg."], "type": "string"}, "materializedView": {"$ref": "MaterializedViewDefinition", "description": "Optional. The materialized view definition."}, "materializedViewStatus": {"$ref": "MaterializedViewStatus", "description": "Output only. The materialized view status.", "readOnly": true}, "maxStaleness": {"description": "Optional. The maximum staleness of data that could be returned when the table (or stale MV) is queried. Staleness encoded as a string encoding of sql IntervalValue type.", "type": "string"}, "model": {"$ref": "ModelDefinition", "description": "Deprecated."}, "numActiveLogicalBytes": {"description": "Output only. Number of logical bytes that are less than 90 days old.", "format": "int64", "readOnly": true, "type": "string"}, "numActivePhysicalBytes": {"description": "Output only. Number of physical bytes less than 90 days old. This data is not kept in real time, and might be delayed by a few seconds to a few minutes.", "format": "int64", "readOnly": true, "type": "string"}, "numBytes": {"description": "Output only. The size of this table in logical bytes, excluding any data in the streaming buffer.", "format": "int64", "readOnly": true, "type": "string"}, "numCurrentPhysicalBytes": {"description": "Output only. Number of physical bytes used by current live data storage. This data is not kept in real time, and might be delayed by a few seconds to a few minutes.", "format": "int64", "readOnly": true, "type": "string"}, "numLongTermBytes": {"description": "Output only. The number of logical bytes in the table that are considered \"long-term storage\".", "format": "int64", "readOnly": true, "type": "string"}, "numLongTermLogicalBytes": {"description": "Output only. Number of logical bytes that are more than 90 days old.", "format": "int64", "readOnly": true, "type": "string"}, "numLongTermPhysicalBytes": {"description": "Output only. Number of physical bytes more than 90 days old. This data is not kept in real time, and might be delayed by a few seconds to a few minutes.", "format": "int64", "readOnly": true, "type": "string"}, "numPartitions": {"description": "Output only. The number of partitions present in the table or materialized view. This data is not kept in real time, and might be delayed by a few seconds to a few minutes.", "format": "int64", "readOnly": true, "type": "string"}, "numPhysicalBytes": {"description": "Output only. The physical size of this table in bytes. This includes storage used for time travel.", "format": "int64", "readOnly": true, "type": "string"}, "numRows": {"description": "Output only. The number of rows of data in this table, excluding any data in the streaming buffer.", "format": "uint64", "readOnly": true, "type": "string"}, "numTimeTravelPhysicalBytes": {"description": "Output only. Number of physical bytes used by time travel storage (deleted or changed data). This data is not kept in real time, and might be delayed by a few seconds to a few minutes.", "format": "int64", "readOnly": true, "type": "string"}, "numTotalLogicalBytes": {"description": "Output only. Total number of logical bytes in the table or materialized view.", "format": "int64", "readOnly": true, "type": "string"}, "numTotalPhysicalBytes": {"description": "Output only. The physical size of this table in bytes. This also includes storage used for time travel. This data is not kept in real time, and might be delayed by a few seconds to a few minutes.", "format": "int64", "readOnly": true, "type": "string"}, "partitionDefinition": {"$ref": "PartitioningDefinition", "description": "Optional. The partition information for all table formats, including managed partitioned tables, hive partitioned tables, iceberg partitioned, and metastore partitioned tables. This field is only populated for metastore partitioned tables. For other table formats, this is an output only field."}, "rangePartitioning": {"$ref": "RangePartitioning", "description": "If specified, configures range partitioning for this table."}, "replicas": {"description": "Optional. Output only. Table references of all replicas currently active on the table.", "items": {"$ref": "TableReference"}, "readOnly": true, "type": "array"}, "requirePartitionFilter": {"default": "false", "description": "Optional. If set to true, queries over this table require a partition filter that can be used for partition elimination to be specified.", "type": "boolean"}, "resourceTags": {"additionalProperties": {"type": "string"}, "description": "[Optional] The tags associated with this table. Tag keys are globally unique. See additional information on [tags](https://cloud.google.com/iam/docs/tags-access-control#definitions). An object containing a list of \"key\": value pairs. The key is the namespaced friendly name of the tag key, e.g. \"12345/environment\" where 12345 is parent id. The value is the friendly short name of the tag value, e.g. \"production\".", "type": "object"}, "restrictions": {"$ref": "RestrictionConfig", "description": "Optional. Output only. Restriction config for table. If set, restrict certain accesses on the table based on the config. See [Data egress](https://cloud.google.com/bigquery/docs/analytics-hub-introduction#data_egress) for more details.", "readOnly": true}, "schema": {"$ref": "TableSchema", "description": "Optional. Describes the schema of this table."}, "selfLink": {"description": "Output only. A URL that can be used to access this resource again.", "readOnly": true, "type": "string"}, "snapshotDefinition": {"$ref": "SnapshotDefinition", "description": "Output only. Contains information about the snapshot. This value is set via snapshot creation.", "readOnly": true}, "streamingBuffer": {"$ref": "Streamingbuffer", "description": "Output only. Contains information regarding this table's streaming buffer, if one is present. This field will be absent if the table is not being streamed to or if there is no data in the streaming buffer.", "readOnly": true}, "tableConstraints": {"$ref": "TableConstraints", "description": "Optional. Tables Primary Key and Foreign Key information"}, "tableReference": {"$ref": "TableReference", "description": "Required. Reference describing the ID of this table."}, "tableReplicationInfo": {"$ref": "TableReplicationInfo", "description": "Optional. Table replication info for table created `AS REPLICA` DDL like: `CREATE MATERIALIZED VIEW mv1 AS REPLICA OF src_mv`"}, "timePartitioning": {"$ref": "TimePartitioning", "description": "If specified, configures time-based partitioning for this table."}, "type": {"description": "Output only. Describes the table type. The following values are supported: * `TABLE`: A normal BigQuery table. * `VIEW`: A virtual table defined by a SQL query. * `EXTERNAL`: A table that references data stored in an external storage system, such as Google Cloud Storage. * `MATERIALIZED_VIEW`: A precomputed view defined by a SQL query. * `SNAPSHOT`: An immutable BigQuery table that preserves the contents of a base table at a particular time. See additional information on [table snapshots](https://cloud.google.com/bigquery/docs/table-snapshots-intro). The default value is `TABLE`.", "readOnly": true, "type": "string"}, "view": {"$ref": "ViewDefinition", "description": "Optional. The view definition."}}, "type": "object"}, "TableCell": {"id": "TableCell", "properties": {"v": {"type": "any"}}, "type": "object"}, "TableConstraints": {"description": "The TableConstraints defines the primary key and foreign key.", "id": "TableConstraints", "properties": {"foreignKeys": {"description": "Optional. Present only if the table has a foreign key. The foreign key is not enforced.", "items": {"description": "Represents a foreign key constraint on a table's columns.", "properties": {"columnReferences": {"description": "Required. The columns that compose the foreign key.", "items": {"description": "The pair of the foreign key column and primary key column.", "properties": {"referencedColumn": {"description": "Required. The column in the primary key that are referenced by the referencing_column.", "type": "string"}, "referencingColumn": {"description": "Required. The column that composes the foreign key.", "type": "string"}}, "type": "object"}, "type": "array"}, "name": {"description": "Optional. Set only if the foreign key constraint is named.", "type": "string"}, "referencedTable": {"properties": {"datasetId": {"type": "string"}, "projectId": {"type": "string"}, "tableId": {"type": "string"}}, "type": "object"}}, "type": "object"}, "type": "array"}, "primaryKey": {"description": "Represents the primary key constraint on a table's columns.", "properties": {"columns": {"description": "Required. The columns that are composed of the primary key constraint.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}}, "type": "object"}, "TableDataInsertAllRequest": {"description": "Request for sending a single streaming insert.", "id": "TableDataInsertAllRequest", "properties": {"ignoreUnknownValues": {"description": "Optional. Accept rows that contain values that do not match the schema. The unknown values are ignored. Default is false, which treats unknown values as errors.", "type": "boolean"}, "kind": {"default": "bigquery#tableDataInsertAllRequest", "description": "Optional. The resource type of the response. The value is not checked at the backend. Historically, it has been set to \"bigquery#tableDataInsertAllRequest\" but you are not required to set it.", "type": "string"}, "rows": {"items": {"description": "Data for a single insertion row.", "properties": {"insertId": {"description": "Insertion ID for best-effort deduplication. This feature is not recommended, and users seeking stronger insertion semantics are encouraged to use other mechanisms such as the BigQuery Write API.", "type": "string"}, "json": {"$ref": "JsonObject", "description": "Data for a single row."}}, "type": "object"}, "type": "array"}, "skipInvalidRows": {"description": "Optional. Insert all valid rows of a request, even if invalid rows exist. The default value is false, which causes the entire request to fail if any invalid rows exist.", "type": "boolean"}, "templateSuffix": {"description": "Optional. If specified, treats the destination table as a base template, and inserts the rows into an instance table named \"{destination}{templateSuffix}\". BigQuery will manage creation of the instance table, using the schema of the base template table. See https://cloud.google.com/bigquery/streaming-data-into-bigquery#template-tables for considerations when working with templates tables.", "type": "string"}, "traceId": {"description": "Optional. Unique request trace id. Used for debugging purposes only. It is case-sensitive, limited to up to 36 ASCII characters. A UUID is recommended.", "type": "string"}}, "type": "object"}, "TableDataInsertAllResponse": {"description": "Describes the format of a streaming insert response.", "id": "TableDataInsertAllResponse", "properties": {"insertErrors": {"description": "Describes specific errors encountered while processing the request.", "items": {"description": "Error details about a single row's insertion.", "properties": {"errors": {"description": "Error information for the row indicated by the index property.", "items": {"$ref": "<PERSON><PERSON>r<PERSON><PERSON><PERSON>"}, "type": "array"}, "index": {"description": "The index of the row that error applies to.", "format": "uint32", "type": "integer"}}, "type": "object"}, "type": "array"}, "kind": {"default": "bigquery#tableDataInsertAllResponse", "description": "Returns \"bigquery#tableDataInsertAllResponse\".", "type": "string"}}, "type": "object"}, "TableDataList": {"id": "TableDataList", "properties": {"etag": {"description": "A hash of this page of results.", "type": "string"}, "kind": {"default": "bigquery#tableDataList", "description": "The resource type of the response.", "type": "string"}, "pageToken": {"description": "A token used for paging results. Providing this token instead of the startIndex parameter can help you retrieve stable results when an underlying table is changing.", "type": "string"}, "rows": {"description": "Rows of results.", "items": {"$ref": "TableRow"}, "type": "array"}, "totalRows": {"description": "Total rows of the entire table. In order to show default value 0 we have to present it as string.", "format": "int64", "type": "string"}}, "type": "object"}, "TableFieldSchema": {"description": "A field in TableSchema", "id": "TableFieldSchema", "properties": {"categories": {"description": "Deprecated.", "properties": {"names": {"description": "Deprecated.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "collation": {"description": "Optional. Field collation can be set only when the type of field is STRING. The following values are supported: * 'und:ci': undetermined locale, case insensitive. * '': empty string. Default to case-sensitive behavior.", "type": "string"}, "dataPolicies": {"description": "Optional. Data policy options, will replace the data_policies.", "items": {"$ref": "DataPolicyOption"}, "type": "array"}, "defaultValueExpression": {"description": "Optional. A SQL expression to specify the [default value] (https://cloud.google.com/bigquery/docs/default-values) for this field.", "type": "string"}, "description": {"description": "Optional. The field description. The maximum length is 1,024 characters.", "type": "string"}, "fields": {"description": "Optional. Describes the nested schema fields if the type property is set to RECORD.", "items": {"$ref": "TableFieldSchema"}, "type": "array"}, "foreignTypeDefinition": {"description": "Optional. Definition of the foreign data type. Only valid for top-level schema fields (not nested fields). If the type is FOREIGN, this field is required.", "type": "string"}, "maxLength": {"description": "Optional. Maximum length of values of this field for STRINGS or BYTES. If max_length is not specified, no maximum length constraint is imposed on this field. If type = \"STRING\", then max_length represents the maximum UTF-8 length of strings in this field. If type = \"BYTES\", then max_length represents the maximum number of bytes in this field. It is invalid to set this field if type ≠ \"STRING\" and ≠ \"BYTES\".", "format": "int64", "type": "string"}, "mode": {"description": "Optional. The field mode. Possible values include NULL<PERSON>LE, REQUIRED and REPEATED. The default value is NULLABLE.", "type": "string"}, "name": {"description": "Required. The field name. The name must contain only letters (a-z, A-Z), numbers (0-9), or underscores (_), and must start with a letter or underscore. The maximum length is 300 characters.", "type": "string"}, "policyTags": {"description": "Optional. The policy tags attached to this field, used for field-level access control. If not set, defaults to empty policy_tags.", "properties": {"names": {"description": "A list of policy tag resource names. For example, \"projects/1/locations/eu/taxonomies/2/policyTags/3\". At most 1 policy tag is currently allowed.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "precision": {"description": "Optional. Precision (maximum number of total digits in base 10) and scale (maximum number of digits in the fractional part in base 10) constraints for values of this field for NUMERIC or BIGNUMERIC. It is invalid to set precision or scale if type ≠ \"NUMERIC\" and ≠ \"BIG<PERSON>MERI<PERSON>\". If precision and scale are not specified, no value range constraint is imposed on this field insofar as values are permitted by the type. Values of this NUMERIC or BIGNUMERIC field must be in this range when: * Precision (P) and scale (S) are specified: [-10P-S + 10-S, 10P-S - 10-S] * Precision (P) is specified but not scale (and thus scale is interpreted to be equal to zero): [-10P + 1, 10P - 1]. Acceptable values for precision and scale if both are specified: * If type = \"NUMERIC\": 1 ≤ precision - scale ≤ 29 and 0 ≤ scale ≤ 9. * If type = \"BIGNUMERIC\": 1 ≤ precision - scale ≤ 38 and 0 ≤ scale ≤ 38. Acceptable values for precision if only precision is specified but not scale (and thus scale is interpreted to be equal to zero): * If type = \"NUMERIC\": 1 ≤ precision ≤ 29. * If type = \"BIG<PERSON><PERSON>RI<PERSON>\": 1 ≤ precision ≤ 38. If scale is specified but not precision, then it is invalid.", "format": "int64", "type": "string"}, "rangeElementType": {"description": "Represents the type of a field element.", "properties": {"type": {"description": "Required. The type of a field element. For more information, see TableFieldSchema.type.", "type": "string"}}, "type": "object"}, "roundingMode": {"description": "Optional. Specifies the rounding mode to be used when storing values of NUMERIC and BIGNUMERIC type.", "enum": ["ROUNDING_MODE_UNSPECIFIED", "ROUND_HALF_AWAY_FROM_ZERO", "ROUND_HALF_EVEN"], "enumDescriptions": ["Unspecified will default to using ROUND_HALF_AWAY_FROM_ZERO.", "ROUND_HALF_AWAY_FROM_ZERO rounds half values away from zero when applying precision and scale upon writing of NUMERIC and BIGNUMERIC values. For Scale: 0 1.1, 1.2, 1.3, 1.4 => 1 1.5, 1.6, 1.7, 1.8, 1.9 => 2", "ROUND_HALF_EVEN rounds half values to the nearest even value when applying precision and scale upon writing of NUMERIC and BIGNUMERIC values. For Scale: 0 1.1, 1.2, 1.3, 1.4 => 1 1.5 => 2 1.6, 1.7, 1.8, 1.9 => 2 2.5 => 2"], "type": "string"}, "scale": {"description": "Optional. See documentation for precision.", "format": "int64", "type": "string"}, "type": {"description": "Required. The field data type. Possible values include: * STRING * BYTES * INTEGER (or INT64) * FLOAT (or FLOAT64) * BOOLEAN (or BOOL) * TIMESTAMP * DATE * TIME * DATETIME * GEOGRAPHY * NUMERIC * BIGNUMERIC * JSON * RECORD (or STRUCT) * RANGE Use of RECORD/STRUCT indicates that the field contains a nested schema.", "type": "string"}}, "type": "object"}, "TableList": {"description": "Partial projection of the metadata for a given table in a list response.", "id": "TableList", "properties": {"etag": {"description": "A hash of this page of results.", "type": "string"}, "kind": {"default": "bigquery#tableList", "description": "The type of list.", "type": "string"}, "nextPageToken": {"description": "A token to request the next page of results.", "type": "string"}, "tables": {"description": "Tables in the requested dataset.", "items": {"properties": {"clustering": {"$ref": "Clustering", "description": "Clustering specification for this table, if configured."}, "creationTime": {"description": "Output only. The time when this table was created, in milliseconds since the epoch.", "format": "int64", "readOnly": true, "type": "string"}, "expirationTime": {"description": "The time when this table expires, in milliseconds since the epoch. If not present, the table will persist indefinitely. Expired tables will be deleted and their storage reclaimed.", "format": "int64", "type": "string"}, "friendlyName": {"description": "The user-friendly name for this table.", "type": "string"}, "id": {"description": "An opaque ID of the table.", "type": "string"}, "kind": {"description": "The resource type.", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "The labels associated with this table. You can use these to organize and group your tables.", "type": "object"}, "rangePartitioning": {"$ref": "RangePartitioning", "description": "The range partitioning for this table."}, "requirePartitionFilter": {"default": "false", "description": "Optional. If set to true, queries including this table must specify a partition filter. This filter is used for partition elimination.", "type": "boolean"}, "tableReference": {"$ref": "TableReference", "description": "A reference uniquely identifying table."}, "timePartitioning": {"$ref": "TimePartitioning", "description": "The time-based partitioning for this table."}, "type": {"description": "The type of table.", "type": "string"}, "view": {"description": "Information about a logical view.", "properties": {"privacyPolicy": {"$ref": "PrivacyPolicy", "description": "Specifies the privacy policy for the view."}, "useLegacySql": {"description": "True if view is defined in legacy SQL dialect, false if in GoogleSQL.", "type": "boolean"}}, "type": "object"}}, "type": "object"}, "type": "array"}, "totalItems": {"description": "The total number of tables in the dataset.", "format": "int32", "type": "integer"}}, "type": "object"}, "TableMetadataCacheUsage": {"description": "Table level detail on the usage of metadata caching. Only set for Metadata caching eligible tables referenced in the query.", "id": "TableMetadataCacheUsage", "properties": {"explanation": {"description": "Free form human-readable reason metadata caching was unused for the job.", "type": "string"}, "staleness": {"description": "Duration since last refresh as of this job for managed tables (indicates metadata cache staleness as seen by this job).", "format": "google-duration", "type": "string"}, "tableReference": {"$ref": "TableReference", "description": "Metadata caching eligible table referenced in the query."}, "tableType": {"description": "[Table type](https://cloud.google.com/bigquery/docs/reference/rest/v2/tables#Table.FIELDS.type).", "type": "string"}, "unusedReason": {"description": "Reason for not using metadata caching for the table.", "enum": ["UNUSED_REASON_UNSPECIFIED", "EXCEEDED_MAX_STALENESS", "METADATA_CACHING_NOT_ENABLED", "OTHER_REASON"], "enumDescriptions": ["Unused reasons not specified.", "Metadata cache was outside the table's maxStaleness.", "Metadata caching feature is not enabled. [Update BigLake tables] (/bigquery/docs/create-cloud-storage-table-biglake#update-biglake-tables) to enable the metadata caching.", "Other unknown reason."], "type": "string"}}, "type": "object"}, "TableReference": {"id": "TableReference", "properties": {"datasetId": {"description": "Required. The ID of the dataset containing this table.", "type": "string"}, "projectId": {"description": "Required. The ID of the project containing this table.", "type": "string"}, "tableId": {"description": "Required. The ID of the table. The ID can contain Unicode characters in category L (letter), M (mark), N (number), Pc (connector, including underscore), Pd (dash), and Zs (space). For more information, see [General Category](https://wikipedia.org/wiki/Unicode_character_property#General_Category). The maximum length is 1,024 characters. Certain operations allow suffixing of the table ID with a partition decorator, such as `sample_table$20190123`.", "type": "string"}}, "type": "object"}, "TableReplicationInfo": {"description": "Replication info of a table created using `AS REPLICA` DDL like: `CREATE MATERIALIZED VIEW mv1 AS REPLICA OF src_mv`", "id": "TableReplicationInfo", "properties": {"replicatedSourceLastRefreshTime": {"description": "Optional. Output only. If source is a materialized view, this field signifies the last refresh time of the source.", "format": "int64", "readOnly": true, "type": "string"}, "replicationError": {"$ref": "<PERSON><PERSON>r<PERSON><PERSON><PERSON>", "description": "Optional. Output only. Replication error that will permanently stopped table replication.", "readOnly": true}, "replicationIntervalMs": {"description": "Optional. Specifies the interval at which the source table is polled for updates. It's Optional. If not specified, default replication interval would be applied.", "format": "int64", "type": "string"}, "replicationStatus": {"description": "Optional. Output only. Replication status of configured replication.", "enum": ["REPLICATION_STATUS_UNSPECIFIED", "ACTIVE", "SOURCE_DELETED", "PERMISSION_DENIED", "UNSUPPORTED_CONFIGURATION"], "enumDescriptions": ["Default value.", "Replication is Active with no errors.", "Source object is deleted.", "Source revoked replication permissions.", "Source configuration doesn’t allow replication."], "readOnly": true, "type": "string"}, "sourceTable": {"$ref": "TableReference", "description": "Required. Source table reference that is replicated."}}, "type": "object"}, "TableRow": {"id": "TableRow", "properties": {"f": {"description": "Represents a single row in the result set, consisting of one or more fields.", "items": {"$ref": "TableCell"}, "type": "array"}}, "type": "object"}, "TableSchema": {"description": "Schema of a table", "id": "TableSchema", "properties": {"fields": {"description": "Describes the fields in a table.", "items": {"$ref": "TableFieldSchema"}, "type": "array"}, "foreignTypeInfo": {"$ref": "ForeignTypeInfo", "description": "Optional. Specifies metadata of the foreign data type definition in field schema (TableFieldSchema.foreign_type_definition)."}}, "type": "object"}, "TestIamPermissionsRequest": {"description": "Request message for `TestIamPermissions` method.", "id": "TestIamPermissionsRequest", "properties": {"permissions": {"description": "The set of permissions to check for the `resource`. Permissions with wildcards (such as `*` or `storage.*`) are not allowed. For more information see [IAM Overview](https://cloud.google.com/iam/docs/overview#permissions).", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "TestIamPermissionsResponse": {"description": "Response message for `TestIamPermissions` method.", "id": "TestIamPermissionsResponse", "properties": {"permissions": {"description": "A subset of `TestPermissionsRequest.permissions` that the caller is allowed.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "TimePartitioning": {"id": "TimePartitioning", "properties": {"expirationMs": {"description": "Optional. Number of milliseconds for which to keep the storage for a partition. A wrapper is used here because 0 is an invalid value.", "format": "int64", "type": "string"}, "field": {"description": "Optional. If not set, the table is partitioned by pseudo column '_PARTITIONTIME'; if set, the table is partitioned by this field. The field must be a top-level TIMESTAMP or DATE field. Its mode must be NULLABLE or REQUIRED. A wrapper is used here because an empty string is an invalid value.", "type": "string"}, "requirePartitionFilter": {"default": "false", "deprecated": true, "description": "If set to true, queries over this table require a partition filter that can be used for partition elimination to be specified. This field is deprecated; please set the field with the same name on the table itself instead. This field needs a wrapper because we want to output the default value, false, if the user explicitly set it.", "type": "boolean"}, "type": {"description": "Required. The supported types are DAY, HOUR, MONTH, and YEAR, which will generate one partition per day, hour, month, and year, respectively.", "type": "string"}}, "type": "object"}, "TrainingOptions": {"description": "Options used in model training.", "id": "TrainingOptions", "properties": {"activationFn": {"description": "Activation function of the neural nets.", "type": "string"}, "adjustStepChanges": {"description": "If true, detect step changes and make data adjustment in the input time series.", "type": "boolean"}, "approxGlobalFeatureContrib": {"description": "Whether to use approximate feature contribution method in XGBoost model explanation for global explain.", "type": "boolean"}, "autoArima": {"description": "Whether to enable auto ARIMA or not.", "type": "boolean"}, "autoArimaMaxOrder": {"description": "The max value of the sum of non-seasonal p and q.", "format": "int64", "type": "string"}, "autoArimaMinOrder": {"description": "The min value of the sum of non-seasonal p and q.", "format": "int64", "type": "string"}, "autoClassWeights": {"description": "Whether to calculate class weights automatically based on the popularity of each label.", "type": "boolean"}, "batchSize": {"description": "Batch size for dnn models.", "format": "int64", "type": "string"}, "boosterType": {"description": "Booster type for boosted tree models.", "enum": ["BOOSTER_TYPE_UNSPECIFIED", "GBTREE", "DART"], "enumDescriptions": ["Unspecified booster type.", "Gbtree booster.", "Dart booster."], "type": "string"}, "budgetHours": {"description": "Budget in hours for AutoML training.", "format": "double", "type": "number"}, "calculatePValues": {"description": "Whether or not p-value test should be computed for this model. Only available for linear and logistic regression models.", "type": "boolean"}, "categoryEncodingMethod": {"description": "Categorical feature encoding method.", "enum": ["ENCODING_METHOD_UNSPECIFIED", "ONE_HOT_ENCODING", "LABEL_ENCODING", "DUMMY_ENCODING"], "enumDescriptions": ["Unspecified encoding method.", "Applies one-hot encoding.", "Applies label encoding.", "Applies dummy encoding."], "type": "string"}, "cleanSpikesAndDips": {"description": "If true, clean spikes and dips in the input time series.", "type": "boolean"}, "colorSpace": {"description": "Enums for color space, used for processing images in Object Table. See more details at https://www.tensorflow.org/io/tutorials/colorspace.", "enum": ["COLOR_SPACE_UNSPECIFIED", "RGB", "HSV", "YIQ", "YUV", "GRAYSCALE"], "enumDescriptions": ["Unspecified color space", "RGB", "HSV", "YIQ", "YUV", "GRAYSCALE"], "type": "string"}, "colsampleBylevel": {"description": "Subsample ratio of columns for each level for boosted tree models.", "format": "double", "type": "number"}, "colsampleBynode": {"description": "Subsample ratio of columns for each node(split) for boosted tree models.", "format": "double", "type": "number"}, "colsampleBytree": {"description": "Subsample ratio of columns when constructing each tree for boosted tree models.", "format": "double", "type": "number"}, "contributionMetric": {"description": "The contribution metric. Applies to contribution analysis models. Allowed formats supported are for summable and summable ratio contribution metrics. These include expressions such as `SUM(x)` or `SUM(x)/SUM(y)`, where x and y are column names from the base table.", "type": "string"}, "dartNormalizeType": {"description": "Type of normalization algorithm for boosted tree models using dart booster.", "enum": ["DART_NORMALIZE_TYPE_UNSPECIFIED", "TREE", "FOREST"], "enumDescriptions": ["Unspecified dart normalize type.", "New trees have the same weight of each of dropped trees.", "New trees have the same weight of sum of dropped trees."], "type": "string"}, "dataFrequency": {"description": "The data frequency of a time series.", "enum": ["DATA_FREQUENCY_UNSPECIFIED", "AUTO_FREQUENCY", "YEARLY", "QUARTERLY", "MONTHLY", "WEEKLY", "DAILY", "HOURLY", "PER_MINUTE"], "enumDescriptions": ["Default value.", "Automatically inferred from timestamps.", "Yearly data.", "Quarterly data.", "Monthly data.", "Weekly data.", "Daily data.", "Hourly data.", "Per-minute data."], "type": "string"}, "dataSplitColumn": {"description": "The column to split data with. This column won't be used as a feature. 1. When data_split_method is CUSTOM, the corresponding column should be boolean. The rows with true value tag are eval data, and the false are training data. 2. When data_split_method is SEQ, the first DATA_SPLIT_EVAL_FRACTION rows (from smallest to largest) in the corresponding column are used as training data, and the rest are eval data. It respects the order in Orderable data types: https://cloud.google.com/bigquery/docs/reference/standard-sql/data-types#data_type_properties", "type": "string"}, "dataSplitEvalFraction": {"description": "The fraction of evaluation data over the whole input data. The rest of data will be used as training data. The format should be double. Accurate to two decimal places. Default value is 0.2.", "format": "double", "type": "number"}, "dataSplitMethod": {"description": "The data split type for training and evaluation, e.g. RANDOM.", "enum": ["DATA_SPLIT_METHOD_UNSPECIFIED", "RANDOM", "CUSTOM", "SEQUENTIAL", "NO_SPLIT", "AUTO_SPLIT"], "enumDescriptions": ["Default value.", "Splits data randomly.", "Splits data with the user provided tags.", "Splits data sequentially.", "Data split will be skipped.", "Splits data automatically: Uses NO_SPLIT if the data size is small. Otherwise uses RANDOM."], "type": "string"}, "decomposeTimeSeries": {"description": "If true, perform decompose time series and save the results.", "type": "boolean"}, "dimensionIdColumns": {"description": "Optional. Names of the columns to slice on. Applies to contribution analysis models.", "items": {"type": "string"}, "type": "array"}, "distanceType": {"description": "Distance type for clustering models.", "enum": ["DISTANCE_TYPE_UNSPECIFIED", "EUCLIDEAN", "COSINE"], "enumDescriptions": ["Default value.", "Eculidean distance.", "Cosine distance."], "type": "string"}, "dropout": {"description": "Dropout probability for dnn models.", "format": "double", "type": "number"}, "earlyStop": {"description": "Whether to stop early when the loss doesn't improve significantly any more (compared to min_relative_progress). Used only for iterative training algorithms.", "type": "boolean"}, "enableGlobalExplain": {"description": "If true, enable global explanation during training.", "type": "boolean"}, "feedbackType": {"description": "Feedback type that specifies which algorithm to run for matrix factorization.", "enum": ["FEEDBACK_TYPE_UNSPECIFIED", "IMPLICIT", "EXPLICIT"], "enumDescriptions": ["Default value.", "Use weighted-als for implicit feedback problems.", "Use nonweighted-als for explicit feedback problems."], "type": "string"}, "fitIntercept": {"description": "Whether the model should include intercept during model training.", "type": "boolean"}, "forecastLimitLowerBound": {"description": "The forecast limit lower bound that was used during ARIMA model training with limits. To see more details of the algorithm: https://otexts.com/fpp2/limits.html", "format": "double", "type": "number"}, "forecastLimitUpperBound": {"description": "The forecast limit upper bound that was used during ARIMA model training with limits.", "format": "double", "type": "number"}, "hiddenUnits": {"description": "Hidden units for dnn models.", "items": {"format": "int64", "type": "string"}, "type": "array"}, "holidayRegion": {"description": "The geographical region based on which the holidays are considered in time series modeling. If a valid value is specified, then holiday effects modeling is enabled.", "enum": ["HOLIDAY_REGION_UNSPECIFIED", "GLOBAL", "NA", "JAPAC", "EMEA", "LAC", "AE", "AR", "AT", "AU", "BE", "BR", "CA", "CH", "CL", "CN", "CO", "CS", "CZ", "DE", "DK", "DZ", "EC", "EE", "EG", "ES", "FI", "FR", "GB", "GR", "HK", "HU", "ID", "IE", "IL", "IN", "IR", "IT", "JP", "KR", "LV", "MA", "MX", "MY", "NG", "NL", "NO", "NZ", "PE", "PH", "PK", "PL", "PT", "RO", "RS", "RU", "SA", "SE", "SG", "SI", "SK", "TH", "TR", "TW", "UA", "US", "VE", "VN", "ZA"], "enumDescriptions": ["Holiday region unspecified.", "Global.", "North America.", "Japan and Asia Pacific: Korea, Greater China, India, Australia, and New Zealand.", "Europe, the Middle East and Africa.", "Latin America and the Caribbean.", "United Arab Emirates", "Argentina", "Austria", "Australia", "Belgium", "Brazil", "Canada", "Switzerland", "Chile", "China", "Colombia", "Czechoslovakia", "Czech Republic", "Germany", "Denmark", "Algeria", "Ecuador", "Estonia", "Egypt", "Spain", "Finland", "France", "Great Britain (United Kingdom)", "Greece", "Hong Kong", "Hungary", "Indonesia", "Ireland", "Israel", "India", "Iran", "Italy", "Japan", "Korea (South)", "Latvia", "Morocco", "Mexico", "Malaysia", "Nigeria", "Netherlands", "Norway", "New Zealand", "Peru", "Philippines", "Pakistan", "Poland", "Portugal", "Romania", "Serbia", "Russian Federation", "Saudi Arabia", "Sweden", "Singapore", "Slovenia", "Slovakia", "Thailand", "Turkey", "Taiwan", "Ukraine", "United States", "Venezuela", "Vietnam", "South Africa"], "type": "string"}, "holidayRegions": {"description": "A list of geographical regions that are used for time series modeling.", "items": {"enum": ["HOLIDAY_REGION_UNSPECIFIED", "GLOBAL", "NA", "JAPAC", "EMEA", "LAC", "AE", "AR", "AT", "AU", "BE", "BR", "CA", "CH", "CL", "CN", "CO", "CS", "CZ", "DE", "DK", "DZ", "EC", "EE", "EG", "ES", "FI", "FR", "GB", "GR", "HK", "HU", "ID", "IE", "IL", "IN", "IR", "IT", "JP", "KR", "LV", "MA", "MX", "MY", "NG", "NL", "NO", "NZ", "PE", "PH", "PK", "PL", "PT", "RO", "RS", "RU", "SA", "SE", "SG", "SI", "SK", "TH", "TR", "TW", "UA", "US", "VE", "VN", "ZA"], "enumDescriptions": ["Holiday region unspecified.", "Global.", "North America.", "Japan and Asia Pacific: Korea, Greater China, India, Australia, and New Zealand.", "Europe, the Middle East and Africa.", "Latin America and the Caribbean.", "United Arab Emirates", "Argentina", "Austria", "Australia", "Belgium", "Brazil", "Canada", "Switzerland", "Chile", "China", "Colombia", "Czechoslovakia", "Czech Republic", "Germany", "Denmark", "Algeria", "Ecuador", "Estonia", "Egypt", "Spain", "Finland", "France", "Great Britain (United Kingdom)", "Greece", "Hong Kong", "Hungary", "Indonesia", "Ireland", "Israel", "India", "Iran", "Italy", "Japan", "Korea (South)", "Latvia", "Morocco", "Mexico", "Malaysia", "Nigeria", "Netherlands", "Norway", "New Zealand", "Peru", "Philippines", "Pakistan", "Poland", "Portugal", "Romania", "Serbia", "Russian Federation", "Saudi Arabia", "Sweden", "Singapore", "Slovenia", "Slovakia", "Thailand", "Turkey", "Taiwan", "Ukraine", "United States", "Venezuela", "Vietnam", "South Africa"], "type": "string"}, "type": "array"}, "horizon": {"description": "The number of periods ahead that need to be forecasted.", "format": "int64", "type": "string"}, "hparamTuningObjectives": {"description": "The target evaluation metrics to optimize the hyperparameters for.", "items": {"enum": ["HPARAM_TUNING_OBJECTIVE_UNSPECIFIED", "MEAN_ABSOLUTE_ERROR", "MEAN_SQUARED_ERROR", "MEAN_SQUARED_LOG_ERROR", "MEDIAN_ABSOLUTE_ERROR", "R_SQUARED", "EXPLAINED_VARIANCE", "PRECISION", "RECALL", "ACCURACY", "F1_SCORE", "LOG_LOSS", "ROC_AUC", "DAVIES_BOULDIN_INDEX", "MEAN_AVERAGE_PRECISION", "NORMALIZED_DISCOUNTED_CUMULATIVE_GAIN", "AVERAGE_RANK"], "enumDescriptions": ["Unspecified evaluation metric.", "Mean absolute error. mean_absolute_error = AVG(ABS(label - predicted))", "Mean squared error. mean_squared_error = AVG(POW(label - predicted, 2))", "Mean squared log error. mean_squared_log_error = AVG(POW(LN(1 + label) - LN(1 + predicted), 2))", "Mean absolute error. median_absolute_error = APPROX_QUANTILES(absolute_error, 2)[OFFSET(1)]", "R^2 score. This corresponds to r2_score in ML.EVALUATE. r_squared = 1 - SUM(squared_error)/(COUNT(label)*VAR_POP(label))", "Explained variance. explained_variance = 1 - VAR_POP(label_error)/VAR_POP(label)", "Precision is the fraction of actual positive predictions that had positive actual labels. For multiclass this is a macro-averaged metric treating each class as a binary classifier.", "Recall is the fraction of actual positive labels that were given a positive prediction. For multiclass this is a macro-averaged metric.", "Accuracy is the fraction of predictions given the correct label. For multiclass this is a globally micro-averaged metric.", "The F1 score is an average of recall and precision. For multiclass this is a macro-averaged metric.", "Logarithmic Loss. For multiclass this is a macro-averaged metric.", "Area Under an ROC Curve. For multiclass this is a macro-averaged metric.", "Davies-Bouldin Index.", "Mean Average Precision.", "Normalized Discounted Cumulative Gain.", "Average Rank."], "type": "string"}, "type": "array"}, "includeDrift": {"description": "Include drift when fitting an ARIMA model.", "type": "boolean"}, "initialLearnRate": {"description": "Specifies the initial learning rate for the line search learn rate strategy.", "format": "double", "type": "number"}, "inputLabelColumns": {"description": "Name of input label columns in training data.", "items": {"type": "string"}, "type": "array"}, "instanceWeightColumn": {"description": "Name of the instance weight column for training data. This column isn't be used as a feature.", "type": "string"}, "integratedGradientsNumSteps": {"description": "Number of integral steps for the integrated gradients explain method.", "format": "int64", "type": "string"}, "isTestColumn": {"description": "Name of the column used to determine the rows corresponding to control and test. Applies to contribution analysis models.", "type": "string"}, "itemColumn": {"description": "Item column specified for matrix factorization models.", "type": "string"}, "kmeansInitializationColumn": {"description": "The column used to provide the initial centroids for kmeans algorithm when kmeans_initialization_method is CUSTOM.", "type": "string"}, "kmeansInitializationMethod": {"description": "The method used to initialize the centroids for kmeans algorithm.", "enum": ["KMEANS_INITIALIZATION_METHOD_UNSPECIFIED", "RANDOM", "CUSTOM", "KMEANS_PLUS_PLUS"], "enumDescriptions": ["Unspecified initialization method.", "Initializes the centroids randomly.", "Initializes the centroids using data specified in kmeans_initialization_column.", "Initializes with kmeans++."], "type": "string"}, "l1RegActivation": {"description": "L1 regularization coefficient to activations.", "format": "double", "type": "number"}, "l1Regularization": {"description": "L1 regularization coefficient.", "format": "double", "type": "number"}, "l2Regularization": {"description": "L2 regularization coefficient.", "format": "double", "type": "number"}, "labelClassWeights": {"additionalProperties": {"format": "double", "type": "number"}, "description": "Weights associated with each label class, for rebalancing the training data. Only applicable for classification models.", "type": "object"}, "learnRate": {"description": "Learning rate in training. Used only for iterative training algorithms.", "format": "double", "type": "number"}, "learnRateStrategy": {"description": "The strategy to determine learn rate for the current iteration.", "enum": ["LEARN_RATE_STRATEGY_UNSPECIFIED", "LINE_SEARCH", "CONSTANT"], "enumDescriptions": ["Default value.", "Use line search to determine learning rate.", "Use a constant learning rate."], "type": "string"}, "lossType": {"description": "Type of loss function used during training run.", "enum": ["LOSS_TYPE_UNSPECIFIED", "MEAN_SQUARED_LOSS", "MEAN_LOG_LOSS"], "enumDescriptions": ["Default value.", "Mean squared loss, used for linear regression.", "Mean log loss, used for logistic regression."], "type": "string"}, "maxIterations": {"description": "The maximum number of iterations in training. Used only for iterative training algorithms.", "format": "int64", "type": "string"}, "maxParallelTrials": {"description": "Maximum number of trials to run in parallel.", "format": "int64", "type": "string"}, "maxTimeSeriesLength": {"description": "The maximum number of time points in a time series that can be used in modeling the trend component of the time series. Don't use this option with the `timeSeriesLengthFraction` or `minTimeSeriesLength` options.", "format": "int64", "type": "string"}, "maxTreeDepth": {"description": "Maximum depth of a tree for boosted tree models.", "format": "int64", "type": "string"}, "minAprioriSupport": {"description": "The apriori support minimum. Applies to contribution analysis models.", "format": "double", "type": "number"}, "minRelativeProgress": {"description": "When early_stop is true, stops training when accuracy improvement is less than 'min_relative_progress'. Used only for iterative training algorithms.", "format": "double", "type": "number"}, "minSplitLoss": {"description": "Minimum split loss for boosted tree models.", "format": "double", "type": "number"}, "minTimeSeriesLength": {"description": "The minimum number of time points in a time series that are used in modeling the trend component of the time series. If you use this option you must also set the `timeSeriesLengthFraction` option. This training option ensures that enough time points are available when you use `timeSeriesLengthFraction` in trend modeling. This is particularly important when forecasting multiple time series in a single query using `timeSeriesIdColumn`. If the total number of time points is less than the `minTimeSeriesLength` value, then the query uses all available time points.", "format": "int64", "type": "string"}, "minTreeChildWeight": {"description": "Minimum sum of instance weight needed in a child for boosted tree models.", "format": "int64", "type": "string"}, "modelRegistry": {"description": "The model registry.", "enum": ["MODEL_REGISTRY_UNSPECIFIED", "VERTEX_AI"], "enumDescriptions": ["Default value.", "Vertex AI."], "type": "string"}, "modelUri": {"description": "Google Cloud Storage URI from which the model was imported. Only applicable for imported models.", "type": "string"}, "nonSeasonalOrder": {"$ref": "ArimaOrder", "description": "A specification of the non-seasonal part of the ARIMA model: the three components (p, d, q) are the AR order, the degree of differencing, and the MA order."}, "numClusters": {"description": "Number of clusters for clustering models.", "format": "int64", "type": "string"}, "numFactors": {"description": "Num factors specified for matrix factorization models.", "format": "int64", "type": "string"}, "numParallelTree": {"description": "Number of parallel trees constructed during each iteration for boosted tree models.", "format": "int64", "type": "string"}, "numPrincipalComponents": {"description": "Number of principal components to keep in the PCA model. Must be <= the number of features.", "format": "int64", "type": "string"}, "numTrials": {"description": "Number of trials to run this hyperparameter tuning job.", "format": "int64", "type": "string"}, "optimizationStrategy": {"description": "Optimization strategy for training linear regression models.", "enum": ["OPTIMIZATION_STRATEGY_UNSPECIFIED", "BATCH_GRADIENT_DESCENT", "NORMAL_EQUATION"], "enumDescriptions": ["Default value.", "Uses an iterative batch gradient descent algorithm.", "Uses a normal equation to solve linear regression problem."], "type": "string"}, "optimizer": {"description": "Optimizer used for training the neural nets.", "type": "string"}, "pcaExplainedVarianceRatio": {"description": "The minimum ratio of cumulative explained variance that needs to be given by the PCA model.", "format": "double", "type": "number"}, "pcaSolver": {"description": "The solver for PCA.", "enum": ["UNSPECIFIED", "FULL", "RANDOMIZED", "AUTO"], "enumDescriptions": ["Default value.", "Full eigen-decoposition.", "Randomized SVD.", "Auto."], "type": "string"}, "sampledShapleyNumPaths": {"description": "Number of paths for the sampled <PERSON><PERSON><PERSON><PERSON> explain method.", "format": "int64", "type": "string"}, "scaleFeatures": {"description": "If true, scale the feature values by dividing the feature standard deviation. Currently only apply to PCA.", "type": "boolean"}, "standardizeFeatures": {"description": "Whether to standardize numerical features. Default to true.", "type": "boolean"}, "subsample": {"description": "Subsample fraction of the training data to grow tree to prevent overfitting for boosted tree models.", "format": "double", "type": "number"}, "tfVersion": {"description": "Based on the selected TF version, the corresponding docker image is used to train external models.", "type": "string"}, "timeSeriesDataColumn": {"description": "Column to be designated as time series data for ARIMA model.", "type": "string"}, "timeSeriesIdColumn": {"description": "The time series id column that was used during ARIMA model training.", "type": "string"}, "timeSeriesIdColumns": {"description": "The time series id columns that were used during ARIMA model training.", "items": {"type": "string"}, "type": "array"}, "timeSeriesLengthFraction": {"description": "The fraction of the interpolated length of the time series that's used to model the time series trend component. All of the time points of the time series are used to model the non-trend component. This training option accelerates modeling training without sacrificing much forecasting accuracy. You can use this option with `minTimeSeriesLength` but not with `maxTimeSeriesLength`.", "format": "double", "type": "number"}, "timeSeriesTimestampColumn": {"description": "Column to be designated as time series timestamp for ARIMA model.", "type": "string"}, "treeMethod": {"description": "Tree construction algorithm for boosted tree models.", "enum": ["TREE_METHOD_UNSPECIFIED", "AUTO", "EXACT", "APPROX", "HIST"], "enumDescriptions": ["Unspecified tree method.", "Use heuristic to choose the fastest method.", "Exact greedy algorithm.", "Approximate greedy algorithm using quantile sketch and gradient histogram.", "Fast histogram optimized approximate greedy algorithm."], "type": "string"}, "trendSmoothingWindowSize": {"description": "Smoothing window size for the trend component. When a positive value is specified, a center moving average smoothing is applied on the history trend. When the smoothing window is out of the boundary at the beginning or the end of the trend, the first element or the last element is padded to fill the smoothing window before the average is applied.", "format": "int64", "type": "string"}, "userColumn": {"description": "User column specified for matrix factorization models.", "type": "string"}, "vertexAiModelVersionAliases": {"description": "The version aliases to apply in Vertex AI model registry. Always overwrite if the version aliases exists in a existing model.", "items": {"type": "string"}, "type": "array"}, "walsAlpha": {"description": "Hyperparameter for matrix factoration when implicit feedback type is specified.", "format": "double", "type": "number"}, "warmStart": {"description": "Whether to train a model from the last checkpoint.", "type": "boolean"}, "xgboostVersion": {"description": "User-selected XGBoost versions for training of XGBoost models.", "type": "string"}}, "type": "object"}, "TrainingRun": {"description": "Information about a single training query run for the model.", "id": "TrainingRun", "properties": {"classLevelGlobalExplanations": {"description": "Output only. Global explanation contains the explanation of top features on the class level. Applies to classification models only.", "items": {"$ref": "GlobalExplanation"}, "readOnly": true, "type": "array"}, "dataSplitResult": {"$ref": "DataSplitResult", "description": "Output only. Data split result of the training run. Only set when the input data is actually split.", "readOnly": true}, "evaluationMetrics": {"$ref": "EvaluationMetrics", "description": "Output only. The evaluation metrics over training/eval data that were computed at the end of training.", "readOnly": true}, "modelLevelGlobalExplanation": {"$ref": "GlobalExplanation", "description": "Output only. Global explanation contains the explanation of top features on the model level. Applies to both regression and classification models.", "readOnly": true}, "results": {"description": "Output only. Output of each iteration run, results.size() <= max_iterations.", "items": {"$ref": "IterationResult"}, "readOnly": true, "type": "array"}, "startTime": {"description": "Output only. The start time of this training run.", "format": "google-datetime", "readOnly": true, "type": "string"}, "trainingOptions": {"$ref": "TrainingOptions", "description": "Output only. Options that were used for this training run, includes user specified and default options that were used.", "readOnly": true}, "trainingStartTime": {"deprecated": true, "description": "Output only. The start time of this training run, in milliseconds since epoch.", "format": "int64", "readOnly": true, "type": "string"}, "vertexAiModelId": {"description": "The model id in the [Vertex AI Model Registry](https://cloud.google.com/vertex-ai/docs/model-registry/introduction) for this training run.", "type": "string"}, "vertexAiModelVersion": {"description": "Output only. The model version in the [Vertex AI Model Registry](https://cloud.google.com/vertex-ai/docs/model-registry/introduction) for this training run.", "readOnly": true, "type": "string"}}, "type": "object"}, "TransactionInfo": {"description": "[Alpha] Information of a multi-statement transaction.", "id": "TransactionInfo", "properties": {"transactionId": {"description": "Output only. [Alpha] Id of the transaction.", "readOnly": true, "type": "string"}}, "type": "object"}, "TransformColumn": {"description": "Information about a single transform column.", "id": "TransformColumn", "properties": {"name": {"description": "Output only. Name of the column.", "readOnly": true, "type": "string"}, "transformSql": {"description": "Output only. The SQL expression used in the column transform.", "readOnly": true, "type": "string"}, "type": {"$ref": "StandardSqlDataType", "description": "Output only. Data type of the column after the transform.", "readOnly": true}}, "type": "object"}, "UndeleteDatasetRequest": {"description": "Request format for undeleting a dataset.", "id": "UndeleteDatasetRequest", "properties": {"deletionTime": {"description": "Optional. The exact time when the dataset was deleted. If not specified, the most recently deleted version is undeleted. Undeleting a dataset using deletion time is not supported.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "UserDefinedFunctionResource": {"description": " This is used for defining User Defined Function (UDF) resources only when using legacy SQL. Users of GoogleSQL should leverage either DDL (e.g. CREATE [TEMPORARY] FUNCTION ... ) or the Routines API to define UDF resources. For additional information on migrating, see: https://cloud.google.com/bigquery/docs/reference/standard-sql/migrating-from-legacy-sql#differences_in_user-defined_javascript_functions", "id": "UserDefinedFunctionResource", "properties": {"inlineCode": {"description": "[Pick one] An inline resource that contains code for a user-defined function (UDF). Providing a inline code resource is equivalent to providing a URI for a file containing the same code.", "type": "string"}, "resourceUri": {"description": "[Pick one] A code resource to load from a Google Cloud Storage URI (gs://bucket/path).", "type": "string"}}, "type": "object"}, "VectorSearchStatistics": {"description": "Statistics for a vector search query. Populated as part of JobStatistics2.", "id": "VectorSearchStatistics", "properties": {"indexUnusedReasons": {"description": "When `indexUsageMode` is `UNUSED` or `PARTIALLY_USED`, this field explains why indexes were not used in all or part of the vector search query. If `indexUsageMode` is `FULLY_USED`, this field is not populated.", "items": {"$ref": "IndexUnusedReason"}, "type": "array"}, "indexUsageMode": {"description": "Specifies the index usage mode for the query.", "enum": ["INDEX_USAGE_MODE_UNSPECIFIED", "UNUSED", "PARTIALLY_USED", "FULLY_USED"], "enumDescriptions": ["Index usage mode not specified.", "No vector indexes were used in the vector search query. See [`indexUnusedReasons`] (/bigquery/docs/reference/rest/v2/Job#IndexUnusedReason) for detailed reasons.", "Part of the vector search query used vector indexes. See [`indexUnusedReasons`] (/bigquery/docs/reference/rest/v2/Job#IndexUnusedReason) for why other parts of the query did not use vector indexes.", "The entire vector search query used vector indexes."], "type": "string"}, "storedColumnsUsages": {"description": "Specifies the usage of stored columns in the query when stored columns are used in the query.", "items": {"$ref": "StoredColumnsUsage"}, "type": "array"}}, "type": "object"}, "ViewDefinition": {"description": "Describes the definition of a logical view.", "id": "ViewDefinition", "properties": {"foreignDefinitions": {"description": "Optional. Foreign view representations.", "items": {"$ref": "ForeignViewDefinition"}, "type": "array"}, "privacyPolicy": {"$ref": "PrivacyPolicy", "description": "Optional. Specifies the privacy policy for the view."}, "query": {"description": "Required. A query that Big<PERSON><PERSON>y executes when the view is referenced.", "type": "string"}, "useExplicitColumnNames": {"description": "True if the column names are explicitly specified. For example by using the 'CREATE VIEW v(c1, c2) AS ...' syntax. Can only be set for GoogleSQL views.", "type": "boolean"}, "useLegacySql": {"description": "Specifies whether to use BigQuery's legacy SQL for this view. The default value is true. If set to false, the view will use BigQuery's GoogleSQL: https://cloud.google.com/bigquery/sql-reference/ Queries and views that reference this view must use the same flag value. A wrapper is used here because the default value is True.", "type": "boolean"}, "userDefinedFunctionResources": {"description": "Describes user-defined function resources used in the query.", "items": {"$ref": "UserDefinedFunctionResource"}, "type": "array"}}, "type": "object"}}, "servicePath": "bigquery/v2/", "title": "BigQuery API", "version": "v2"}