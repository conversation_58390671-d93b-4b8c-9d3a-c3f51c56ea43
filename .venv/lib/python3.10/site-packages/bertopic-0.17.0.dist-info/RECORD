bertopic-0.17.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
bertopic-0.17.0.dist-info/LICENSE,sha256=y5LNbN1qY2JoadgzySghxtqyxjn8PZz_EhYwm9YFhkk,1102
bertopic-0.17.0.dist-info/METADATA,sha256=xXBcPnhabVBODDF2U_Hpmj1n1kAJ8cKLyuuwsF8Io2U,23896
bertopic-0.17.0.dist-info/RECORD,,
bertopic-0.17.0.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
bertopic-0.17.0.dist-info/WHEEL,sha256=beeZ86-EfXScwlR_HKu4SllMC9wUEj_8Z_4FJ3egI2w,91
bertopic-0.17.0.dist-info/top_level.txt,sha256=7XXm06i3_LJOWcmJuJhp3YFI3CyNWjqCmIUn_QN6H9o,9
bertopic/__init__.py,sha256=JZwqyX-E5xP549AHHsdKHfTSW7Uqz9wHCPjRw7CqTec,155
bertopic/__pycache__/__init__.cpython-310.pyc,,
bertopic/__pycache__/_bertopic.cpython-310.pyc,,
bertopic/__pycache__/_save_utils.cpython-310.pyc,,
bertopic/__pycache__/_utils.cpython-310.pyc,,
bertopic/_bertopic.py,sha256=7NoxsKHM4EeES-VjLIT3OOQzAI9ZJvRQOpgipOpQILI,222365
bertopic/_save_utils.py,sha256=KBQuhSeCZsoA4_SR8t_RsYc9HiLdsrLle7aSVkn07TU,18096
bertopic/_utils.py,sha256=oQdVvou8GCpeYWOrTV8h2_IcnMiq-ZXjMW0PzH6eMUw,8705
bertopic/backend/__init__.py,sha256=nM5l5AqR_ApvN62WtAOlF9OfJcO8GOAkt2Wp1FVEl78,1263
bertopic/backend/__pycache__/__init__.cpython-310.pyc,,
bertopic/backend/__pycache__/_base.cpython-310.pyc,,
bertopic/backend/__pycache__/_cohere.cpython-310.pyc,,
bertopic/backend/__pycache__/_flair.cpython-310.pyc,,
bertopic/backend/__pycache__/_gensim.cpython-310.pyc,,
bertopic/backend/__pycache__/_hftransformers.cpython-310.pyc,,
bertopic/backend/__pycache__/_model2vec.cpython-310.pyc,,
bertopic/backend/__pycache__/_multimodal.cpython-310.pyc,,
bertopic/backend/__pycache__/_openai.cpython-310.pyc,,
bertopic/backend/__pycache__/_sentencetransformers.cpython-310.pyc,,
bertopic/backend/__pycache__/_sklearn.cpython-310.pyc,,
bertopic/backend/__pycache__/_spacy.cpython-310.pyc,,
bertopic/backend/__pycache__/_use.cpython-310.pyc,,
bertopic/backend/__pycache__/_utils.cpython-310.pyc,,
bertopic/backend/__pycache__/_word_doc.cpython-310.pyc,,
bertopic/backend/_base.py,sha256=ZFd7kQ1RyhIvUMHYVv0IX7r90yGXzU_J_MV-EvvbyME,2301
bertopic/backend/_cohere.py,sha256=Wt3dXS0AefGrBoCJeE85Kn0gUmXvBCTV7i52gO6vHJA,3232
bertopic/backend/_flair.py,sha256=HUIoHQk6YklzTmmqHc6kqORo239YuzJWwT8Up5g4a6o,3009
bertopic/backend/_gensim.py,sha256=2-mXlpKDTCcziLZRjYXxRyvCQSrXr5B1Ykaq9PFUBTo,2379
bertopic/backend/_hftransformers.py,sha256=bhg5f5jM4va6GGAiBGPQU4NYU8RJ6BeFm2D4ThRb30c,3621
bertopic/backend/_model2vec.py,sha256=dX1-bexV4GzXvMmxpkFS_rP9_dTN84czWkXGDCv0Ij0,5322
bertopic/backend/_multimodal.py,sha256=m7i_adqSI7fdY2fsKGrPoypwwVE3eH6F0zx59mBkJsM,7953
bertopic/backend/_openai.py,sha256=XRN1SRUJLkcCQWQfrAEUHmnHf9VJnO9ngiaun7Bkq0c,3393
bertopic/backend/_sentencetransformers.py,sha256=lkNI4Z8scEAhavVrCJNdLUpkftMgT4iNfXpYsIOGVes,3350
bertopic/backend/_sklearn.py,sha256=LB1ir8UNpUKrsYy24Eo_QLhAEAvjKA3zn--0lnGdq_c,2547
bertopic/backend/_spacy.py,sha256=-kLSQB18_73E3LR3947fQG5VElUtz34YFGQbrFm_LBw,3147
bertopic/backend/_use.py,sha256=oUiVczP7tpxqN6mby9Coux9jSwdaxVoGLXLNIAOt4yI,1752
bertopic/backend/_utils.py,sha256=Lt5mYmM2aQCop-w9fQAiGtZJZXIPBk2xC0-K80tX0Uk,5400
bertopic/backend/_word_doc.py,sha256=R0qt_SayUcZNQPb9ab-m6U4cmkCiica-4UDN9_FAWaI,1540
bertopic/cluster/__init__.py,sha256=mLqV7-cijgZwyqISyZz5UogUtVU138MAj10wYsAJiDo,70
bertopic/cluster/__pycache__/__init__.cpython-310.pyc,,
bertopic/cluster/__pycache__/_base.cpython-310.pyc,,
bertopic/cluster/__pycache__/_utils.cpython-310.pyc,,
bertopic/cluster/_base.py,sha256=zM9CxO_9cI9_lpbGb2VLXhv-azhfNtOow6-dtxQR-5Q,1073
bertopic/cluster/_utils.py,sha256=Sb5rrfqjZcabNQzqPt-uLP9VExxJgI4BCglvs0q-GkU,2852
bertopic/dimensionality/__init__.py,sha256=rAlZRFM9qYRKNXAzZIJEV1ed3cfvdRQFU78JL7C8e8k,102
bertopic/dimensionality/__pycache__/__init__.cpython-310.pyc,,
bertopic/dimensionality/__pycache__/_base.cpython-310.pyc,,
bertopic/dimensionality/_base.py,sha256=2TkFVmlzLjMzcNSDoOcpLL3eGfoWRSalsZzMyvjdJ4o,671
bertopic/plotting/__init__.py,sha256=5unCz8KaU_NvYeD55O4ExRmC0i8KpTrK0GQcQAHjcvA,1025
bertopic/plotting/__pycache__/__init__.cpython-310.pyc,,
bertopic/plotting/__pycache__/_approximate_distribution.cpython-310.pyc,,
bertopic/plotting/__pycache__/_barchart.cpython-310.pyc,,
bertopic/plotting/__pycache__/_datamap.cpython-310.pyc,,
bertopic/plotting/__pycache__/_distribution.cpython-310.pyc,,
bertopic/plotting/__pycache__/_documents.cpython-310.pyc,,
bertopic/plotting/__pycache__/_heatmap.cpython-310.pyc,,
bertopic/plotting/__pycache__/_hierarchical_documents.cpython-310.pyc,,
bertopic/plotting/__pycache__/_hierarchy.cpython-310.pyc,,
bertopic/plotting/__pycache__/_term_rank.cpython-310.pyc,,
bertopic/plotting/__pycache__/_topics.cpython-310.pyc,,
bertopic/plotting/__pycache__/_topics_over_time.cpython-310.pyc,,
bertopic/plotting/__pycache__/_topics_per_class.cpython-310.pyc,,
bertopic/plotting/_approximate_distribution.py,sha256=qGyxjSnoO-mlNpWhybrYUfRk4LONO5XUEb9_yjxOmn0,3598
bertopic/plotting/_barchart.py,sha256=N0lxX9htwxJoRWw6n4CiyiLm6jgrZeKG5KrwtVHlkYA,4513
bertopic/plotting/_datamap.py,sha256=fRIGKCkDdRAnY24-IIRk6awP5ElD7Ux2_yo0RgYalCw,7747
bertopic/plotting/_distribution.py,sha256=LccWH-TUxNcanYc6w1oxqWJdE5-kAWVkvjnjlqMc9m0,3992
bertopic/plotting/_documents.py,sha256=tVpb6GhCrLRM0hZo7iq1hjVE3QKkTPmfDkZlT32qwkw,9819
bertopic/plotting/_heatmap.py,sha256=Wx-iotzm7U_eK-NR7TP2PEHHtiOFc0rUDcEATLsXcWA,5252
bertopic/plotting/_hierarchical_documents.py,sha256=4Fms6QIGFUmt9437X2tg3cLCEunztIud22x0iuhuwRE,15946
bertopic/plotting/_hierarchy.py,sha256=yeKykQ2oJ2SKppabHe3ylmZxtt3cpiXcJ1YDmNAQ8Sc,14552
bertopic/plotting/_term_rank.py,sha256=mXv8JWDEEFMHhRKyC8cuwS7KMUv8AEBV0brpsPaNYmg,4731
bertopic/plotting/_topics.py,sha256=cnzAkdbx4SDo64cI1FU_2SwcFq7ZtV17oP8QDdT198o,7331
bertopic/plotting/_topics_over_time.py,sha256=CiuxVEZ7VZHRpaIcZfzTOLeUYcl7M1Jvpk3RIb9oa3o,5070
bertopic/plotting/_topics_per_class.py,sha256=376IU5nyUFqmphRS9SmnpjX51ldjjYNtPnNFJb1yunk,5199
bertopic/representation/__init__.py,sha256=-LIq8S9syJcXVx1sEV1d3coXIhVNFd1O1zwfwC-816k,2500
bertopic/representation/__pycache__/__init__.cpython-310.pyc,,
bertopic/representation/__pycache__/_base.cpython-310.pyc,,
bertopic/representation/__pycache__/_cohere.cpython-310.pyc,,
bertopic/representation/__pycache__/_keybert.cpython-310.pyc,,
bertopic/representation/__pycache__/_langchain.cpython-310.pyc,,
bertopic/representation/__pycache__/_litellm.cpython-310.pyc,,
bertopic/representation/__pycache__/_llamacpp.cpython-310.pyc,,
bertopic/representation/__pycache__/_mmr.cpython-310.pyc,,
bertopic/representation/__pycache__/_openai.cpython-310.pyc,,
bertopic/representation/__pycache__/_pos.cpython-310.pyc,,
bertopic/representation/__pycache__/_textgeneration.cpython-310.pyc,,
bertopic/representation/__pycache__/_utils.cpython-310.pyc,,
bertopic/representation/__pycache__/_visual.cpython-310.pyc,,
bertopic/representation/__pycache__/_zeroshot.cpython-310.pyc,,
bertopic/representation/_base.py,sha256=ElRrfGSn5pEfcSORldWoOX5BDQZwmQoTOtFi_rU4zE0,1777
bertopic/representation/_cohere.py,sha256=zqgBquHqBBax-3SGXtBuNbJLhdBjbrM4jnqU4kSIRq8,8740
bertopic/representation/_keybert.py,sha256=_fq3Gvl0MjjvQnrb_VA9ReFIyU2leVpFAr2rahA8vXE,9088
bertopic/representation/_langchain.py,sha256=eCZKRe_RHwo3aBElRkhR6qOU4O-nIgY8i2PR-8AwsW8,9017
bertopic/representation/_litellm.py,sha256=iJgEQxEYQEQfqmnAh6XiyHLfDCyI9Blko0lu1_77-1U,6811
bertopic/representation/_llamacpp.py,sha256=Ulhqai4tkpIHl2jN8seN4fuQAjPDEFNo7wjt6f9K9xw,9629
bertopic/representation/_mmr.py,sha256=0w9yZ3CS8-6Xd1WhFVC7OO2NZhfsD4-Ong52W4GL0NA,4829
bertopic/representation/_openai.py,sha256=GRPpEn6ioBxPLU0mVhok5yUO51qdnfI7kDteLk_YnxA,11295
bertopic/representation/_pos.py,sha256=mF6gqUjvFkNTkN8skE2dAikgFIqTkPIdqW8eXhBcg7M,6115
bertopic/representation/_textgeneration.py,sha256=O6hP0AxVvH_bQX6B6JDwkg91CcyzRge4LDaGybJ9NlU,8154
bertopic/representation/_utils.py,sha256=x1-eqE-eBh-pnyLzcB6E2_ZIkuf6kZaOHfxqUIRqQ1g,4758
bertopic/representation/_visual.py,sha256=dinG8PE1B-dJAc1d-rBeYfaeVrmo3tevTvqpPjI37Jk,10955
bertopic/representation/_zeroshot.py,sha256=AGUU42_kbF7pKMAnyVs7Ykigr8n2WlsP8mA8ZrFlkiY,4206
bertopic/vectorizers/__init__.py,sha256=RsbDhaW3J1jBG6U-7MYZoMfkL6zstxvUbjpzjSBi2oE,155
bertopic/vectorizers/__pycache__/__init__.cpython-310.pyc,,
bertopic/vectorizers/__pycache__/_ctfidf.cpython-310.pyc,,
bertopic/vectorizers/__pycache__/_online_cv.cpython-310.pyc,,
bertopic/vectorizers/_ctfidf.py,sha256=w1EwdgCKRnaD-2hff8i2Q5qZSDQvKQ15Uxi50KoM6Xs,4370
bertopic/vectorizers/_online_cv.py,sha256=ZSTN8Y3o0fIOFesLk9joS7ONieYQk9yfnhKvYx6WkIc,6166
